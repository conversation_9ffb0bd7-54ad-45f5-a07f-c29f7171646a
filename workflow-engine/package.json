{"name": "workflow-engine", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "discover-nodes": "node scripts/discover-nodes.js", "prebuild": "npm run discover-nodes"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@types/sqlite3": "^5.1.0", "@types/uuid": "^10.0.0", "better-sqlite3": "^12.2.0", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "reactflow": "^11.11.4", "redis": "^4.7.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/redis": "^4.0.11", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}