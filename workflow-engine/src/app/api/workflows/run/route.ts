import { NextRequest, NextResponse } from 'next/server';
import { WorkflowManager } from '@/lib/core/WorkflowManager';

// POST /api/workflows/run - 运行工作流
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workflowData, name } = body;

    if (!workflowData) {
      return NextResponse.json(
        { success: false, error: 'Workflow data is required' },
        { status: 400 }
      );
    }

    const manager = WorkflowManager.getInstance();
    await manager.initialize();

    // 创建工作流
    const workflowId = await manager.createWorkflowFromReactFlow(workflowData);

    // 暂时跳过数据库保存，专注于让工作流运行
    let dbId = null;
    console.log(`Workflow name: ${name || 'Unnamed'}`);

    // 运行工作流
    await manager.runWorkflow(workflowId);

    return NextResponse.json({
      success: true,
      data: {
        workflowId,
        dbId,
        status: 'running'
      }
    });

  } catch (error) {
    console.error('Error running workflow:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to run workflow' 
      },
      { status: 500 }
    );
  }
}
