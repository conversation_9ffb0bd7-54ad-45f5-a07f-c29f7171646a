import { NextRequest, NextResponse } from 'next/server';
import { WorkflowManager } from '@/lib/core/WorkflowManager';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflowId');

    if (!workflowId) {
      return NextResponse.json(
        { success: false, error: 'Missing workflowId parameter' },
        { status: 400 }
      );
    }

    console.log('Fetching results for workflow:', workflowId);

    // 获取工作流管理器实例
    const manager = WorkflowManager.getInstance();
    
    // 尝试从活跃的工作流引擎获取结果
    const engine = manager.getWorkflowEngine(workflowId);
    
    if (engine) {
      // 工作流仍在运行，获取当前状态
      const nodes = engine.getNodes();
      const results = nodes.map(node => {
        console.log(`Processing node ${node.id}:`, {
          type: node.type,
          status: node.status,
          hasGetOutputData: typeof node.getOutputData === 'function',
          outputs: node.outputs,
          outputDataMethod: node.getOutputData ? node.getOutputData() : 'NO_METHOD'
        });

        const outputData = node.getOutputData ? node.getOutputData() : null;
        console.log(`Node ${node.id} outputData:`, outputData);

        return {
          nodeId: node.id,
          status: node.status,
          outputData,
          outputs: node.outputs ? Object.fromEntries(
            node.outputs.map(output => [output.id, output.value])
          ) : {}
        };
      });

      console.log('Found active workflow, returning current results:', results);

      return NextResponse.json({
        success: true,
        data: results
      });
    }

    // 工作流已完成，尝试从数据库获取结果
    // 这里我们需要实现数据库查询逻辑
    // 暂时返回空结果，因为我们还没有实现持久化存储
    console.log('Workflow not found in active engines, checking database...');
    
    // TODO: 实现从数据库获取工作流结果的逻辑
    // const dbResults = await getWorkflowResultsFromDatabase(workflowId);
    
    // 暂时返回空结果
    console.log('No results found for workflow:', workflowId);
    return NextResponse.json({
      success: true,
      data: []
    });

  } catch (error) {
    console.error('Error fetching workflow results:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
