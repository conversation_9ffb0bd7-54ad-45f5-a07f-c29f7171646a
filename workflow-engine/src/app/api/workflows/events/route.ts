import { NextRequest } from 'next/server';
import { MessageQueue } from '@/lib/core/MessageQueue';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const workflowId = searchParams.get('workflowId');

  if (!workflowId) {
    return new Response('Missing workflowId parameter', { status: 400 });
  }

  console.log('Setting up event stream for workflow:', workflowId);

  const stream = new ReadableStream({
    async start(controller) {
      const messageQueue = MessageQueue.getInstance();
      let unsubscribe: (() => void) | null = null;

      try {
        // 确保消息队列已连接
        await messageQueue.connect();

        // 订阅工作流事件（包括历史事件）
        unsubscribe = await messageQueue.subscribeToWorkflow(
          workflowId,
          (event) => {
            try {
              const message = `data: ${JSON.stringify(event)}\n\n`;
              controller.enqueue(new TextEncoder().encode(message));
              
              console.log(`Sent event to client: ${event.type} for workflow ${workflowId}`);
              
              // 如果是工作流完成事件，延迟关闭连接
              if (event.type === 'workflowCompleted') {
                setTimeout(() => {
                  try {
                    if (unsubscribe) unsubscribe();
                    controller.close();
                  } catch (error) {
                    // 忽略关闭错误
                  }
                }, 1000);
              }
            } catch (error) {
              console.error('Error sending event to client:', error);
            }
          },
          true // 包括历史事件
        );

        console.log('Subscribed to workflow events via MessageQueue:', workflowId);

        // 发送初始连接确认
        const initEvent = {
          id: `init_${Date.now()}`,
          type: 'connected',
          workflowId,
          timestamp: new Date().toISOString(),
          data: { message: 'Connected to event stream' }
        };
        
        const initMessage = `data: ${JSON.stringify(initEvent)}\n\n`;
        controller.enqueue(new TextEncoder().encode(initMessage));

      } catch (error) {
        console.error('Error setting up event stream:', error);
        
        // 发送错误事件
        const errorEvent = {
          id: `error_${Date.now()}`,
          type: 'error',
          workflowId,
          timestamp: new Date().toISOString(),
          data: { error: 'Failed to setup event stream' }
        };
        
        const errorMessage = `data: ${JSON.stringify(errorEvent)}\n\n`;
        controller.enqueue(new TextEncoder().encode(errorMessage));
        
        // 关闭连接
        setTimeout(() => {
          try {
            controller.close();
          } catch (closeError) {
            // 忽略关闭错误
          }
        }, 100);
      }

      // 当连接关闭时清理
      request.signal.addEventListener('abort', () => {
        console.log('Client disconnected, cleaning up subscription for:', workflowId);
        if (unsubscribe) {
          unsubscribe();
        }
      });
    },
    
    cancel() {
      console.log('Event stream cancelled for workflow:', workflowId);
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  });
}
