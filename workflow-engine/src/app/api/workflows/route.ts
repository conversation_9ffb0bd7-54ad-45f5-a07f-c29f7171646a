import { NextRequest, NextResponse } from 'next/server';
import { WorkflowDatabase } from '@/lib/database/Database';

// GET /api/workflows - 获取所有工作流
export async function GET() {
  try {
    const db = WorkflowDatabase.getInstance();
    const workflows = db.getAllWorkflows();
    
    return NextResponse.json({
      success: true,
      data: workflows
    });
  } catch (error) {
    console.error('Error fetching workflows:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflows' },
      { status: 500 }
    );
  }
}

// POST /api/workflows - 创建新工作流
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, data } = body;

    if (!name || !data) {
      return NextResponse.json(
        { success: false, error: 'Name and data are required' },
        { status: 400 }
      );
    }

    const db = WorkflowDatabase.getInstance();
    const workflowId = db.createWorkflow(name, description || '', data);
    
    // 记录创建日志
    db.logExecution(workflowId, null, 'workflow_created', { name, description });

    return NextResponse.json({
      success: true,
      data: { id: workflowId }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating workflow:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create workflow' },
      { status: 500 }
    );
  }
}
