import { NextRequest, NextResponse } from 'next/server';
import { WorkflowManager } from '@/lib/core/WorkflowManager';

// POST /api/workflows/control - 控制工作流执行
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { workflowId, action, data } = body;

    if (!workflowId || !action) {
      return NextResponse.json(
        { success: false, error: 'Workflow ID and action are required' },
        { status: 400 }
      );
    }

    const manager = WorkflowManager.getInstance();

    switch (action) {
      case 'stop':
        manager.stopWorkflow(workflowId);
        break;
        
      case 'pause':
        manager.pauseWorkflow(workflowId);
        break;
        
      case 'resume':
        await manager.resumeWorkflow(workflowId);
        break;
        
      case 'userInteraction':
        if (!data || !data.nodeId) {
          return NextResponse.json(
            { success: false, error: 'Node ID is required for user interaction' },
            { status: 400 }
          );
        }
        await manager.handleUserInteraction(workflowId, data.nodeId, data.interactionData);
        break;
        
      case 'updateNodeProperty':
        if (!data || !data.nodeId || !data.property) {
          return NextResponse.json(
            { success: false, error: 'Node ID and property are required' },
            { status: 400 }
          );
        }
        manager.updateNodeProperty(workflowId, data.nodeId, data.property, data.value);
        break;
        
      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

    // 获取更新后的状态
    const status = manager.getWorkflowStatus(workflowId);

    return NextResponse.json({
      success: true,
      data: {
        workflowId,
        action,
        status
      }
    });

  } catch (error) {
    console.error('Error controlling workflow:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to control workflow' 
      },
      { status: 500 }
    );
  }
}

// GET /api/workflows/control - 获取工作流状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflowId');

    if (!workflowId) {
      // 返回所有活动工作流
      const manager = WorkflowManager.getInstance();
      const activeWorkflows = manager.getActiveWorkflows();
      
      return NextResponse.json({
        success: true,
        data: {
          activeWorkflows,
          count: activeWorkflows.length
        }
      });
    }

    // 返回特定工作流状态
    const manager = WorkflowManager.getInstance();
    const status = manager.getWorkflowStatus(workflowId);

    if (!status) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        workflowId,
        ...status
      }
    });

  } catch (error) {
    console.error('Error getting workflow status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to get workflow status' 
      },
      { status: 500 }
    );
  }
}
