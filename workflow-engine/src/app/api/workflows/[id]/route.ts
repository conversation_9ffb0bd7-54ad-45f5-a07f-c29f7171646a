import { NextRequest, NextResponse } from 'next/server';
import { WorkflowDatabase } from '@/lib/database/Database';

// GET /api/workflows/[id] - 获取特定工作流
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const workflowId = parseInt(params.id);
    
    if (isNaN(workflowId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid workflow ID' },
        { status: 400 }
      );
    }

    const db = WorkflowDatabase.getInstance();
    const workflow = db.getWorkflow(workflowId);
    
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // 获取节点配置
    const nodeConfigs = db.getWorkflowNodeConfigs(workflowId);

    return NextResponse.json({
      success: true,
      data: {
        ...workflow,
        data: JSON.parse(workflow.data),
        nodeConfigs
      }
    });
  } catch (error) {
    console.error('Error fetching workflow:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch workflow' },
      { status: 500 }
    );
  }
}

// PUT /api/workflows/[id] - 更新工作流
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const workflowId = parseInt(params.id);
    
    if (isNaN(workflowId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid workflow ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, description, data } = body;

    const db = WorkflowDatabase.getInstance();
    
    // 检查工作流是否存在
    const existingWorkflow = db.getWorkflow(workflowId);
    if (!existingWorkflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // 更新工作流
    const updated = db.updateWorkflow(workflowId, name, description, data);
    
    if (!updated) {
      return NextResponse.json(
        { success: false, error: 'No changes made' },
        { status: 400 }
      );
    }

    // 记录更新日志
    db.logExecution(workflowId, null, 'workflow_updated', { name, description });

    return NextResponse.json({
      success: true,
      message: 'Workflow updated successfully'
    });
  } catch (error) {
    console.error('Error updating workflow:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update workflow' },
      { status: 500 }
    );
  }
}

// DELETE /api/workflows/[id] - 删除工作流
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const workflowId = parseInt(params.id);
    
    if (isNaN(workflowId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid workflow ID' },
        { status: 400 }
      );
    }

    const db = WorkflowDatabase.getInstance();
    
    // 检查工作流是否存在
    const existingWorkflow = db.getWorkflow(workflowId);
    if (!existingWorkflow) {
      return NextResponse.json(
        { success: false, error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // 记录删除日志
    db.logExecution(workflowId, null, 'workflow_deleted', { name: existingWorkflow.name });

    // 删除工作流
    const deleted = db.deleteWorkflow(workflowId);
    
    if (!deleted) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete workflow' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Workflow deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting workflow:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete workflow' },
      { status: 500 }
    );
  }
}
