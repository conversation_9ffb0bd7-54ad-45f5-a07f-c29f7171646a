import { NextRequest, NextResponse } from 'next/server';
import { NodeFactory } from '@/lib/core/NodeFactory';
import { NodeRegistry } from '@/lib/core/NodeRegistry';

// GET /api/nodes - 获取所有可用的节点类型
export async function GET() {
  try {
    // 确保节点注册表已初始化
    await NodeRegistry.initialize();

    const nodeFactory = NodeFactory.getInstance();
    const registeredNodes = nodeFactory.getRegisteredNodes();

    return NextResponse.json({
      success: true,
      data: registeredNodes
    });
  } catch (error) {
    console.error('Error fetching node types:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch node types' },
      { status: 500 }
    );
  }
}

// POST /api/nodes/validate - 验证节点配置
export async function POST(request: NextRequest) {
  try {
    // 确保节点注册表已初始化
    await NodeRegistry.initialize();

    const body = await request.json();
    const { type, config } = body;

    if (!type) {
      return NextResponse.json(
        { success: false, error: 'Node type is required' },
        { status: 400 }
      );
    }

    const nodeFactory = NodeFactory.getInstance();
    const validation = nodeFactory.validateNodeConfig(type, config);

    return NextResponse.json({
      success: true,
      data: validation
    });
  } catch (error) {
    console.error('Error validating node config:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to validate node config' },
      { status: 500 }
    );
  }
}
