@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

#__next {
  height: 100%;
  width: 100%;
}

/* React Flow 连接点样式优化 */
.react-flow__handle {
  width: 16px !important;
  height: 16px !important;
  border: 3px solid white !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
  z-index: 10 !important;
}

.react-flow__handle:hover {
  transform: scale(1.2) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
}

/* 输入连接点（左侧） */
.react-flow__handle-left {
  background: #3b82f6 !important; /* 蓝色 */
  left: -8px !important;
}

/* 输出连接点（右侧） */
.react-flow__handle-right {
  background: #10b981 !important; /* 绿色 */
  right: -8px !important;
}

/* 顶部和底部连接点 */
.react-flow__handle-top {
  background: #8b5cf6 !important; /* 紫色 */
  top: -8px !important;
}

.react-flow__handle-bottom {
  background: #8b5cf6 !important; /* 紫色 */
  bottom: -8px !important;
}

/* 连接点在连接时的样式 */
.react-flow__handle.connecting {
  background: #f59e0b !important; /* 橙色 - 连接时的颜色 */
  transform: scale(1.3) !important;
}

/* 连接点在有效连接目标时的样式 */
.react-flow__handle.valid {
  background: #10b981 !important; /* 绿色 - 有效连接目标 */
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.3) !important;
}

/* 连接点在无效连接目标时的样式 */
.react-flow__handle.invalid {
  background: #ef4444 !important; /* 红色 - 无效连接目标 */
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.3) !important;
}
