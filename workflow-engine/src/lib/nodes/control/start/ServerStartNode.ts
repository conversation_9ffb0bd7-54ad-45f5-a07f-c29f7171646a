/**
 * 开始节点 - 服务器端实现
 * 
 * 这个文件实现开始节点的服务器端逻辑，包括：
 * - 继承共享的节点信息和配置
 * - 实现服务器端特有的执行逻辑
 * - 处理延迟和自动开始功能
 * - 提供服务器端的状态管理
 */

import { BaseNode, NodeData, ExecutionMode } from '../../BaseNode';
import { NodeInfo } from '../../SelfContainedNode';
import {
  START_NODE_INFO,
  START_NODE_INPUTS,
  START_NODE_OUTPUTS,
  START_NODE_DEFAULT_CONFIG,
  StartNodeConfig,
  StartNodeResult,
  validateStartNodeConfig,
  generateStartNodeResult,
  formatStartNodeStatus,
  StartNodeUtils,
  START_NODE_CONSTANTS,
  StartNodeError,
  StartNodeErrorType
} from './StartNodeShared';

/**
 * 开始节点服务器端实现
 */
export class ServerStartNode extends BaseNode {
  public config: StartNodeConfig;
  private executionId?: string;
  private delayTimer?: NodeJS.Timeout;

  constructor(id: string, config: any = {}) {
    super(START_NODE_INFO.type, START_NODE_INFO.name, id);

    // 合并配置
    this.config = {
      ...START_NODE_DEFAULT_CONFIG,
      ...config
    };

    // 验证配置
    const validation = validateStartNodeConfig(this.config);
    if (!validation.valid) {
      throw new StartNodeError(
        StartNodeErrorType.INVALID_CONFIG,
        `开始节点配置无效: ${validation.errors.join(', ')}`,
        validation
      );
    }

    // 设置输入输出引脚
    this.inputs = [...START_NODE_INPUTS];
    this.outputs = [...START_NODE_OUTPUTS];

    // 设置执行模式
    this.executionMode = ExecutionMode.FRONTEND_ONLY;

    // 调用初始化
    this.initialize();

    console.log(`✅ ServerStartNode created with config:`, this.config);
  }

  /**
   * 初始化方法（BaseNode 要求）
   */
  initialize(): void {
    // 初始化已在构造函数中完成
  }

  /**
   * 处理方法（BaseNode 要求）
   */
  async process(inputData: NodeData): Promise<NodeData> {
    return await this.executeFrontend();
  }

  /**
   * 渲染UI（BaseNode 要求，服务器端返回空字符串）
   */
  renderUI(): string {
    return '';
  }

  /**
   * 渲染图标（BaseNode 要求，服务器端返回空字符串）
   */
  renderIcon(): string {
    return '';
  }

  /**
   * 获取属性面板（BaseNode 要求，服务器端返回空字符串）
   */
  getPropertyPanel(): string {
    return '';
  }

  /**
   * 判断是否需要后端处理（BaseNode 要求）
   */
  shouldUseBackend(inputData: NodeData): boolean {
    return false; // 开始节点只需要前端处理
  }

  /**
   * 前端处理（BaseNode 要求）
   */
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    return await this.executeFrontend();
  }

  /**
   * 后端处理（BaseNode 要求）
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    return {}; // 开始节点不需要后端处理
  }

  /**
   * 获取服务器端节点信息（静态方法，用于自动发现）
   */
  static getServerNodeInfo(): NodeInfo {
    return START_NODE_INFO;
  }

  /**
   * 创建服务器端节点实例（静态方法，用于自动发现）
   */
  static createServerInstance(id: string, config: any): ServerStartNode {
    return new ServerStartNode(id, config);
  }

  /**
   * 前端执行逻辑
   */
  async executeFrontend(): Promise<any> {
    try {
      this.executionId = StartNodeUtils.generateExecutionId();
      
      console.log(`ℹ️ [${this.type}:${this.id}] 开始节点前端处理开始`, {
        executionId: this.executionId,
        delay: this.config.delay,
        autoStart: this.config.autoStart
      });

      // 如果有延迟，先等待
      if (this.config.delay > 0) {
        await this.handleDelay();
      }

      // 生成执行结果
      const result = generateStartNodeResult(this.config);
      
      console.log(`ℹ️ [${this.type}:${this.id}] 开始节点前端处理完成`, {
        executionId: this.executionId,
        result
      });

      return result;

    } catch (error) {
      console.error(`❌ [${this.type}:${this.id}] 开始节点前端执行失败:`, error);
      throw new StartNodeError(
        StartNodeErrorType.UNKNOWN_ERROR,
        `开始节点执行失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error, executionId: this.executionId }
      );
    }
  }

  /**
   * 后端执行逻辑（开始节点通常不需要后端处理）
   */
  async executeBackend(): Promise<any> {
    console.log(`ℹ️ [${this.type}:${this.id}] 开始节点后端处理（通常为空）`);
    return {};
  }

  /**
   * 处理延迟逻辑
   */
  private async handleDelay(): Promise<void> {
    const delayMs = StartNodeUtils.getDelayMs(this.config);
    
    if (delayMs <= 0) {
      return;
    }

    console.log(`⏱️ [${this.type}:${this.id}] 延迟 ${this.config.delay} 秒开始执行`);

    return new Promise((resolve, reject) => {
      this.delayTimer = setTimeout(() => {
        console.log(`✅ [${this.type}:${this.id}] 延迟完成，开始执行`);
        resolve();
      }, delayMs);

      // 设置超时保护
      setTimeout(() => {
        if (this.delayTimer) {
          clearTimeout(this.delayTimer);
          reject(new StartNodeError(
            StartNodeErrorType.EXECUTION_TIMEOUT,
            `延迟执行超时 (${this.config.delay}秒)`
          ));
        }
      }, delayMs + START_NODE_CONSTANTS.DEFAULT_TIMEOUT);
    });
  }

  /**
   * 获取当前配置
   */
  getConfig(): StartNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<StartNodeConfig>): void {
    const mergedConfig = { ...this.config, ...newConfig };
    
    // 验证新配置
    const validation = validateStartNodeConfig(mergedConfig);
    if (!validation.valid) {
      throw new StartNodeError(
        StartNodeErrorType.INVALID_CONFIG,
        `配置更新失败: ${validation.errors.join(', ')}`,
        validation
      );
    }

    this.config = mergedConfig;
    console.log(`🔧 [${this.type}:${this.id}] 配置已更新:`, this.config);
  }

  /**
   * 获取格式化的状态信息
   */
  getFormattedStatus(): string {
    return formatStartNodeStatus(this.status as any, this.config);
  }

  /**
   * 检查是否应该自动开始
   */
  shouldAutoStart(): boolean {
    return StartNodeUtils.shouldAutoStart(this.config);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.delayTimer) {
      clearTimeout(this.delayTimer);
      this.delayTimer = undefined;
    }
    
    console.log(`🧹 [${this.type}:${this.id}] 资源已清理`);
  }

  /**
   * 获取节点统计信息
   */
  getStats(): any {
    return {
      nodeType: this.type,
      executionMode: this.executionMode,
      config: this.config,
      executionId: this.executionId,
      hasDelayTimer: !!this.delayTimer,
      shouldAutoStart: this.shouldAutoStart(),
      formattedStatus: this.getFormattedStatus()
    };
  }

  /**
   * 序列化节点状态
   */
  serialize(): any {
    return {
      ...super.serialize(),
      config: this.config,
      executionId: this.executionId,
      nodeInfo: START_NODE_INFO
    };
  }

  /**
   * 反序列化节点状态
   */
  static deserialize(data: any): ServerStartNode {
    const node = new ServerStartNode(data.id, data.config);
    node.status = data.status || 'idle';
    node.executionId = data.executionId;
    return node;
  }
}

// 导出类型和常量供其他模块使用
export type {
  StartNodeConfig,
  StartNodeResult
};

export {
  StartNodeError,
  StartNodeErrorType,
  START_NODE_INFO,
  START_NODE_CONSTANTS
};
