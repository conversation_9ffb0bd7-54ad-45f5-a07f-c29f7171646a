/**
 * 开始节点共享代码
 * 
 * 这个文件包含开始节点的所有共享逻辑，包括：
 * - 节点信息定义
 * - 输入输出引脚定义
 * - 配置选项定义
 * - 验证逻辑
 * - 工具函数
 * 
 * 客户端和服务器端实现都会继承这些共享代码，避免重复。
 */

import { NodeInfo } from '../../SelfContainedNode';
import { Pin } from '../../BaseNode';

/**
 * 开始节点的基本信息
 */
export const START_NODE_INFO: NodeInfo = {
  type: 'react-start',
  name: '开始节点',
  description: '工作流的起始点，触发整个工作流的执行',
  category: 'control',
  version: '2.0.0',
  author: 'Workflow Engine',
  tags: ['start', 'trigger', 'control'],
  icon: 'play',
  defaultConfig: {},
  pins: {
    inputs: [],
    outputs: []
  }
};

/**
 * 开始节点的输入引脚（开始节点没有输入）
 */
export const START_NODE_INPUTS: Pin[] = [];

/**
 * 开始节点的输出引脚
 */
export const START_NODE_OUTPUTS: Pin[] = [
  {
    id: 'trigger',
    type: 'output',
    name: 'trigger',
    dataType: 'any',
    description: '触发信号',
    required: false
  },
  {
    id: 'timestamp',
    type: 'output', 
    name: 'timestamp',
    dataType: 'string',
    description: '开始时间戳',
    required: false
  }
];

/**
 * 开始节点的配置选项
 */
export interface StartNodeConfig {
  /** 是否自动开始 */
  autoStart: boolean;
  /** 延迟时间（秒） */
  delay: number;
  /** 自定义标签 */
  label?: string;
  /** 自定义描述 */
  description?: string;
}

/**
 * 开始节点的默认配置
 */
export const START_NODE_DEFAULT_CONFIG: StartNodeConfig = {
  autoStart: false,
  delay: 0,
  label: START_NODE_INFO.name,
  description: START_NODE_INFO.description
};

/**
 * 开始节点的执行结果
 */
export interface StartNodeResult {
  /** 触发信号 */
  trigger: boolean;
  /** 开始时间戳 */
  timestamp: string;
}

/**
 * 验证开始节点配置
 */
export function validateStartNodeConfig(config: Partial<StartNodeConfig>): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证延迟时间
  if (config.delay !== undefined) {
    if (typeof config.delay !== 'number') {
      errors.push('延迟时间必须是数字');
    } else if (config.delay < 0) {
      errors.push('延迟时间不能为负数');
    } else if (config.delay > 3600) {
      warnings.push('延迟时间超过1小时，请确认是否正确');
    }
  }

  // 验证自动开始
  if (config.autoStart !== undefined && typeof config.autoStart !== 'boolean') {
    errors.push('自动开始必须是布尔值');
  }

  // 验证标签长度
  if (config.label && config.label.length > 50) {
    warnings.push('标签长度超过50个字符，可能影响显示');
  }

  // 验证描述长度
  if (config.description && config.description.length > 200) {
    warnings.push('描述长度超过200个字符，可能影响显示');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 生成开始节点的执行结果
 */
export function generateStartNodeResult(config: StartNodeConfig): StartNodeResult {
  return {
    trigger: true,
    timestamp: new Date().toISOString()
  };
}

/**
 * 格式化开始节点的状态信息
 */
export function formatStartNodeStatus(
  status: 'idle' | 'running' | 'completed' | 'error',
  config: StartNodeConfig,
  result?: StartNodeResult
): string {
  switch (status) {
    case 'idle':
      return config.autoStart ? '准备自动开始' : '等待手动触发';
    case 'running':
      return config.delay > 0 ? `延迟 ${config.delay} 秒后开始` : '正在启动';
    case 'completed':
      return result ? `已完成 (${result.timestamp})` : '已完成';
    case 'error':
      return '启动失败';
    default:
      return '未知状态';
  }
}

/**
 * 开始节点的工具函数
 */
export class StartNodeUtils {
  /**
   * 检查是否应该自动开始
   */
  static shouldAutoStart(config: StartNodeConfig): boolean {
    return config.autoStart === true;
  }

  /**
   * 计算实际延迟时间（毫秒）
   */
  static getDelayMs(config: StartNodeConfig): number {
    return Math.max(0, config.delay) * 1000;
  }

  /**
   * 生成唯一的执行ID
   */
  static generateExecutionId(): string {
    return `start_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 格式化时间戳为可读格式
   */
  static formatTimestamp(timestamp: string): string {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      return timestamp;
    }
  }

  /**
   * 创建延迟Promise
   */
  static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 开始节点的常量定义
 */
export const START_NODE_CONSTANTS = {
  /** 最大延迟时间（秒） */
  MAX_DELAY: 3600,
  /** 最小延迟时间（秒） */
  MIN_DELAY: 0,
  /** 默认超时时间（毫秒） */
  DEFAULT_TIMEOUT: 30000,
  /** 状态检查间隔（毫秒） */
  STATUS_CHECK_INTERVAL: 1000
} as const;

/**
 * 开始节点的错误类型
 */
export enum StartNodeErrorType {
  INVALID_CONFIG = 'INVALID_CONFIG',
  EXECUTION_TIMEOUT = 'EXECUTION_TIMEOUT',
  DELAY_ERROR = 'DELAY_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 开始节点的错误类
 */
export class StartNodeError extends Error {
  constructor(
    public type: StartNodeErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'StartNodeError';
  }
}
