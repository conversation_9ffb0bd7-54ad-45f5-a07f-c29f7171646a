/**
 * 开始节点 - 客户端实现
 * 
 * 这个文件实现开始节点的客户端逻辑，包括：
 * - 继承 ReactBaseNode 基类
 * - 实现React组件的UI渲染
 * - 处理用户交互和配置更新
 * - 使用共享代码避免重复
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Play, Clock, Settings, CheckCircle, AlertCircle } from 'lucide-react';
import { ReactBaseNode } from '../../ReactBaseNode';
import { NodeData } from '../../BaseNode';
import { BaseNodePropertyPanel } from '../../../../components/BaseNodePropertyPanel';
import {
  START_NODE_INFO,
  START_NODE_INPUTS,
  START_NODE_OUTPUTS,
  START_NODE_DEFAULT_CONFIG,
  StartNodeConfig,
  StartNodeResult,
  validateStartNodeConfig,
  formatStartNodeStatus,
  StartNodeUtils,
  START_NODE_CONSTANTS
} from './StartNodeShared';

/**
 * 开始节点客户端实现
 */
export class ReactStartNode extends ReactBaseNode {
  public config: StartNodeConfig;

  constructor(type: string, name?: string) {
    super(type, name || START_NODE_INFO.name);
    
    // 合并配置
    this.config = {
      ...START_NODE_DEFAULT_CONFIG
    };

    // 设置输入输出引脚
    this.inputs = [...START_NODE_INPUTS];
    this.outputs = [...START_NODE_OUTPUTS];

    // 调用初始化
    this.initialize();

    console.log(`✅ ReactStartNode created with config:`, this.config);
  }

  /**
   * 初始化方法（BaseNode 要求）
   */
  initialize(): void {
    // 初始化已在构造函数中完成
  }

  /**
   * 前端处理逻辑
   */
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    try {
      console.log(`ℹ️ [${this.type}:${this.id}] 开始节点前端处理开始`);

      // 如果有延迟，先等待
      if (this.config.delay > 0) {
        await StartNodeUtils.delay(StartNodeUtils.getDelayMs(this.config));
      }

      // 生成执行结果
      const result = {
        trigger: true,
        timestamp: new Date().toISOString()
      };
      
      console.log(`ℹ️ [${this.type}:${this.id}] 开始节点前端处理完成`, result);
      return result;

    } catch (error) {
      console.error(`❌ [${this.type}:${this.id}] 开始节点前端执行失败:`, error);
      throw error;
    }
  }

  /**
   * 后端处理逻辑（开始节点通常不需要后端处理）
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    console.log(`ℹ️ [${this.type}:${this.id}] 开始节点后端处理（通常为空）`);
    return {};
  }

  /**
   * 判断是否需要后端处理
   */
  shouldUseBackend(inputData: NodeData): boolean {
    return false; // 开始节点只需要前端处理
  }

  /**
   * 渲染节点UI组件
   */
  renderUIComponent(): React.ReactElement {
    return <StartNodeUI node={this} />;
  }

  /**
   * 渲染节点图标组件
   */
  renderIconComponent(): React.ReactElement {
    return <StartNodeIcon node={this} />;
  }

  /**
   * 渲染属性面板组件
   */
  getPropertyPanelComponent(): React.ReactElement {
    return <StartNodePropertyPanel node={this} />;
  }

  /**
   * 获取当前配置
   */
  getConfig(): StartNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<StartNodeConfig>): void {
    const mergedConfig = { ...this.config, ...newConfig };
    
    // 验证新配置
    const validation = validateStartNodeConfig(mergedConfig);
    if (!validation.valid) {
      console.error('配置更新失败:', validation.errors);
      return;
    }

    this.config = mergedConfig;
    this.emit('configChanged', this.config);
    console.log(`🔧 [${this.type}:${this.id}] 配置已更新:`, this.config);
  }

  /**
   * 获取格式化的状态信息
   */
  getFormattedStatus(): string {
    return formatStartNodeStatus(this.status as any, this.config);
  }

  /**
   * 检查是否应该自动开始
   */
  shouldAutoStart(): boolean {
    return StartNodeUtils.shouldAutoStart(this.config);
  }
}

/**
 * 开始节点UI组件
 */
interface StartNodeUIProps {
  node: ReactStartNode;
}

const StartNodeUI: React.FC<StartNodeUIProps> = ({ node }) => {
  const [status, setStatus] = useState(node.status);
  const [config, setConfig] = useState(node.getConfig());

  useEffect(() => {
    const handleStatusChange = () => {
      setStatus(node.status);
    };

    const handleConfigChange = () => {
      setConfig(node.getConfig());
    };

    node.on('statusChanged', handleStatusChange);
    node.on('configChanged', handleConfigChange);

    return () => {
      node.off('statusChanged', handleStatusChange);
      node.off('configChanged', handleConfigChange);
    };
  }, [node]);

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <Clock size={14} className="text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'error':
        return <AlertCircle size={14} className="text-red-500" />;
      default:
        return <Play size={14} className="text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'completed':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  return (
    <div className={`p-3 rounded-lg border-2 ${getStatusColor()} min-w-[120px]`}>
      <div className="flex items-center gap-2 mb-2">
        {getStatusIcon()}
        <span className="font-medium text-sm">{config.label || START_NODE_INFO.name}</span>
      </div>
      
      <div className="text-xs text-gray-600 mb-2">
        {node.getFormattedStatus()}
      </div>

      {config.delay > 0 && (
        <div className="flex items-center gap-1 text-xs text-blue-600">
          <Clock size={12} />
          <span>延迟 {config.delay}s</span>
        </div>
      )}

      {config.autoStart && (
        <div className="text-xs text-green-600 mt-1">
          自动开始
        </div>
      )}
    </div>
  );
};

/**
 * 开始节点图标组件
 */
interface StartNodeIconProps {
  nodeId?: string;
  status?: string;
  isRunning?: boolean;
  isCompleted?: boolean;
  isError?: boolean;
  isWaiting?: boolean;
  node?: ReactStartNode;
}

const StartNodeIcon: React.FC<StartNodeIconProps> = ({
  nodeId,
  status: propStatus,
  isRunning = false,
  isCompleted = false,
  isError = false,
  isWaiting = false,
  node
}) => {
  const [nodeStatus, setNodeStatus] = useState(propStatus || (node ? node.status : 'idle'));

  useEffect(() => {
    if (node) {
      const handleStatusChange = () => {
        setNodeStatus(node.status);
      };

      node.on('statusChanged', handleStatusChange);

      return () => {
        node.off('statusChanged', handleStatusChange);
      };
    }
  }, [node]);

  // 使用传入的状态或节点状态
  const currentStatus = propStatus || nodeStatus;
  const currentIsRunning = isRunning || currentStatus === 'running';
  const currentIsCompleted = isCompleted || currentStatus === 'completed';
  const currentIsError = isError || currentStatus === 'error';

  const getGradientColors = () => {
    if (currentIsError) {
      return 'from-red-400 to-red-600';
    } else if (currentIsCompleted) {
      return 'from-green-400 to-emerald-600';
    } else if (currentIsRunning) {
      return 'from-blue-400 to-cyan-600';
    } else {
      return 'from-gray-400 to-gray-600';
    }
  };

  const getAnimationClass = () => {
    if (currentIsRunning) {
      return 'animate-pulse';
    } else if (currentIsCompleted) {
      return 'animate-bounce';
    } else {
      return '';
    }
  };

  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br ${getGradientColors()} rounded-full
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${getAnimationClass()}
    `}>
      <Play size={16} />
    </div>
  );
};

/**
 * 开始节点属性面板组件
 */
interface StartNodePropertyPanelProps {
  node: ReactStartNode;
}

const StartNodePropertyPanel: React.FC<StartNodePropertyPanelProps> = ({ node }) => {
  const [config, setConfig] = useState(node.getConfig());
  const [validation, setValidation] = useState(validateStartNodeConfig(config));
  const props = node.getPropertyProps();

  const handleConfigChange = useCallback((key: keyof StartNodeConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);

    const newValidation = validateStartNodeConfig(newConfig);
    setValidation(newValidation);

    if (newValidation.valid) {
      node.updateConfig(newConfig);
    }
  }, [config, node]);

  return (
    <BaseNodePropertyPanel
      nodeId={String(props.nodeId || '')}
      nodeType={String(props.type || '')}
      name={String(props.name || '')}
      description={String(props.description || '')}
      status={String(props.status || '')}
      onNameChange={props.onNameChange}
      onDescriptionChange={props.onDescriptionChange}
      title="开始节点配置"
    >
      {/* 自动开始 */}
      <div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="autoStart"
            checked={config.autoStart}
            onChange={(e) => handleConfigChange('autoStart', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="autoStart" className="text-sm font-medium">自动开始</label>
        </div>
        <p className="text-xs text-gray-500 mt-1">勾选后，工作流加载时自动开始执行</p>
      </div>

      {/* 延迟时间 */}
      <div>
        <label className="block text-sm font-medium mb-1">延迟时间 (秒)</label>
        <input
          type="number"
          min={START_NODE_CONSTANTS.MIN_DELAY}
          max={START_NODE_CONSTANTS.MAX_DELAY}
          value={config.delay}
          onChange={(e) => handleConfigChange('delay', parseInt(e.target.value) || 0)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
        />
        <p className="text-xs text-gray-500 mt-1">开始执行前的延迟时间</p>
      </div>

      {/* 验证错误 */}
      {validation.errors.length > 0 && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <h4 className="text-sm font-medium text-red-800 mb-1">配置错误</h4>
          <ul className="text-xs text-red-600 space-y-1">
            {validation.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* 验证警告 */}
      {validation.warnings.length > 0 && (
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <h4 className="text-sm font-medium text-yellow-800 mb-1">注意事项</h4>
          <ul className="text-xs text-yellow-600 space-y-1">
            {validation.warnings.map((warning, index) => (
              <li key={index}>• {warning}</li>
            ))}
          </ul>
        </div>
      )}
    </BaseNodePropertyPanel>
  );
};

// 导出组件供外部使用
export { StartNodeUI, StartNodeIcon, StartNodePropertyPanel };

// 导出节点信息（ClientNodeRegistry 需要）
export const nodeInfo = {
  type: START_NODE_INFO.type,
  name: START_NODE_INFO.name,
  description: START_NODE_INFO.description,
  category: '控制',
  icon: 'play',
  version: START_NODE_INFO.version,
  author: START_NODE_INFO.author,
  tags: START_NODE_INFO.tags
};
