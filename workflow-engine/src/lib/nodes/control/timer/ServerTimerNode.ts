/**
 * 计时器节点 - 服务器端实现
 * 
 * 这个文件实现计时器节点的服务器端逻辑，包括：
 * - 继承共享的节点信息和配置
 * - 实现服务器端特有的计时逻辑
 * - 处理重复和自动重复功能
 * - 提供服务器端的状态管理
 */

import { BaseNode, NodeData, ExecutionMode } from '../../BaseNode';
import { NodeInfo } from '../../SelfContainedNode';
import {
  TIMER_NODE_INFO,
  TIMER_NODE_INPUTS,
  TIMER_NODE_OUTPUTS,
  TIMER_NODE_DEFAULT_CONFIG,
  TimerNodeConfig,
  TimerNodeResult,
  validateTimerNodeConfig,
  calculateTimerState,
  formatTimerStatus,
  TimerNodeUtils,
  TIMER_NODE_CONSTANTS,
  TimerNodeError,
  TimerNodeErrorType
} from './TimerNodeShared';

/**
 * 计时器节点服务器端实现
 */
export class ServerTimerNode extends BaseNode {
  public config: TimerNodeConfig;
  private timer?: NodeJS.Timeout;
  private startTime: number = 0;
  private currentRepeat: number = 0;
  private timerId?: string;

  constructor(id: string, config: any = {}) {
    super(TIMER_NODE_INFO.type, TIMER_NODE_INFO.name, id);
    
    // 合并配置
    this.config = {
      ...TIMER_NODE_DEFAULT_CONFIG,
      ...config
    };

    // 验证配置
    const validation = validateTimerNodeConfig(this.config);
    if (!validation.valid) {
      throw new TimerNodeError(
        TimerNodeErrorType.INVALID_CONFIG,
        `计时器节点配置无效: ${validation.errors.join(', ')}`,
        validation
      );
    }

    // 设置输入输出引脚
    this.inputs = [...TIMER_NODE_INPUTS];
    this.outputs = [...TIMER_NODE_OUTPUTS];

    // 设置执行模式
    this.executionMode = ExecutionMode.FRONTEND_ONLY;

    // 调用初始化
    this.initialize();

    console.log(`✅ ServerTimerNode created with config:`, this.config);
  }

  /**
   * 初始化方法（BaseNode 要求）
   */
  initialize(): void {
    // 初始化已在构造函数中完成
  }

  /**
   * 处理方法（BaseNode 要求）
   */
  async process(inputData: NodeData): Promise<NodeData> {
    return await this.executeFrontend();
  }

  /**
   * 渲染UI（BaseNode 要求，服务器端返回空字符串）
   */
  renderUI(): string {
    return '';
  }

  /**
   * 渲染图标（BaseNode 要求，服务器端返回空字符串）
   */
  renderIcon(): string {
    return '';
  }

  /**
   * 获取属性面板（BaseNode 要求，服务器端返回空字符串）
   */
  getPropertyPanel(): string {
    return '';
  }

  /**
   * 前端处理（BaseNode 要求）
   */
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    return await this.executeFrontend();
  }

  /**
   * 判断是否需要后端处理（BaseNode 要求）
   */
  shouldUseBackend(inputData: NodeData): boolean {
    return false; // 计时器节点只需要前端处理
  }

  /**
   * 后端处理（BaseNode 要求）
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    return {}; // 计时器节点不需要后端处理
  }

  /**
   * 获取服务器端节点信息（静态方法，用于自动发现）
   */
  static getServerNodeInfo(): NodeInfo {
    return TIMER_NODE_INFO;
  }

  /**
   * 创建服务器端节点实例（静态方法，用于自动发现）
   */
  static createServerInstance(id: string, config: any): ServerTimerNode {
    return new ServerTimerNode(id, config);
  }

  /**
   * 前端执行逻辑
   */
  async executeFrontend(): Promise<any> {
    try {
      this.timerId = TimerNodeUtils.generateTimerId();
      this.startTime = Date.now();
      this.currentRepeat = 0;
      
      console.log(`ℹ️ [${this.type}:${this.id}] 计时器节点前端处理开始`, {
        timerId: this.timerId,
        duration: this.config.duration,
        autoRepeat: this.config.autoRepeat,
        repeatCount: this.config.repeatCount
      });

      // 开始计时
      const result = await this.startTimer();
      
      console.log(`ℹ️ [${this.type}:${this.id}] 计时器节点前端处理完成`, {
        timerId: this.timerId,
        result
      });

      return result;

    } catch (error) {
      console.error(`❌ [${this.type}:${this.id}] 计时器节点前端执行失败:`, error);
      throw new TimerNodeError(
        TimerNodeErrorType.UNKNOWN_ERROR,
        `计时器节点执行失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error, timerId: this.timerId }
      );
    }
  }

  /**
   * 后端执行逻辑（计时器节点通常不需要后端处理）
   */
  async executeBackend(): Promise<any> {
    console.log(`ℹ️ [${this.type}:${this.id}] 计时器节点后端处理（通常为空）`);
    return {};
  }

  /**
   * 开始计时器
   */
  private async startTimer(): Promise<TimerNodeResult> {
    return new Promise((resolve, reject) => {
      const executeTimer = () => {
        console.log(`⏱️ [${this.type}:${this.id}] 开始第 ${this.currentRepeat + 1} 次计时 (${this.config.duration}秒)`);
        
        this.timer = setTimeout(() => {
          this.currentRepeat++;
          
          // 计算当前状态
          const currentState = calculateTimerState(this.config, this.startTime);
          
          console.log(`✅ [${this.type}:${this.id}] 第 ${this.currentRepeat} 次计时完成`, currentState);
          
          // 检查是否需要继续重复
          if (TimerNodeUtils.shouldContinueRepeat(this.config, this.currentRepeat)) {
            // 继续下一次重复
            executeTimer();
          } else {
            // 计时完成
            const finalState = calculateTimerState(this.config, this.startTime);
            resolve(finalState);
          }
        }, this.config.duration * 1000);
      };
      
      // 开始第一次计时
      executeTimer();
      
      // 设置超时保护
      const totalTimeout = TimerNodeUtils.getTotalDuration(this.config) * 1000 + TIMER_NODE_CONSTANTS.DEFAULT_TIMEOUT;
      setTimeout(() => {
        if (this.timer) {
          this.cleanup();
          reject(new TimerNodeError(
            TimerNodeErrorType.EXECUTION_TIMEOUT,
            `计时器执行超时 (${this.config.duration}秒)`
          ));
        }
      }, totalTimeout);
    });
  }

  /**
   * 停止计时器
   */
  stopTimer(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = undefined;
      console.log(`⏹️ [${this.type}:${this.id}] 计时器已停止`);
    }
  }

  /**
   * 重置计时器
   */
  resetTimer(): void {
    this.stopTimer();
    this.startTime = 0;
    this.currentRepeat = 0;
    console.log(`🔄 [${this.type}:${this.id}] 计时器已重置`);
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): TimerNodeResult {
    if (this.startTime === 0) {
      return {
        completed: false,
        elapsed: 0,
        remaining: this.config.duration,
        currentRepeat: 0
      };
    }
    
    return calculateTimerState(this.config, this.startTime);
  }

  /**
   * 获取当前配置
   */
  getConfig(): TimerNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TimerNodeConfig>): void {
    const mergedConfig = { ...this.config, ...newConfig };
    
    // 验证新配置
    const validation = validateTimerNodeConfig(mergedConfig);
    if (!validation.valid) {
      throw new TimerNodeError(
        TimerNodeErrorType.INVALID_CONFIG,
        `配置更新失败: ${validation.errors.join(', ')}`,
        validation
      );
    }

    // 如果计时器正在运行，需要重启
    const wasRunning = !!this.timer;
    if (wasRunning) {
      this.stopTimer();
    }

    this.config = mergedConfig;
    
    if (wasRunning) {
      console.log(`🔧 [${this.type}:${this.id}] 配置已更新，重启计时器:`, this.config);
      // 注意：这里不自动重启，需要外部重新触发
    } else {
      console.log(`🔧 [${this.type}:${this.id}] 配置已更新:`, this.config);
    }
  }

  /**
   * 获取格式化的状态信息
   */
  getFormattedStatus(): string {
    const currentState = this.getCurrentState();
    return formatTimerStatus(this.status, this.config, currentState);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopTimer();
    console.log(`🧹 [${this.type}:${this.id}] 资源已清理`);
  }

  /**
   * 获取节点统计信息
   */
  getStats(): any {
    const currentState = this.getCurrentState();
    return {
      nodeType: this.type,
      executionMode: this.executionMode,
      config: this.config,
      timerId: this.timerId,
      isRunning: !!this.timer,
      currentState,
      formattedStatus: this.getFormattedStatus(),
      progress: TimerNodeUtils.calculateProgress(currentState.elapsed, this.config.duration)
    };
  }

  /**
   * 序列化节点状态
   */
  serialize(): any {
    return {
      ...super.serialize(),
      config: this.config,
      timerId: this.timerId,
      startTime: this.startTime,
      currentRepeat: this.currentRepeat,
      nodeInfo: TIMER_NODE_INFO
    };
  }

  /**
   * 反序列化节点状态
   */
  static deserialize(data: any): ServerTimerNode {
    const node = new ServerTimerNode(data.id, data.config);
    node.status = data.status || 'idle';
    node.timerId = data.timerId;
    node.startTime = data.startTime || 0;
    node.currentRepeat = data.currentRepeat || 0;
    return node;
  }
}

// 导出类型和常量供其他模块使用
export type {
  TimerNodeConfig,
  TimerNodeResult
};

export {
  TimerNodeError,
  TimerNodeErrorType,
  TIMER_NODE_INFO,
  TIMER_NODE_CONSTANTS
};
