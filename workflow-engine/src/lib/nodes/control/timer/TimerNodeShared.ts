/**
 * 计时器节点共享代码
 * 
 * 这个文件包含计时器节点的所有共享逻辑，包括：
 * - 节点信息定义
 * - 输入输出引脚定义
 * - 配置选项定义
 * - 验证逻辑
 * - 工具函数
 */

import { NodeInfo } from '../../SelfContainedNode';
import { Pin } from '../../BaseNode';

/**
 * 计时器节点的基本信息
 */
export const TIMER_NODE_INFO: NodeInfo = {
  type: 'react-timer',
  name: '计时器节点',
  description: '可配置的计时器节点，用于延时和定时操作',
  category: 'custom',
  version: '2.0.0',
  author: 'Workflow Engine',
  tags: ['timer', 'delay', 'schedule'],
  icon: 'clock',
  defaultConfig: {},
  pins: {
    inputs: [],
    outputs: []
  }
};

/**
 * 计时器节点的输入引脚
 */
export const TIMER_NODE_INPUTS: Pin[] = [
  {
    id: 'trigger',
    type: 'input',
    name: 'trigger',
    dataType: 'any',
    description: '触发信号',
    required: false
  }
];

/**
 * 计时器节点的输出引脚
 */
export const TIMER_NODE_OUTPUTS: Pin[] = [
  {
    id: 'completed',
    type: 'output',
    name: 'completed',
    dataType: 'boolean',
    description: '计时完成信号',
    required: false
  },
  {
    id: 'elapsed',
    type: 'output',
    name: 'elapsed',
    dataType: 'number',
    description: '已经过时间（秒）',
    required: false
  }
];

/**
 * 计时器节点的配置选项
 */
export interface TimerNodeConfig {
  /** 计时时长（秒） */
  duration: number;
  /** 是否自动重复 */
  autoRepeat: boolean;
  /** 重复次数（0表示无限重复） */
  repeatCount: number;
  /** 自定义标签 */
  label?: string;
  /** 自定义描述 */
  description?: string;
}

/**
 * 计时器节点的默认配置
 */
export const TIMER_NODE_DEFAULT_CONFIG: TimerNodeConfig = {
  duration: 5,
  autoRepeat: false,
  repeatCount: 0,
  label: TIMER_NODE_INFO.name,
  description: TIMER_NODE_INFO.description
};

/**
 * 计时器节点的执行结果
 */
export interface TimerNodeResult {
  /** 是否完成 */
  completed: boolean;
  /** 已经过时间 */
  elapsed: number;
  /** 剩余时间 */
  remaining: number;
  /** 当前重复次数 */
  currentRepeat: number;
}

/**
 * 验证计时器节点配置
 */
export function validateTimerNodeConfig(config: Partial<TimerNodeConfig>): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证计时时长
  if (config.duration !== undefined) {
    if (typeof config.duration !== 'number') {
      errors.push('计时时长必须是数字');
    } else if (config.duration <= 0) {
      errors.push('计时时长必须大于0');
    } else if (config.duration > 86400) {
      warnings.push('计时时长超过24小时，请确认是否正确');
    }
  }

  // 验证重复次数
  if (config.repeatCount !== undefined) {
    if (typeof config.repeatCount !== 'number') {
      errors.push('重复次数必须是数字');
    } else if (config.repeatCount < 0) {
      errors.push('重复次数不能为负数');
    } else if (config.repeatCount > 1000) {
      warnings.push('重复次数过多，可能影响性能');
    }
  }

  // 验证自动重复
  if (config.autoRepeat !== undefined && typeof config.autoRepeat !== 'boolean') {
    errors.push('自动重复必须是布尔值');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 计算计时器状态
 */
export function calculateTimerState(
  config: TimerNodeConfig,
  startTime: number,
  currentTime: number = Date.now()
): TimerNodeResult {
  const elapsed = Math.max(0, (currentTime - startTime) / 1000);
  const durationMs = config.duration * 1000;
  const totalElapsed = elapsed;
  
  let currentRepeat = 0;
  let remaining = config.duration;
  let completed = false;

  if (config.autoRepeat && config.repeatCount > 0) {
    // 有限重复
    const totalDuration = config.duration * config.repeatCount;
    currentRepeat = Math.floor(elapsed / config.duration);
    remaining = Math.max(0, totalDuration - elapsed);
    completed = elapsed >= totalDuration;
  } else if (config.autoRepeat && config.repeatCount === 0) {
    // 无限重复
    currentRepeat = Math.floor(elapsed / config.duration);
    remaining = config.duration - (elapsed % config.duration);
    completed = false;
  } else {
    // 单次执行
    remaining = Math.max(0, config.duration - elapsed);
    completed = elapsed >= config.duration;
  }

  return {
    completed,
    elapsed: totalElapsed,
    remaining,
    currentRepeat
  };
}

/**
 * 格式化计时器状态信息
 */
export function formatTimerStatus(
  status: 'idle' | 'running' | 'completed' | 'error',
  config: TimerNodeConfig,
  result?: TimerNodeResult
): string {
  switch (status) {
    case 'idle':
      return `等待触发 (${config.duration}秒)`;
    case 'running':
      if (result) {
        const remainingStr = TimerNodeUtils.formatTime(result.remaining);
        if (config.autoRepeat) {
          return `计时中 ${remainingStr} (第${result.currentRepeat + 1}次)`;
        } else {
          return `计时中 ${remainingStr}`;
        }
      }
      return '计时中';
    case 'completed':
      if (result && config.autoRepeat) {
        return `已完成 (共${result.currentRepeat}次)`;
      }
      return '已完成';
    case 'error':
      return '计时失败';
    default:
      return '未知状态';
  }
}

/**
 * 计时器节点的工具函数
 */
export class TimerNodeUtils {
  /**
   * 格式化时间显示
   */
  static formatTime(seconds: number): string {
    if (seconds < 60) {
      return `${Math.ceil(seconds)}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.ceil(seconds % 60);
      return `${minutes}分${remainingSeconds}秒`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes}分钟`;
    }
  }

  /**
   * 计算总执行时间
   */
  static getTotalDuration(config: TimerNodeConfig): number {
    if (config.autoRepeat && config.repeatCount > 0) {
      return config.duration * config.repeatCount;
    }
    return config.duration;
  }

  /**
   * 检查是否应该继续重复
   */
  static shouldContinueRepeat(config: TimerNodeConfig, currentRepeat: number): boolean {
    if (!config.autoRepeat) {
      return false;
    }
    if (config.repeatCount === 0) {
      return true; // 无限重复
    }
    return currentRepeat < config.repeatCount;
  }

  /**
   * 生成唯一的计时器ID
   */
  static generateTimerId(): string {
    return `timer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建延迟Promise
   */
  static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 计算进度百分比
   */
  static calculateProgress(elapsed: number, duration: number): number {
    return Math.min(100, Math.max(0, (elapsed / duration) * 100));
  }
}

/**
 * 计时器节点的常量定义
 */
export const TIMER_NODE_CONSTANTS = {
  /** 最大计时时长（秒） */
  MAX_DURATION: 86400,
  /** 最小计时时长（秒） */
  MIN_DURATION: 0.1,
  /** 最大重复次数 */
  MAX_REPEAT_COUNT: 1000,
  /** 状态更新间隔（毫秒） */
  STATUS_UPDATE_INTERVAL: 100,
  /** 默认超时时间（毫秒） */
  DEFAULT_TIMEOUT: 30000
} as const;

/**
 * 计时器节点的错误类型
 */
export enum TimerNodeErrorType {
  INVALID_CONFIG = 'INVALID_CONFIG',
  EXECUTION_TIMEOUT = 'EXECUTION_TIMEOUT',
  TIMER_ERROR = 'TIMER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 计时器节点的错误类
 */
export class TimerNodeError extends Error {
  constructor(
    public type: TimerNodeErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'TimerNodeError';
  }
}
