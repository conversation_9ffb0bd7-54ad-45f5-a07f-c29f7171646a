/**
 * 计时器节点 - 客户端实现
 * 
 * 这个文件实现计时器节点的客户端逻辑，包括：
 * - 继承 ReactBaseNode 基类
 * - 实现React组件的UI渲染
 * - 处理用户交互和配置更新
 * - 使用共享代码避免重复
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Clock, Settings, Play, Pause, RotateCcw, CheckCircle, AlertCircle } from 'lucide-react';
import { ReactBaseNode } from '../../ReactBaseNode';
import { NodeData } from '../../BaseNode';
import {
  TIMER_NODE_INFO,
  TIMER_NODE_INPUTS,
  TIMER_NODE_OUTPUTS,
  TIMER_NODE_DEFAULT_CONFIG,
  TimerNodeConfig,
  TimerNodeResult,
  validateTimerNodeConfig,
  calculateTimerState,
  formatTimerStatus,
  TimerNodeUtils,
  TIMER_NODE_CONSTANTS
} from './TimerNodeShared';

/**
 * 计时器节点客户端实现
 */
export class ReactTimerNode extends ReactBaseNode {
  public config: TimerNodeConfig;
  private startTime: number = 0;

  constructor(type: string, name?: string) {
    super(type, name || TIMER_NODE_INFO.name);
    
    // 合并配置
    this.config = {
      ...TIMER_NODE_DEFAULT_CONFIG
    };

    // 设置输入输出引脚
    this.inputs = [...TIMER_NODE_INPUTS];
    this.outputs = [...TIMER_NODE_OUTPUTS];

    console.log(`✅ ReactTimerNode created with config:`, this.config);
  }

  /**
   * 前端处理逻辑
   */
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    try {
      this.startTime = Date.now();
      console.log(`ℹ️ [${this.type}:${this.id}] 计时器节点前端处理开始`);

      // 开始计时
      const result = await this.startTimer();
      
      console.log(`ℹ️ [${this.type}:${this.id}] 计时器节点前端处理完成`, result);
      return result;

    } catch (error) {
      console.error(`❌ [${this.type}:${this.id}] 计时器节点前端执行失败:`, error);
      throw error;
    }
  }

  /**
   * 后端处理逻辑（计时器节点通常不需要后端处理）
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    console.log(`ℹ️ [${this.type}:${this.id}] 计时器节点后端处理（通常为空）`);
    return {};
  }

  /**
   * 判断是否需要后端处理
   */
  shouldUseBackend(inputData: NodeData): boolean {
    return false; // 计时器节点只需要前端处理
  }

  /**
   * 开始计时器
   */
  private async startTimer(): Promise<TimerNodeResult> {
    return new Promise((resolve) => {
      const duration = this.config.duration * 1000; // 转换为毫秒
      
      setTimeout(() => {
        const result = calculateTimerState(this.config, this.startTime);
        resolve(result);
      }, duration);
    });
  }

  /**
   * 获取当前状态
   */
  getCurrentState(): TimerNodeResult {
    if (this.startTime === 0) {
      return {
        completed: false,
        elapsed: 0,
        remaining: this.config.duration,
        currentRepeat: 0
      };
    }
    
    return calculateTimerState(this.config, this.startTime);
  }

  /**
   * 渲染节点UI组件
   */
  renderUIComponent(): React.ReactElement {
    return <TimerNodeUI node={this} />;
  }

  /**
   * 渲染节点图标组件
   */
  renderIconComponent(): React.ReactElement {
    return <TimerNodeIcon node={this} />;
  }

  /**
   * 渲染属性面板组件
   */
  getPropertyPanelComponent(): React.ReactElement {
    return <TimerNodePropertyPanel node={this} />;
  }

  /**
   * 获取当前配置
   */
  getConfig(): TimerNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TimerNodeConfig>): void {
    const mergedConfig = { ...this.config, ...newConfig };
    
    // 验证新配置
    const validation = validateTimerNodeConfig(mergedConfig);
    if (!validation.valid) {
      console.error('配置更新失败:', validation.errors);
      return;
    }

    this.config = mergedConfig;
    this.emit('configChanged', this.config);
    console.log(`🔧 [${this.type}:${this.id}] 配置已更新:`, this.config);
  }

  /**
   * 获取格式化的状态信息
   */
  getFormattedStatus(): string {
    const currentState = this.getCurrentState();
    return formatTimerStatus(this.status as any, this.config, currentState);
  }
}

/**
 * 计时器节点UI组件
 */
interface TimerNodeUIProps {
  node: ReactTimerNode;
}

const TimerNodeUI: React.FC<TimerNodeUIProps> = ({ node }) => {
  const [status, setStatus] = useState(node.status);
  const [config, setConfig] = useState(node.getConfig());
  const [currentState, setCurrentState] = useState(node.getCurrentState());

  useEffect(() => {
    const handleStatusChange = () => {
      setStatus(node.status);
      setCurrentState(node.getCurrentState());
    };

    const handleConfigChange = () => {
      setConfig(node.getConfig());
    };

    node.on('statusChanged', handleStatusChange);
    node.on('configChanged', handleConfigChange);

    // 定时更新状态
    const interval = setInterval(() => {
      if (status === 'running') {
        setCurrentState(node.getCurrentState());
      }
    }, 100);

    return () => {
      node.off('statusChanged', handleStatusChange);
      node.off('configChanged', handleConfigChange);
      clearInterval(interval);
    };
  }, [node, status]);

  const getStatusIcon = () => {
    switch (status) {
      case 'running':
        return <Clock size={14} className="text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'error':
        return <AlertCircle size={14} className="text-red-500" />;
      default:
        return <Clock size={14} className="text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'completed':
        return 'border-green-500 bg-green-50';
      case 'error':
        return 'border-red-500 bg-red-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  const progress = TimerNodeUtils.calculateProgress(currentState.elapsed, config.duration);

  return (
    <div className={`p-3 rounded-lg border-2 ${getStatusColor()} min-w-[140px]`}>
      <div className="flex items-center gap-2 mb-2">
        {getStatusIcon()}
        <span className="font-medium text-sm">{config.label || TIMER_NODE_INFO.name}</span>
      </div>
      
      <div className="text-xs text-gray-600 mb-2">
        {node.getFormattedStatus()}
      </div>

      {/* 进度条 */}
      {status === 'running' && (
        <div className="mb-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-500 h-2 rounded-full transition-all duration-100"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {TimerNodeUtils.formatTime(currentState.remaining)} 剩余
          </div>
        </div>
      )}

      {config.autoRepeat && (
        <div className="text-xs text-orange-600 mt-1">
          {config.repeatCount > 0 ? `重复 ${config.repeatCount} 次` : '无限重复'}
        </div>
      )}
    </div>
  );
};

/**
 * 计时器节点图标组件
 */
interface TimerNodeIconProps {
  nodeId?: string;
  status?: string;
  isRunning?: boolean;
  isCompleted?: boolean;
  isError?: boolean;
  isWaiting?: boolean;
  node?: ReactTimerNode;
}

const TimerNodeIcon: React.FC<TimerNodeIconProps> = ({
  nodeId,
  status: propStatus,
  isRunning = false,
  isCompleted = false,
  isError = false,
  isWaiting = false,
  node
}) => {
  const [nodeStatus, setNodeStatus] = useState(propStatus || (node ? node.status : 'idle'));

  useEffect(() => {
    if (node) {
      const handleStatusChange = () => {
        setNodeStatus(node.status);
      };

      node.on('statusChanged', handleStatusChange);

      return () => {
        node.off('statusChanged', handleStatusChange);
      };
    }
  }, [node]);

  // 使用传入的状态或节点状态
  const currentStatus = propStatus || nodeStatus;
  const currentIsRunning = isRunning || currentStatus === 'running';
  const currentIsCompleted = isCompleted || currentStatus === 'completed';
  const currentIsError = isError || currentStatus === 'error';

  const getGradientColors = () => {
    if (currentIsError) {
      return 'from-red-400 to-red-600';
    } else if (currentIsCompleted) {
      return 'from-green-400 to-emerald-600';
    } else if (currentIsRunning) {
      return 'from-blue-400 to-cyan-600';
    } else {
      return 'from-orange-400 to-orange-600';
    }
  };

  const getAnimationClass = () => {
    if (currentIsRunning) {
      return 'animate-spin';
    } else if (currentIsCompleted) {
      return 'animate-pulse';
    } else {
      return '';
    }
  };

  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br ${getGradientColors()} rounded-full
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${getAnimationClass()}
    `}>
      <Clock size={16} />
    </div>
  );
};

/**
 * 计时器节点属性面板组件
 */
interface TimerNodePropertyPanelProps {
  node: ReactTimerNode;
}

const TimerNodePropertyPanel: React.FC<TimerNodePropertyPanelProps> = ({ node }) => {
  const [config, setConfig] = useState(node.getConfig());
  const [validation, setValidation] = useState(validateTimerNodeConfig(config));

  const handleConfigChange = useCallback((key: keyof TimerNodeConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    
    const newValidation = validateTimerNodeConfig(newConfig);
    setValidation(newValidation);
    
    if (newValidation.valid) {
      node.updateConfig(newConfig);
    }
  }, [config, node]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Settings size={16} />
        <h3 className="font-medium">计时器节点配置</h3>
      </div>

      {/* 节点名称 */}
      <div>
        <label className="block text-sm font-medium mb-1">节点名称</label>
        <input
          type="text"
          value={config.label || ''}
          onChange={(e) => handleConfigChange('label', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          placeholder="自定义节点名称"
        />
      </div>

      {/* 计时时长 */}
      <div>
        <label className="block text-sm font-medium mb-1">计时时长 (秒)</label>
        <input
          type="number"
          min={TIMER_NODE_CONSTANTS.MIN_DURATION}
          max={TIMER_NODE_CONSTANTS.MAX_DURATION}
          value={config.duration}
          onChange={(e) => handleConfigChange('duration', parseFloat(e.target.value) || 1)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
        />
        <p className="text-xs text-gray-500 mt-1">计时器运行的时长</p>
      </div>

      {/* 自动重复 */}
      <div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="autoRepeat"
            checked={config.autoRepeat}
            onChange={(e) => handleConfigChange('autoRepeat', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="autoRepeat" className="text-sm font-medium">自动重复</label>
        </div>
        <p className="text-xs text-gray-500 mt-1">勾选后，计时器会自动重复执行</p>
      </div>

      {/* 重复次数 */}
      {config.autoRepeat && (
        <div>
          <label className="block text-sm font-medium mb-1">重复次数</label>
          <input
            type="number"
            min={0}
            max={TIMER_NODE_CONSTANTS.MAX_REPEAT_COUNT}
            value={config.repeatCount}
            onChange={(e) => handleConfigChange('repeatCount', parseInt(e.target.value) || 0)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
          />
          <p className="text-xs text-gray-500 mt-1">0 表示无限重复</p>
        </div>
      )}

      {/* 验证错误 */}
      {validation.errors.length > 0 && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <h4 className="text-sm font-medium text-red-800 mb-1">配置错误</h4>
          <ul className="text-xs text-red-600 space-y-1">
            {validation.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* 验证警告 */}
      {validation.warnings.length > 0 && (
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <h4 className="text-sm font-medium text-yellow-800 mb-1">注意事项</h4>
          <ul className="text-xs text-yellow-600 space-y-1">
            {validation.warnings.map((warning, index) => (
              <li key={index}>• {warning}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// 导出组件供外部使用
export { TimerNodeUI, TimerNodeIcon, TimerNodePropertyPanel };

// 导出节点信息（ClientNodeRegistry 需要）
export const nodeInfo = {
  type: TIMER_NODE_INFO.type,
  name: TIMER_NODE_INFO.name,
  description: TIMER_NODE_INFO.description,
  category: '自定义',
  icon: 'clock',
  version: TIMER_NODE_INFO.version,
  author: TIMER_NODE_INFO.author,
  tags: TIMER_NODE_INFO.tags
};
