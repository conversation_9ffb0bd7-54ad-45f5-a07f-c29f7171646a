import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';

// 引脚接口定义
export interface Pin {
  id: string;
  name: string;
  type: 'input' | 'output';
  dataType: string; // 'string' | 'number' | 'boolean' | 'object' | 'array' | 'any'
  required?: boolean;
  description?: string;
}

// 节点配置接口
export interface NodeConfig {
  [key: string]: any;
}

// 节点数据接口
export interface NodeData {
  [key: string]: any;
}

// 节点状态枚举
export enum NodeStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  COMPLETED = 'completed',
  ERROR = 'error',
  WAITING = 'waiting'
}

// 执行模式枚举
export enum ExecutionMode {
  FRONTEND_ONLY = 'frontend_only',     // 仅前端执行（轻量级运算）
  BACKEND_ONLY = 'backend_only',       // 仅后端执行（重量级运算）
  HYBRID = 'hybrid'                    // 混合模式（前端+后端）
}

// WebSocket消息类型
export enum WSMessageType {
  // 前端到后端
  FRONTEND_TO_BACKEND = 'frontend_to_backend',
  EXECUTE_BACKEND = 'execute_backend',
  UPDATE_CONFIG = 'update_config',

  // 后端到前端
  BACKEND_TO_FRONTEND = 'backend_to_frontend',
  UPDATE_STATUS = 'update_status',
  UPDATE_UI = 'update_ui',
  SEND_DATA = 'send_data',

  // 节点间通信
  NODE_MESSAGE = 'node_message',
  BROADCAST = 'broadcast'
}

// WebSocket消息接口
export interface WSMessage {
  type: WSMessageType;
  nodeId: string;
  data: any;
  timestamp: number;
  messageId: string;
}

// 抽象节点基类
export abstract class BaseNode extends EventEmitter {
  public readonly id: string;
  public readonly type: string;
  public name: string;
  public description: string;
  public status: NodeStatus = NodeStatus.IDLE;
  public inputs: Pin[] = [];
  public outputs: Pin[] = [];
  public config: NodeConfig = {};
  public position: { x: number; y: number } = { x: 0, y: 0 };

  // 执行模式和线程化支持
  public executionMode: ExecutionMode = ExecutionMode.HYBRID;
  public isThreaded: boolean = false;
  public threadId: string | null = null;

  // WebSocket通信
  protected wsConnection: WebSocket | null = null;
  protected wsUrl: string = '';
  protected messageHandlers: Map<WSMessageType, Function[]> = new Map();

  // 节点间通信
  protected connectedNodes: Map<string, BaseNode> = new Map();
  protected messageQueue: WSMessage[] = [];

  private inputData: Map<string, any> = new Map();
  private outputData: Map<string, any> = new Map();

  constructor(type: string, name?: string, id?: string) {
    super();
    this.id = id || uuidv4();
    this.type = type;
    this.name = name || type;
    this.description = '';

    // 初始化WebSocket消息处理器
    this.initializeMessageHandlers();

    // 生成线程ID
    this.threadId = `thread_${this.id}`;

    // 注意：不在这里调用 initialize()，因为子类的方法还没有被绑定
    // 子类应该在自己的构造函数中调用 this.initialize()
  }

  // 抽象方法 - 子类必须实现
  abstract initialize(): void;
  abstract process(inputData: NodeData): Promise<NodeData>;
  abstract renderUI(): string; // 返回HTML/CSS/JS字符串
  abstract renderIcon(): string; // 返回节点图标HTML
  abstract getPropertyPanel(): string; // 返回属性面板HTML

  // 新增抽象方法 - 前后端分离执行
  abstract processFrontend(inputData: NodeData): Promise<NodeData>; // 前端轻量级处理
  abstract processBackend(inputData: NodeData): Promise<NodeData>;  // 后端重量级处理
  abstract shouldUseBackend(inputData: NodeData): boolean;          // 判断是否需要后端处理

  // 引脚管理
  addInputPin(pin: Omit<Pin, 'id' | 'type'>): Pin {
    const newPin: Pin = {
      id: uuidv4(),
      type: 'input',
      ...pin
    };
    this.inputs.push(newPin);
    this.updateNodeShape();
    return newPin;
  }

  addOutputPin(pin: Omit<Pin, 'id' | 'type'>): Pin {
    const newPin: Pin = {
      id: uuidv4(),
      type: 'output',
      ...pin
    };
    this.outputs.push(newPin);
    this.updateNodeShape();
    return newPin;
  }

  // ==================== WebSocket通信方法 ====================

  /**
   * 初始化WebSocket消息处理器
   */
  private initializeMessageHandlers(): void {
    // 注册默认消息处理器
    this.onMessage(WSMessageType.UPDATE_STATUS, this.handleStatusUpdate.bind(this));
    this.onMessage(WSMessageType.UPDATE_UI, this.handleUIUpdate.bind(this));
    this.onMessage(WSMessageType.SEND_DATA, this.handleDataReceived.bind(this));
    this.onMessage(WSMessageType.EXECUTE_BACKEND, this.handleBackendExecution.bind(this));
  }

  /**
   * 连接到WebSocket服务器
   */
  async connectWebSocket(wsUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.wsUrl = wsUrl;
        this.wsConnection = new WebSocket(wsUrl);

        this.wsConnection.onopen = () => {
          console.log(`🔗 Node ${this.id} connected to WebSocket`);
          resolve();
        };

        this.wsConnection.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };

        this.wsConnection.onerror = (error) => {
          console.error(`❌ WebSocket error for node ${this.id}:`, error);
          reject(error);
        };

        this.wsConnection.onclose = () => {
          console.log(`🔌 WebSocket connection closed for node ${this.id}`);
          this.wsConnection = null;
        };

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnectWebSocket(): void {
    if (this.wsConnection) {
      this.wsConnection.close();
      this.wsConnection = null;
    }
  }

  /**
   * 发送WebSocket消息
   */
  sendMessage(type: WSMessageType, data: any, targetNodeId?: string): void {
    if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
      console.warn(`⚠️ WebSocket not connected for node ${this.id}`);
      return;
    }

    const message: WSMessage = {
      type,
      nodeId: targetNodeId || this.id,
      data,
      timestamp: Date.now(),
      messageId: uuidv4()
    };

    this.wsConnection.send(JSON.stringify(message));
  }

  /**
   * 注册消息处理器
   */
  onMessage(type: WSMessageType, handler: Function): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(event: MessageEvent): void {
    try {
      const message: WSMessage = JSON.parse(event.data);

      // 检查消息是否针对此节点
      if (message.nodeId !== this.id && message.type !== WSMessageType.BROADCAST) {
        return;
      }

      // 调用相应的消息处理器
      const handlers = this.messageHandlers.get(message.type);
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message);
          } catch (error) {
            console.error(`❌ Error handling message ${message.type}:`, error);
          }
        });
      }

    } catch (error) {
      console.error(`❌ Error parsing WebSocket message:`, error);
    }
  }

  removePin(pinId: string): boolean {
    const inputIndex = this.inputs.findIndex(pin => pin.id === pinId);
    if (inputIndex !== -1) {
      this.inputs.splice(inputIndex, 1);
      this.updateNodeShape();
      return true;
    }

    const outputIndex = this.outputs.findIndex(pin => pin.id === pinId);
    if (outputIndex !== -1) {
      this.outputs.splice(outputIndex, 1);
      this.updateNodeShape();
      return true;
    }

    return false;
  }

  // ==================== 默认消息处理器 ====================

  /**
   * 处理状态更新消息
   */
  private handleStatusUpdate(message: WSMessage): void {
    const { status } = message.data;
    if (Object.values(NodeStatus).includes(status)) {
      this.status = status;
      this.emit('statusChanged', this.status);
    }
  }

  /**
   * 处理UI更新消息
   */
  private handleUIUpdate(message: WSMessage): void {
    const { uiData } = message.data;
    this.emit('uiUpdate', uiData);
  }

  /**
   * 处理数据接收消息
   */
  private handleDataReceived(message: WSMessage): void {
    const { pinName, data } = message.data;
    this.emit('dataReceived', { pinName, data });
  }

  /**
   * 处理后端执行请求
   */
  private async handleBackendExecution(message: WSMessage): Promise<void> {
    try {
      const { inputData } = message.data;
      const result = await this.processBackend(inputData);

      // 发送执行结果回前端
      this.sendMessage(WSMessageType.BACKEND_TO_FRONTEND, {
        action: 'execution_result',
        result
      });

    } catch (error) {
      this.sendMessage(WSMessageType.BACKEND_TO_FRONTEND, {
        action: 'execution_error',
        error: error.message
      });
    }
  }

  // ==================== 线程化支持 ====================

  /**
   * 启动节点线程
   */
  async startThread(): Promise<void> {
    if (this.isThreaded) {
      console.warn(`⚠️ Node ${this.id} is already threaded`);
      return;
    }

    try {
      this.isThreaded = true;
      console.log(`🧵 Starting thread for node ${this.id}`);

      // 连接WebSocket（如果配置了）
      if (this.wsUrl) {
        await this.connectWebSocket(this.wsUrl);
      }

      // 启动节点特定的线程逻辑
      await this.onThreadStart();

      this.emit('threadStarted', { nodeId: this.id, threadId: this.threadId });

    } catch (error) {
      this.isThreaded = false;
      console.error(`❌ Failed to start thread for node ${this.id}:`, error);
      throw error;
    }
  }

  /**
   * 停止节点线程
   */
  async stopThread(): Promise<void> {
    if (!this.isThreaded) {
      return;
    }

    try {
      console.log(`🛑 Stopping thread for node ${this.id}`);

      // 停止节点特定的线程逻辑
      await this.onThreadStop();

      // 断开WebSocket连接
      this.disconnectWebSocket();

      this.isThreaded = false;
      this.threadId = null;

      this.emit('threadStopped', { nodeId: this.id });

    } catch (error) {
      console.error(`❌ Failed to stop thread for node ${this.id}:`, error);
      throw error;
    }
  }

  /**
   * 线程启动时的回调（子类可重写）
   */
  protected async onThreadStart(): Promise<void> {
    // 默认实现为空，子类可以重写
  }

  /**
   * 线程停止时的回调（子类可重写）
   */
  protected async onThreadStop(): Promise<void> {
    // 默认实现为空，子类可以重写
  }

  // ==================== 节点间通信 ====================

  /**
   * 连接到其他节点
   */
  connectToNode(nodeId: string, node: BaseNode): void {
    this.connectedNodes.set(nodeId, node);
    console.log(`🔗 Node ${this.id} connected to node ${nodeId}`);
  }

  /**
   * 断开与其他节点的连接
   */
  disconnectFromNode(nodeId: string): void {
    this.connectedNodes.delete(nodeId);
    console.log(`🔌 Node ${this.id} disconnected from node ${nodeId}`);
  }

  /**
   * 向连接的节点发送消息
   */
  sendToNode(nodeId: string, data: any): void {
    const targetNode = this.connectedNodes.get(nodeId);
    if (targetNode) {
      targetNode.receiveFromNode(this.id, data);
    } else {
      console.warn(`⚠️ Node ${nodeId} not connected to ${this.id}`);
    }
  }

  /**
   * 接收来自其他节点的消息
   */
  receiveFromNode(sourceNodeId: string, data: any): void {
    console.log(`📨 Node ${this.id} received data from ${sourceNodeId}:`, data);
    this.emit('nodeMessage', { sourceNodeId, data });

    // 触发前端处理
    this.processFrontendMessage(sourceNodeId, data);
  }

  /**
   * 处理来自其他节点的前端消息
   */
  protected async processFrontendMessage(sourceNodeId: string, data: any): Promise<void> {
    // 默认实现：直接处理数据
    try {
      const result = await this.processFrontend(data);

      // 如果需要后端处理
      if (this.shouldUseBackend(data)) {
        this.sendMessage(WSMessageType.EXECUTE_BACKEND, { inputData: data });
      } else {
        // 直接发送结果到下游节点
        this.broadcastToDownstream(result);
      }

    } catch (error) {
      console.error(`❌ Error processing frontend message:`, error);
    }
  }

  /**
   * 广播数据到下游节点
   */
  protected broadcastToDownstream(data: any): void {
    this.connectedNodes.forEach((node, nodeId) => {
      this.sendToNode(nodeId, data);
    });
  }

  // 根据引脚数量自动调节节点外形
  updateNodeShape(): void {
    const totalPins = this.inputs.length + this.outputs.length;
    const minHeight = 60;
    const pinHeight = 20;
    const newHeight = Math.max(minHeight, totalPins * pinHeight + 40);
    
    this.emit('shapeUpdate', {
      nodeId: this.id,
      height: newHeight,
      inputCount: this.inputs.length,
      outputCount: this.outputs.length
    });
  }

  // 数据处理
  setInputData(pinId: string, data: any): void {
    this.inputData.set(pinId, data);
    this.emit('inputDataChanged', { pinId, data });
  }

  getInputData(pinId?: string): any {
    if (pinId) {
      return this.inputData.get(pinId);
    }
    // 返回所有输入数据
    const result: NodeData = {};
    this.inputs.forEach(pin => {
      result[pin.name] = this.inputData.get(pin.id);
    });
    return result;
  }

  setOutputData(pinId: string, data: any): void {
    this.outputData.set(pinId, data);
    this.emit('outputDataChanged', { pinId, data });
  }

  getOutputData(pinId?: string): any {
    if (pinId) {
      return this.outputData.get(pinId);
    }
    // 返回所有输出数据
    const result: NodeData = {};
    this.outputs.forEach(pin => {
      result[pin.name] = this.outputData.get(pin.id);
    });
    return result;
  }

  // ==================== 执行方法 ====================

  /**
   * 主执行方法 - 支持前后端分离执行
   */
  async execute(): Promise<void> {
    try {
      console.log(`🚀🚀🚀 BASENODE EXECUTE METHOD CALLED FOR NODE ${this.id}, TYPE: ${this.type} 🚀🚀🚀`);
      console.log(`🚀 Execution mode: ${this.executionMode}`);
      console.log(`🚀 Node status before execution:`, this.status);

      this.status = NodeStatus.RUNNING;
      this.emit('statusChanged', this.status);

      // 检查必需的输入数据
      const missingInputs = this.inputs
        .filter(pin => pin.required)
        .filter(pin => !this.inputData.has(pin.id));

      if (missingInputs.length > 0) {
        console.log(`❌ Missing required inputs:`, missingInputs);
        throw new Error(`Missing required inputs: ${missingInputs.map(p => p.name).join(', ')}`);
      }

      const inputData = this.getInputData();
      let outputData: NodeData;

      // 根据执行模式选择执行方式
      switch (this.executionMode) {
        case ExecutionMode.FRONTEND_ONLY:
          console.log(`🔥 Executing in FRONTEND_ONLY mode`);
          outputData = await this.processFrontend(inputData);
          break;

        case ExecutionMode.BACKEND_ONLY:
          console.log(`🔥 Executing in BACKEND_ONLY mode`);
          outputData = await this.processBackend(inputData);
          break;

        case ExecutionMode.HYBRID:
          console.log(`🔥 Executing in HYBRID mode`);
          // 先尝试前端处理
          outputData = await this.processFrontend(inputData);

          // 判断是否需要后端处理
          if (this.shouldUseBackend(inputData)) {
            console.log(`🔄 Switching to backend processing`);
            outputData = await this.processBackend(inputData);
          }
          break;

        default:
          // 向后兼容：使用原有的process方法
          console.log(`🔥 Using legacy process() method`);
          outputData = await this.process(inputData);
          break;
      }

      console.log(`🔥🔥🔥 EXECUTION COMPLETED FOR NODE ${this.id} 🔥🔥🔥`);
      console.log(`🔥 OutputData:`, outputData);

      // 设置输出数据
      this.outputs.forEach(pin => {
        if (outputData[pin.name] !== undefined) {
          this.setOutputData(pin.id, outputData[pin.name]);
        }
      });

      this.status = NodeStatus.COMPLETED;
      this.emit('statusChanged', this.status);
      this.emit('completed', this.getOutputData());

      // 如果是线程化节点，自动发送数据到下游节点
      if (this.isThreaded) {
        this.broadcastToDownstream(outputData);
      }

    } catch (error) {
      this.status = NodeStatus.ERROR;
      this.emit('statusChanged', this.status);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 设置执行模式
   */
  setExecutionMode(mode: ExecutionMode): void {
    this.executionMode = mode;
    console.log(`🔧 Node ${this.id} execution mode set to: ${mode}`);
  }

  // 配置管理
  updateConfig(newConfig: Partial<NodeConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configChanged', this.config);
  }

  // 序列化和反序列化
  serialize(): any {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      description: this.description,
      inputs: this.inputs,
      outputs: this.outputs,
      config: this.config,
      position: this.position
    };
  }

  static deserialize(data: any): BaseNode {
    // 这里需要根据type创建具体的节点实例
    // 实际实现中会通过节点工厂来处理
    throw new Error('deserialize method must be implemented by NodeFactory');
  }

  // 清理资源
  destroy(): void {
    // 停止线程
    if (this.isThreaded) {
      this.stopThread().catch(error => {
        console.error(`❌ Error stopping thread during destroy:`, error);
      });
    }

    // 断开WebSocket连接
    this.disconnectWebSocket();

    // 清理节点连接
    this.connectedNodes.clear();

    // 清理消息队列
    this.messageQueue = [];

    // 清理事件监听器
    this.removeAllListeners();

    // 清理数据
    this.inputData.clear();
    this.outputData.clear();

    console.log(`🗑️ Node ${this.id} destroyed`);
  }
}
