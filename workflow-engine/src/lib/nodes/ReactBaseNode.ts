import { BaseNode, NodeData, Pin, NodeConfig, NodeStatus, ExecutionMode, WSMessageType } from './BaseNode';
import { ReactElement } from 'react';

// React节点基类 - 支持React组件渲染和前后端分离执行
export abstract class ReactBaseNode extends BaseNode {
  // React特定属性
  protected frontendState: any = {};
  protected uiUpdateCallbacks: Map<string, Function> = new Map();

  constructor(type: string, name?: string) {
    super(type, name);

    // React节点默认使用混合执行模式
    this.executionMode = ExecutionMode.HYBRID;

    // 注册React特定的消息处理器
    this.initializeReactMessageHandlers();
  }

  // 抽象方法 - 返回React组件而不是HTML字符串
  abstract renderUIComponent(): ReactElement;
  abstract renderIconComponent(): ReactElement;
  abstract getPropertyPanelComponent(): ReactElement;

  // React节点必须实现的前后端分离方法
  abstract processFrontend(inputData: NodeData): Promise<NodeData>;
  abstract processBackend(inputData: NodeData): Promise<NodeData>;
  abstract shouldUseBackend(inputData: NodeData): boolean;

  // 保持原有的字符串方法作为后备，但现在它们调用React组件
  renderUI(): string {
    // 这个方法现在主要用于向后兼容
    // 实际的UI渲染会通过React组件完成
    return `<div id="node-ui-${this.id}">React组件将在此处渲染</div>`;
  }

  renderIcon(): string {
    return `<div id="node-icon-${this.id}">React组件将在此处渲染</div>`;
  }

  getPropertyPanel(): string {
    return `<div id="node-properties-${this.id}">React组件将在此处渲染</div>`;
  }

  // 新增方法：获取组件props
  getUIProps(): any {
    return {
      nodeId: this.id,
      config: this.config,
      status: this.status,
      inputs: this.inputs,
      outputs: this.outputs,
      onConfigChange: (key: string, value: any) => {
        this.updateConfig({ [key]: value });
      },
      onAction: (action: string, data?: any) => {
        this.emit('nodeAction', { action, data });
      }
    };
  }

  getIconProps(): any {
    return {
      nodeId: this.id,
      status: this.status,
      type: this.type,
      isRunning: this.status === NodeStatus.RUNNING,
      isCompleted: this.status === NodeStatus.COMPLETED,
      isError: this.status === NodeStatus.ERROR,
      isWaiting: this.status === NodeStatus.WAITING
    };
  }

  getPropertyProps(): any {
    return {
      nodeId: this.id,
      config: this.config,
      name: this.name,
      description: this.description,
      type: this.type,
      inputs: this.inputs,
      outputs: this.outputs,
      position: this.position,
      onConfigChange: (key: string, value: any) => {
        this.updateConfig({ [key]: value });
      },
      onNameChange: (name: string) => {
        this.name = name;
        this.emit('nameChanged', name);
      },
      onDescriptionChange: (description: string) => {
        this.description = description;
        this.emit('descriptionChanged', description);
      }
    };
  }

  // ==================== React特定方法 ====================

  /**
   * 初始化React特定的消息处理器
   */
  private initializeReactMessageHandlers(): void {
    this.onMessage(WSMessageType.UPDATE_UI, this.handleReactUIUpdate.bind(this));
    this.onMessage(WSMessageType.FRONTEND_TO_BACKEND, this.handleFrontendToBackend.bind(this));
  }

  /**
   * 处理React UI更新
   */
  private handleReactUIUpdate(message: any): void {
    const { componentType, props } = message.data;
    this.updateFrontendState(props);
    this.triggerUIUpdate(componentType);
  }

  /**
   * 处理前端到后端的消息
   */
  private async handleFrontendToBackend(message: any): Promise<void> {
    const { action, data } = message.data;

    switch (action) {
      case 'execute_backend':
        try {
          const result = await this.processBackend(data);
          this.sendMessage(WSMessageType.BACKEND_TO_FRONTEND, {
            action: 'backend_result',
            result
          });
        } catch (error) {
          this.sendMessage(WSMessageType.BACKEND_TO_FRONTEND, {
            action: 'backend_error',
            error: error.message
          });
        }
        break;

      case 'update_config':
        this.updateConfig(data);
        break;

      default:
        console.warn(`⚠️ Unknown frontend-to-backend action: ${action}`);
    }
  }

  /**
   * 更新前端状态
   */
  updateFrontendState(newState: any): void {
    this.frontendState = { ...this.frontendState, ...newState };
    this.emit('frontendStateChanged', this.frontendState);
  }

  /**
   * 获取前端状态
   */
  getFrontendState(): any {
    return this.frontendState;
  }

  /**
   * 注册UI更新回调
   */
  onUIUpdate(componentType: string, callback: Function): void {
    this.uiUpdateCallbacks.set(componentType, callback);
  }

  /**
   * 触发UI更新
   */
  triggerUIUpdate(componentType: string): void {
    const callback = this.uiUpdateCallbacks.get(componentType);
    if (callback) {
      callback(this.frontendState);
    }
    this.emit('uiUpdateTriggered', { componentType, state: this.frontendState });
  }

  /**
   * 向前端发送数据
   */
  sendToFrontend(action: string, data: any): void {
    this.sendMessage(WSMessageType.BACKEND_TO_FRONTEND, {
      action,
      data
    });
  }

  /**
   * 向后端发送数据
   */
  sendToBackend(action: string, data: any): void {
    this.sendMessage(WSMessageType.FRONTEND_TO_BACKEND, {
      action,
      data
    });
  }

  /**
   * 默认的process方法实现 - 向后兼容
   */
  async process(inputData: NodeData): Promise<NodeData> {
    // 默认使用前端处理，如果需要后端处理则调用后端
    let result = await this.processFrontend(inputData);

    if (this.shouldUseBackend(inputData)) {
      result = await this.processBackend(inputData);
    }

    return result;
  }

  /**
   * 线程启动时的React特定初始化
   */
  protected async onThreadStart(): Promise<void> {
    await super.onThreadStart();

    // React节点特定的线程启动逻辑
    console.log(`🧵 React node ${this.id} thread started`);

    // 初始化前端状态
    this.frontendState = this.getInitialFrontendState();

    // 发送初始状态到前端
    this.sendToFrontend('initial_state', this.frontendState);
  }

  /**
   * 线程停止时的React特定清理
   */
  protected async onThreadStop(): Promise<void> {
    await super.onThreadStop();

    // 清理React特定资源
    this.uiUpdateCallbacks.clear();
    this.frontendState = {};

    console.log(`🛑 React node ${this.id} thread stopped`);
  }

  /**
   * 获取初始前端状态（子类可重写）
   */
  protected getInitialFrontendState(): any {
    return {
      status: this.status,
      config: this.config,
      timestamp: Date.now()
    };
  }

  /**
   * 清理React特定资源
   */
  destroy(): void {
    this.uiUpdateCallbacks.clear();
    this.frontendState = {};
    super.destroy();
  }
}
