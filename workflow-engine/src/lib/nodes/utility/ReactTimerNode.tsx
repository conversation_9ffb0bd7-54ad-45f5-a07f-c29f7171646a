'use client';

import React, { useState, useEffect } from 'react';
import { SelfContainedNode, SelfContainedNodeDecorator, NodeInfo } from '../SelfContainedNode';
import { NodeData, ExecutionMode } from '../BaseNode';
import { Clock } from 'lucide-react';

// React组件：计时器节点UI
interface TimerNodeUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const TimerNodeUI: React.FC<TimerNodeUIProps> = ({ nodeId, config, status, onAction }) => {
  const [timeLeft, setTimeLeft] = useState(config.duration || 10);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsRunning(false);
            onAction('timerComplete', { duration: config.duration });
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isRunning, timeLeft, config.duration, onAction]);

  const handleStart = () => {
    setTimeLeft(config.duration || 10);
    setIsRunning(true);
    onAction('timerStart');
  };

  const handleStop = () => {
    setIsRunning(false);
    onAction('timerStop');
  };

  const handleReset = () => {
    setIsRunning(false);
    setTimeLeft(config.duration || 10);
    onAction('timerReset');
  };

  return (
    <div className="p-5 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg text-white text-center min-w-[200px]">
      <div className="text-lg font-bold mb-2">⏰ 计时器</div>
      <div className="text-3xl font-mono mb-3">{timeLeft}s</div>
      <div className="text-sm opacity-90 mb-3">状态: {status}</div>
      <div className="flex space-x-2">
        <button
          onClick={handleStart}
          disabled={isRunning}
          className="flex-1 bg-green-500/80 hover:bg-green-500 disabled:bg-gray-500/50 text-white px-3 py-2 rounded text-sm transition-colors"
        >
          开始
        </button>
        <button
          onClick={handleStop}
          disabled={!isRunning}
          className="flex-1 bg-red-500/80 hover:bg-red-500 disabled:bg-gray-500/50 text-white px-3 py-2 rounded text-sm transition-colors"
        >
          停止
        </button>
        <button
          onClick={handleReset}
          className="flex-1 bg-yellow-500/80 hover:bg-yellow-500 text-white px-3 py-2 rounded text-sm transition-colors"
        >
          重置
        </button>
      </div>
    </div>
  );
};

// React组件：计时器节点图标
interface TimerNodeIconProps {
  nodeId: string;
  status: string;
  isRunning: boolean;
}

const TimerNodeIcon: React.FC<TimerNodeIconProps> = ({ nodeId, status, isRunning }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-blue-400 to-cyan-600 rounded-full
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isRunning ? 'animate-spin' : ''}
    `}>
      <Clock size={16} />
    </div>
  );
};

// React组件：计时器节点属性面板
interface TimerNodePropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const TimerNodeProperties: React.FC<TimerNodePropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      {/* 基础属性 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          节点名称
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="自定义节点名称"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          描述
        </label>
        <textarea
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          rows={2}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="节点描述"
        />
      </div>

      {/* 节点特定配置 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          计时时长 (秒)
        </label>
        <input
          type="number"
          value={config.duration || 10}
          onChange={(e) => onConfigChange('duration', parseInt(e.target.value) || 10)}
          min="1"
          max="3600"
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoStart || false}
            onChange={(e) => onConfigChange('autoStart', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">自动开始</span>
        </label>
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.repeat || false}
            onChange={(e) => onConfigChange('repeat', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">重复计时</span>
        </label>
      </div>
    </div>
  );
};

// 节点信息定义
const timerNodeInfo: Partial<NodeInfo> = {
  type: 'react-timer',
  name: '计时器节点',
  description: '可配置的计时器节点，用于延时和定时操作',
  category: '自定义',
  icon: 'clock',
  version: '2.0.0',
  author: 'System',
  tags: ['utility', 'timer', 'delay'],
  defaultConfig: {
    duration: 10,
    autoStart: false,
    repeat: false
  },
  pins: {
    inputs: [
      { name: 'trigger', dataType: 'any', description: '触发计时器', required: false },
      { name: 'reset', dataType: 'any', description: '重置计时器', required: false }
    ],
    outputs: [
      { name: 'completed', dataType: 'boolean', description: '计时完成信号' },
      { name: 'elapsed', dataType: 'number', description: '已经过的时间' }
    ]
  }
};

// 计时器节点类 - 自包含架构
@SelfContainedNodeDecorator(timerNodeInfo)
export class ReactTimerNode extends SelfContainedNode {
  private timer: NodeJS.Timeout | null = null;
  private initialized = false;

  constructor() {
    super('react-timer', '计时器节点');
    this.description = '可配置的计时器节点，用于延时和定时操作';

    // 计时器节点使用混合执行模式
    this.executionMode = ExecutionMode.HYBRID;

    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    if (this.initialized) {
      return; // 防止重复初始化
    }

    // 输入引脚（都是可选的）
    this.addInputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发计时器',
      required: false
    });

    this.addInputPin({
      name: 'reset',
      dataType: 'any',
      description: '重置计时器',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'completed',
      dataType: 'boolean',
      description: '计时完成信号'
    });

    this.addOutputPin({
      name: 'elapsed',
      dataType: 'number',
      description: '已经过的时间'
    });

    // 默认配置
    this.config = {
      duration: 10,
      autoStart: false,
      repeat: false
    };

    this.initialized = true;
  }

  /**
   * 获取服务器端节点信息
   */
  static getServerNodeInfo(): any {
    return timerNodeInfo;
  }

  /**
   * 创建服务器端实例
   */
  static createServerInstance(config: any = {}): ReactTimerNode {
    const instance = new ReactTimerNode();
    instance.updateConfig(config);
    return instance;
  }

  // 前端处理（轻量级计时）
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    console.log('🔥 Timer node frontend processing');

    const duration = this.config.duration || 10;
    const autoStart = this.config.autoStart || false;

    // 检查是否有触发信号或自动开始
    const shouldStart = autoStart || inputData.trigger !== undefined;

    if (!shouldStart) {
      return {
        completed: false,
        elapsed: 0,
        isRunning: false,
        timestamp: new Date().toISOString(),
        source: 'frontend'
      };
    }

    // 前端处理短时间计时（≤30秒）
    if (duration <= 30) {
      console.log('🔥 Frontend timer starting with duration:', duration);

      return new Promise((resolve) => {
        let elapsed = 0;

        // 发送开始状态到前端UI
        this.updateFrontendState({
          isRunning: true,
          elapsed: 0,
          remaining: duration,
          progress: 0
        });

        this.timer = setInterval(() => {
          elapsed += 1;

          // 更新前端状态
          this.updateFrontendState({
            isRunning: true,
            elapsed,
            remaining: duration - elapsed,
            progress: elapsed / duration
          });

          if (elapsed >= duration) {
            if (this.timer) {
              clearInterval(this.timer);
              this.timer = null;
            }

            this.updateFrontendState({
              isRunning: false,
              elapsed: duration,
              remaining: 0,
              progress: 1
            });

            resolve({
              completed: true,
              elapsed: duration,
              isRunning: false,
              timestamp: new Date().toISOString(),
              source: 'frontend'
            });
          }
        }, 1000);
      });
    } else {
      // 长时间计时需要后端处理
      return {
        completed: false,
        elapsed: 0,
        isRunning: false,
        needsBackend: true,
        timestamp: new Date().toISOString(),
        source: 'frontend'
      };
    }
  }

  // 后端处理（重量级计时）
  async processBackend(inputData: NodeData): Promise<NodeData> {
    console.log('🔥 Timer node backend processing');

    const duration = this.config.duration || 10;
    const autoStart = this.config.autoStart || false;

    // 检查是否有触发信号或自动开始
    const shouldStart = autoStart || inputData.trigger !== undefined;

    if (!shouldStart) {
      return {
        completed: false,
        elapsed: 0,
        isRunning: false,
        timestamp: new Date().toISOString(),
        source: 'backend'
      };
    }

    console.log('🔥 Backend timer starting with duration:', duration);

    return new Promise((resolve) => {
      let elapsed = 0;

      // 向前端发送开始状态
      this.sendToFrontend('timer_started', {
        duration,
        elapsed: 0,
        isRunning: true
      });

      this.timer = setInterval(() => {
        elapsed += 1;

        // 定期向前端发送进度更新
        this.sendToFrontend('timer_progress', {
          elapsed,
          remaining: duration - elapsed,
          progress: elapsed / duration,
          isRunning: true
        });

        if (elapsed >= duration) {
          if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
          }

          // 向前端发送完成状态
          this.sendToFrontend('timer_completed', {
            elapsed: duration,
            isRunning: false
          });

          resolve({
            completed: true,
            elapsed: duration,
            isRunning: false,
            timestamp: new Date().toISOString(),
            source: 'backend'
          });
        }
      }, 1000);
    });
  }

  // 判断是否需要后端处理
  shouldUseBackend(inputData: NodeData): boolean {
    const duration = this.config.duration || 10;
    // 超过30秒的计时使用后端处理
    return duration > 30;
  }

  // 向后兼容的process方法
  async process(inputData: NodeData): Promise<NodeData> {
    // 使用新的前后端分离逻辑
    if (this.shouldUseBackend(inputData)) {
      return this.processBackend(inputData);
    } else {
      return this.processFrontend(inputData);
    }
  }

  destroy(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    super.destroy();
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(TimerNodeUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = this.getIconProps();
    return React.createElement(TimerNodeIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(TimerNodeProperties, props);
  }
}

// 导出组件供外部使用
export { TimerNodeUI, TimerNodeIcon, TimerNodeProperties };

// 旧的nodeInfo定义已移除，使用新的timerNodeInfo

// 导出节点信息 - 使用统一的节点信息
export const nodeInfo = timerNodeInfo;

// 默认导出节点类 - 这是ClientNodeRegistry需要的
export default ReactTimerNode;
