import { ReactBaseNode } from './ReactBaseNode';
import { NodeData, ExecutionMode } from './BaseNode';

/**
 * 自包含节点基类 - 支持前后端分离的独立运行
 * 
 * 每个继承此类的节点都包含完整的前后端逻辑，无需外部依赖
 * 实现真正的节点独立运行架构
 */
export abstract class SelfContainedNode extends ReactBaseNode {
  
  constructor(type: string, name?: string) {
    super(type, name);
    
    // 自包含节点默认使用混合执行模式
    this.executionMode = ExecutionMode.HYBRID;
    
    // 初始化节点特定的配置
    this.initializeNodeSpecificConfig();
  }

  /**
   * 初始化节点特定配置（子类可重写）
   */
  protected initializeNodeSpecificConfig(): void {
    // 默认实现为空，子类可以重写
  }

  /**
   * 获取节点的服务器端信息
   * 这个方法在服务器端被调用，用于提供节点的基本信息
   */
  static getServerNodeInfo(): any {
    return {
      type: 'unknown',
      name: '未知节点',
      description: '节点描述',
      category: '自定义',
      icon: 'settings',
      version: '1.0.0',
      author: 'System',
      tags: ['self-contained'],
      defaultConfig: {},
      pins: {
        inputs: [],
        outputs: []
      }
    };
  }

  /**
   * 创建服务器端节点实例的工厂方法
   * 这个方法在服务器端被调用，创建不包含React组件的节点实例
   */
  static createServerInstance(config: any = {}): SelfContainedNode {
    // 这个方法应该被子类重写
    throw new Error('createServerInstance must be implemented by subclass');
  }

  /**
   * 验证节点配置
   */
  validateConfig(config: any): { valid: boolean; errors: string[] } {
    // 默认验证逻辑，子类可以重写
    return { valid: true, errors: [] };
  }

  /**
   * 获取节点的默认配置
   */
  getDefaultConfig(): any {
    return {};
  }

  /**
   * 获取节点的引脚定义
   */
  getPinDefinitions(): { inputs: any[]; outputs: any[] } {
    return {
      inputs: [],
      outputs: []
    };
  }

  /**
   * 处理节点配置更新
   */
  onConfigUpdate(newConfig: any): void {
    // 默认实现：更新配置
    this.updateConfig(newConfig);
    
    // 触发配置更新事件
    this.emit('configUpdated', newConfig);
  }

  /**
   * 处理节点启动
   */
  async onNodeStart(): Promise<void> {
    console.log(`🚀 Starting self-contained node: ${this.type}`);
    
    // 子类可以重写此方法来实现特定的启动逻辑
  }

  /**
   * 处理节点停止
   */
  async onNodeStop(): Promise<void> {
    console.log(`🛑 Stopping self-contained node: ${this.type}`);
    
    // 子类可以重写此方法来实现特定的停止逻辑
  }

  /**
   * 处理节点重置
   */
  async onNodeReset(): Promise<void> {
    console.log(`🔄 Resetting self-contained node: ${this.type}`);
    
    // 重置节点状态
    this.status = 'idle' as any;
    
    // 清空输入输出数据
    this.clearInputData();
    this.clearOutputData();
    
    // 子类可以重写此方法来实现特定的重置逻辑
  }

  /**
   * 获取节点状态信息
   */
  getStatusInfo(): any {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      status: this.status,
      executionMode: this.executionMode,
      isThreaded: this.isThreaded,
      inputCount: this.inputs.length,
      outputCount: this.outputs.length,
      config: this.config
    };
  }

  /**
   * 获取节点性能指标
   */
  getPerformanceMetrics(): any {
    return {
      nodeId: this.id,
      type: this.type,
      executionCount: 0, // 子类可以跟踪执行次数
      averageExecutionTime: 0, // 子类可以跟踪平均执行时间
      lastExecutionTime: null, // 子类可以跟踪最后执行时间
      errorCount: 0, // 子类可以跟踪错误次数
      memoryUsage: process.memoryUsage ? process.memoryUsage() : null
    };
  }

  /**
   * 处理错误
   */
  protected handleError(error: Error, context: string): void {
    console.error(`❌ Error in self-contained node ${this.type} (${context}):`, error);
    
    // 更新节点状态
    this.status = 'error' as any;
    
    // 触发错误事件
    this.emit('error', { error, context, nodeId: this.id, nodeType: this.type });
  }

  /**
   * 记录调试信息
   */
  protected debug(message: string, data?: any): void {
    console.log(`🔍 [${this.type}:${this.id}] ${message}`, data || '');
  }

  /**
   * 记录信息
   */
  protected info(message: string, data?: any): void {
    console.log(`ℹ️ [${this.type}:${this.id}] ${message}`, data || '');
  }

  /**
   * 记录警告
   */
  protected warn(message: string, data?: any): void {
    console.warn(`⚠️ [${this.type}:${this.id}] ${message}`, data || '');
  }

  /**
   * 清空输入数据
   */
  private clearInputData(): void {
    this.inputs.forEach(pin => {
      this.setInputData(pin.id, undefined);
    });
  }

  /**
   * 清空输出数据
   */
  private clearOutputData(): void {
    this.outputs.forEach(pin => {
      this.setOutputData(pin.id, undefined);
    });
  }

  /**
   * 线程启动时的自包含节点特定初始化
   */
  protected async onThreadStart(): Promise<void> {
    await super.onThreadStart();
    
    // 调用节点启动逻辑
    await this.onNodeStart();
    
    this.info('Thread started for self-contained node');
  }

  /**
   * 线程停止时的自包含节点特定清理
   */
  protected async onThreadStop(): Promise<void> {
    await super.onThreadStop();
    
    // 调用节点停止逻辑
    await this.onNodeStop();
    
    this.info('Thread stopped for self-contained node');
  }

  /**
   * 销毁节点时的清理
   */
  destroy(): void {
    this.info('Destroying self-contained node');
    
    // 停止节点
    this.onNodeStop().catch(error => {
      this.handleError(error, 'destroy');
    });
    
    // 调用父类销毁方法
    super.destroy();
  }
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  version: string;
  author: string;
  tags: string[];
  defaultConfig: any;
  pins: {
    inputs: any[];
    outputs: any[];
  };
}

/**
 * 自包含节点装饰器
 * 用于标记和配置自包含节点
 */
export function SelfContainedNodeDecorator(nodeInfo: Partial<NodeInfo>) {
  return function <T extends { new (...args: any[]): SelfContainedNode }>(constructor: T) {
    // 将节点信息附加到构造函数
    (constructor as any).nodeInfo = nodeInfo;
    
    // 重写 getServerNodeInfo 静态方法
    (constructor as any).getServerNodeInfo = function() {
      return {
        type: 'unknown',
        name: '未知节点',
        description: '节点描述',
        category: '自定义',
        icon: 'settings',
        version: '1.0.0',
        author: 'System',
        tags: ['self-contained'],
        defaultConfig: {},
        pins: {
          inputs: [],
          outputs: []
        },
        ...nodeInfo
      };
    };
    
    return constructor;
  };
}
