'use client';

import React, { useState } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData, NodeStatus } from '../BaseNode';
import { FileText, Plus, Trash2 } from 'lucide-react';

// 表单字段接口
interface FormField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox';
  label: string;
  required: boolean;
  placeholder?: string;
  options?: string[];
}

// React组件：表单录入UI
interface FormInputUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const FormInputUI: React.FC<FormInputUIProps> = ({ nodeId, config, status, onAction }) => {
  const [formData, setFormData] = useState<{ [key: string]: any }>({});
  const [errors, setErrors] = useState<string[]>([]);

  const fields = config.fields || [];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证表单
    const newErrors: string[] = [];
    fields.forEach((field: FormField) => {
      if (field.required && (!formData[field.name] || formData[field.name] === '')) {
        newErrors.push(`${field.label} 是必填字段`);
      }
    });

    if (newErrors.length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors([]);
    onAction('formSubmit', formData);
  };

  const handleCancel = () => {
    onAction('formCancel');
  };

  const renderField = (field: FormField) => {
    const value = formData[field.name] || '';
    
    switch (field.type) {
      case 'textarea':
        return (
          <textarea
            key={field.id}
            name={field.name}
            value={value}
            onChange={(e) => setFormData(prev => ({ ...prev, [field.name]: e.target.value }))}
            placeholder={field.placeholder}
            required={field.required}
            rows={4}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        );
      
      case 'select':
        return (
          <select
            key={field.id}
            name={field.name}
            value={value}
            onChange={(e) => setFormData(prev => ({ ...prev, [field.name]: e.target.value }))}
            required={field.required}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">请选择...</option>
            {field.options?.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        );
      
      case 'checkbox':
        return (
          <label key={field.id} className="flex items-center space-x-2">
            <input
              type="checkbox"
              name={field.name}
              checked={value === true}
              onChange={(e) => setFormData(prev => ({ ...prev, [field.name]: e.target.checked }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span>{field.label}{field.required && ' *'}</span>
          </label>
        );
      
      default:
        return (
          <input
            key={field.id}
            type={field.type}
            name={field.name}
            value={value}
            onChange={(e) => setFormData(prev => ({ ...prev, [field.name]: e.target.value }))}
            placeholder={field.placeholder}
            required={field.required}
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        );
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-xl shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {config.title || '请填写表单'}
        </h2>
        <p className="text-gray-600">
          {config.description || '请填写以下信息'}
        </p>
      </div>

      {errors.length > 0 && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          {errors.map((error, index) => (
            <p key={index} className="text-red-600 text-sm">{error}</p>
          ))}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {fields.map((field: FormField) => (
          <div key={field.id}>
            {field.type !== 'checkbox' && (
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {field.label}{field.required && ' *'}
              </label>
            )}
            {renderField(field)}
          </div>
        ))}

        <div className="flex space-x-3 pt-4">
          <button
            type="button"
            onClick={handleCancel}
            className="flex-1 py-3 px-4 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            className="flex-1 py-3 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            提交
          </button>
        </div>
      </form>
    </div>
  );
};

// React组件：表单录入节点图标
interface FormInputIconProps {
  nodeId: string;
  status: string;
  isWaiting: boolean;
}

const FormInputIcon: React.FC<FormInputIconProps> = ({ nodeId, status, isWaiting }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isWaiting ? 'animate-pulse' : ''}
    `}>
      <FileText size={16} />
    </div>
  );
};

// React组件：表单录入节点属性面板
interface FormInputPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const FormInputProperties: React.FC<FormInputPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  const [fields, setFields] = useState<FormField[]>(config.fields || []);

  const addField = () => {
    const newField: FormField = {
      id: `field_${Date.now()}`,
      name: `field_${fields.length + 1}`,
      type: 'text',
      label: `字段 ${fields.length + 1}`,
      required: false,
      placeholder: ''
    };
    const newFields = [...fields, newField];
    setFields(newFields);
    onConfigChange('fields', newFields);
  };

  const removeField = (fieldId: string) => {
    const newFields = fields.filter(f => f.id !== fieldId);
    setFields(newFields);
    onConfigChange('fields', newFields);
  };

  const updateField = (fieldId: string, updates: Partial<FormField>) => {
    const newFields = fields.map(f => 
      f.id === fieldId ? { ...f, ...updates } : f
    );
    setFields(newFields);
    onConfigChange('fields', newFields);
  };

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          表单标题
        </label>
        <input
          type="text"
          value={config.title || ''}
          onChange={(e) => onConfigChange('title', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          表单描述
        </label>
        <textarea
          value={config.description || ''}
          onChange={(e) => onConfigChange('description', e.target.value)}
          rows={2}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            表单字段
          </label>
          <button
            onClick={addField}
            className="flex items-center space-x-1 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            <Plus size={12} />
            <span>添加字段</span>
          </button>
        </div>
        
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {fields.map((field) => (
            <div key={field.id} className="p-3 bg-white border border-gray-200 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <input
                  type="text"
                  value={field.label}
                  onChange={(e) => updateField(field.id, { label: e.target.value })}
                  className="flex-1 p-1 border border-gray-300 rounded text-sm mr-2"
                  placeholder="字段标签"
                />
                <button
                  onClick={() => removeField(field.id)}
                  className="p-1 text-red-500 hover:text-red-700"
                >
                  <Trash2 size={14} />
                </button>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <select
                  value={field.type}
                  onChange={(e) => updateField(field.id, { type: e.target.value as any })}
                  className="p-1 border border-gray-300 rounded text-xs"
                >
                  <option value="text">文本</option>
                  <option value="number">数字</option>
                  <option value="email">邮箱</option>
                  <option value="textarea">多行文本</option>
                  <option value="select">下拉选择</option>
                  <option value="checkbox">复选框</option>
                </select>
                <label className="flex items-center space-x-1">
                  <input
                    type="checkbox"
                    checked={field.required}
                    onChange={(e) => updateField(field.id, { required: e.target.checked })}
                    className="rounded border-gray-300"
                  />
                  <span className="text-xs">必填</span>
                </label>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// 表单录入节点类
export class ReactFormInputNode extends ReactBaseNode {
  private isWaitingForInput: boolean = false;

  constructor() {
    super('react-form-input', '表单录入节点');
    this.description = '用户表单输入节点，工作流运行时弹出表单供用户填写';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚
    this.addInputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发表单显示'
    });

    // 输出引脚
    this.addOutputPin({
      name: 'formData',
      dataType: 'object',
      description: '用户填写的表单数据'
    });

    this.addOutputPin({
      name: 'completed',
      dataType: 'boolean',
      description: '表单是否完成填写'
    });

    // 默认表单配置
    this.config = {
      title: '请填写表单',
      description: '请填写以下信息',
      fields: [
        {
          id: 'name',
          name: 'name',
          type: 'text',
          label: '姓名',
          required: true,
          placeholder: '请输入您的姓名'
        },
        {
          id: 'email',
          name: 'email',
          type: 'email',
          label: '邮箱',
          required: true,
          placeholder: '请输入您的邮箱地址'
        }
      ]
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    // 设置等待用户输入状态
    this.status = NodeStatus.WAITING;
    this.isWaitingForInput = true;
    this.emit('statusChanged', this.status);

    // 显示表单界面
    this.emit('showForm', {
      nodeId: this.id,
      formConfig: this.config
    });

    // 等待用户输入
    return new Promise((resolve) => {
      this.once('formSubmitted', (data) => {
        this.isWaitingForInput = false;
        resolve({
          formData: data,
          completed: true
        });
      });
    });
  }

  // 处理表单提交
  submitForm(data: { [key: string]: any }): void {
    if (!this.isWaitingForInput) {
      console.warn(`Node ${this.id} is not waiting for input`);
      return;
    }

    console.log(`Form submitted for node ${this.id}:`, data);
    this.isWaitingForInput = false;
    this.status = NodeStatus.RUNNING;
    this.emit('statusChanged', this.status);
    this.emit('formSubmitted', data);
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(FormInputUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = { ...this.getIconProps(), isWaiting: this.isWaitingForInput };
    return React.createElement(FormInputIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(FormInputProperties, props);
  }
}

// 导出组件供外部使用
export { FormInputUI, FormInputIcon, FormInputProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-form-input',
  name: '表单录入节点 (React)',
  description: '用户表单输入节点',
  category: '交互',
  icon: 'file-text',
  version: '2.0.0',
  author: 'System',
  tags: ['form', 'input', 'user', 'interaction', 'react']
};
