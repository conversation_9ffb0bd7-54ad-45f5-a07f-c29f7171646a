import { SelfContainedNode } from '../SelfContainedNode';
import { NodeData, ExecutionMode } from '../BaseNode';

/**
 * 服务器端计时器节点 - 完全独立的服务器端实现
 * 不包含任何React组件，专门用于服务器端执行
 */
export class ServerTimerNode extends SelfContainedNode {
  private timer: NodeJS.Timeout | null = null;
  private startTime: number = 0;
  private isRunning: boolean = false;

  constructor() {
    super('react-timer', '计时器节点');
    this.description = '可配置的计时器节点，用于延时和定时操作';
    
    // 计时器节点使用混合执行模式
    this.executionMode = ExecutionMode.HYBRID;
    
    this.initialize();
  }

  initialize(): void {
    // 添加输入引脚
    this.addInputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发计时器',
      required: false
    });

    this.addInputPin({
      name: 'reset',
      dataType: 'any',
      description: '重置计时器',
      required: false
    });

    // 添加输出引脚
    this.addOutputPin({
      name: 'completed',
      dataType: 'boolean',
      description: '计时完成信号'
    });

    this.addOutputPin({
      name: 'elapsed',
      dataType: 'number',
      description: '已经过的时间'
    });

    // 默认配置
    this.config = {
      duration: 10,
      autoStart: false,
      repeat: false,
      ...this.config
    };
  }

  /**
   * 前端处理逻辑
   */
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    const duration = this.config.duration || 10;
    const autoStart = this.config.autoStart || false;
    const repeat = this.config.repeat || false;
    
    this.info('计时器节点前端处理开始', { duration, autoStart, repeat });
    
    // 如果计时时长 <= 30秒，在前端处理
    if (duration <= 30) {
      return this.executeTimer(duration, autoStart, repeat, 'frontend');
    }
    
    // 计时时长 > 30秒，转到后端处理
    this.info('计时时长过长，转到后端处理');
    return this.processBackend(inputData);
  }

  /**
   * 后端处理逻辑
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    const duration = this.config.duration || 10;
    const autoStart = this.config.autoStart || false;
    const repeat = this.config.repeat || false;
    
    this.info('计时器节点后端处理开始', { duration, autoStart, repeat });
    
    return this.executeTimer(duration, autoStart, repeat, 'backend');
  }

  /**
   * 执行计时器逻辑
   */
  private async executeTimer(duration: number, autoStart: boolean, repeat: boolean, mode: string): Promise<NodeData> {
    this.info(`${mode}模式执行计时器`, { duration, autoStart, repeat });
    
    // 检查是否有触发信号或自动开始
    const hasTriggerInput = this.inputData.has('trigger') && this.inputData.get('trigger') !== undefined;
    const shouldStart = autoStart || hasTriggerInput;

    if (!shouldStart) {
      this.info('计时器未触发，返回初始状态');
      return {
        completed: false,
        elapsed: 0,
        isRunning: false,
        timestamp: new Date().toISOString()
      };
    }

    // 检查是否有重置信号
    const hasResetInput = this.inputData.has('reset') && this.inputData.get('reset') !== undefined;
    if (hasResetInput) {
      this.info('收到重置信号，重置计时器');
      this.resetTimer();
      return {
        completed: false,
        elapsed: 0,
        isRunning: false,
        timestamp: new Date().toISOString()
      };
    }
    
    // 开始计时
    this.startTimer();
    
    try {
      // 等待计时完成
      await new Promise<void>((resolve, reject) => {
        this.timer = setTimeout(() => {
          this.info(`计时器完成: ${duration}秒`);
          this.isRunning = false;
          resolve();
        }, duration * 1000);
      });
      
      const result = {
        completed: true,
        elapsed: duration,
        isRunning: false,
        timestamp: new Date().toISOString()
      };
      
      this.info(`${mode}模式计时器执行完成`, result);
      
      // 如果是重复模式，可以在这里实现重复逻辑
      if (repeat) {
        this.info('重复模式已启用，但当前实现为单次执行');
      }
      
      return result;
      
    } catch (error) {
      this.handleError(error as Error, `${mode}模式计时器执行`);
      return {
        completed: false,
        elapsed: this.getElapsedTime(),
        isRunning: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 启动计时器
   */
  private startTimer(): void {
    this.startTime = Date.now();
    this.isRunning = true;
    this.info('计时器已启动');
  }

  /**
   * 重置计时器
   */
  private resetTimer(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.startTime = 0;
    this.isRunning = false;
    this.info('计时器已重置');
  }

  /**
   * 获取已经过的时间
   */
  private getElapsedTime(): number {
    if (!this.isRunning || this.startTime === 0) {
      return 0;
    }
    return Math.floor((Date.now() - this.startTime) / 1000);
  }

  /**
   * 判断是否应该使用后端处理
   */
  shouldUseBackend(inputData: NodeData): boolean {
    const duration = this.config.duration || 10;
    
    // 计时时长 > 30秒时使用后端处理
    return duration > 30;
  }

  /**
   * 获取服务器端节点信息
   */
  static getServerNodeInfo(): any {
    return {
      type: 'react-timer',
      name: '计时器节点',
      description: '可配置的计时器节点，用于延时和定时操作',
      category: '自定义',
      icon: 'clock',
      version: '2.0.0',
      author: 'System',
      tags: ['utility', 'timer', 'delay'],
      defaultConfig: {
        duration: 10,
        autoStart: false,
        repeat: false
      },
      pins: {
        inputs: [
          { name: 'trigger', dataType: 'any', description: '触发计时器', required: false },
          { name: 'reset', dataType: 'any', description: '重置计时器', required: false }
        ],
        outputs: [
          { name: 'completed', dataType: 'boolean', description: '计时完成信号' },
          { name: 'elapsed', dataType: 'number', description: '已经过的时间' }
        ]
      }
    };
  }

  /**
   * 创建服务器端实例
   */
  static createServerInstance(config: any = {}): ServerTimerNode {
    const instance = new ServerTimerNode();
    instance.updateConfig(config);
    return instance;
  }

  /**
   * 验证节点配置
   */
  validateConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (config.duration !== undefined) {
      if (typeof config.duration !== 'number' || config.duration <= 0) {
        errors.push('计时时长必须是正数');
      }
      if (config.duration > 3600) {
        errors.push('计时时长不能超过3600秒（1小时）');
      }
    }
    
    if (config.autoStart !== undefined && typeof config.autoStart !== 'boolean') {
      errors.push('自动开始必须是布尔值');
    }
    
    if (config.repeat !== undefined && typeof config.repeat !== 'boolean') {
      errors.push('重复计时必须是布尔值');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取节点的默认配置
   */
  getDefaultConfig(): any {
    return {
      duration: 10,
      autoStart: false,
      repeat: false
    };
  }

  /**
   * 获取节点的引脚定义
   */
  getPinDefinitions(): { inputs: any[]; outputs: any[] } {
    return {
      inputs: [
        { name: 'trigger', dataType: 'any', description: '触发计时器', required: false },
        { name: 'reset', dataType: 'any', description: '重置计时器', required: false }
      ],
      outputs: [
        { name: 'completed', dataType: 'boolean', description: '计时完成信号' },
        { name: 'elapsed', dataType: 'number', description: '已经过的时间' }
      ]
    };
  }

  /**
   * 处理节点启动
   */
  async onNodeStart(): Promise<void> {
    this.info('计时器节点启动');
    this.resetTimer();
  }

  /**
   * 处理节点停止
   */
  async onNodeStop(): Promise<void> {
    this.info('计时器节点停止');
    this.resetTimer();
  }

  /**
   * 处理节点重置
   */
  async onNodeReset(): Promise<void> {
    this.info('计时器节点重置');
    this.resetTimer();
    await super.onNodeReset();
  }

  /**
   * 获取节点状态信息
   */
  getStatusInfo(): any {
    return {
      ...super.getStatusInfo(),
      duration: this.config.duration,
      autoStart: this.config.autoStart,
      repeat: this.config.repeat,
      isRunning: this.isRunning,
      elapsed: this.getElapsedTime()
    };
  }

  /**
   * 销毁节点时的清理
   */
  destroy(): void {
    this.resetTimer();
    super.destroy();
  }
}

// 默认导出
export default ServerTimerNode;
