import { SelfContainedNode } from '../SelfContainedNode';
import { NodeData, ExecutionMode } from '../BaseNode';

/**
 * 服务器端开始节点 - 完全独立的服务器端实现
 * 不包含任何React组件，专门用于服务器端执行
 */
export class ServerStartNode extends SelfContainedNode {
  constructor() {
    super('react-start', '开始节点');
    this.description = '工作流的起始点，触发整个工作流的执行';
    
    // 开始节点默认使用前端执行模式（轻量级）
    this.executionMode = ExecutionMode.FRONTEND_ONLY;
    
    this.initialize();
  }

  initialize(): void {
    // 开始节点只有输出，没有输入
    this.addOutputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发信号'
    });

    this.addOutputPin({
      name: 'timestamp',
      dataType: 'string',
      description: '开始时间戳'
    });

    // 默认配置
    this.config = {
      autoStart: false,
      delay: 0,
      ...this.config
    };
  }

  /**
   * 前端处理逻辑
   */
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    const { delay = 0 } = this.config;
    
    this.info('开始节点前端处理开始', { delay });
    
    // 如果延迟时间 <= 5秒，在前端处理
    if (delay <= 5) {
      if (delay > 0) {
        this.info(`前端延迟 ${delay} 秒`);
        await new Promise(resolve => setTimeout(resolve, delay * 1000));
      }
      
      const result = {
        trigger: true,
        timestamp: new Date().toISOString()
      };
      
      this.info('开始节点前端处理完成', result);
      return result;
    }
    
    // 延迟时间 > 5秒，转到后端处理
    this.info('延迟时间过长，转到后端处理');
    return this.processBackend(inputData);
  }

  /**
   * 后端处理逻辑
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    const { delay = 0 } = this.config;
    
    this.info('开始节点后端处理开始', { delay });
    
    if (delay > 0) {
      this.info(`后端延迟 ${delay} 秒`);
      await new Promise(resolve => setTimeout(resolve, delay * 1000));
    }
    
    const result = {
      trigger: true,
      timestamp: new Date().toISOString()
    };
    
    this.info('开始节点后端处理完成', result);
    return result;
  }

  /**
   * 判断是否应该使用后端处理
   */
  shouldUseBackend(inputData: NodeData): boolean {
    const { delay = 0 } = this.config;
    
    // 延迟时间 > 5秒时使用后端处理
    return delay > 5;
  }

  /**
   * 获取服务器端节点信息
   */
  static getServerNodeInfo(): any {
    return {
      type: 'react-start',
      name: '开始节点',
      description: '工作流的起始点，触发整个工作流的执行',
      category: '控制',
      icon: 'play',
      version: '2.0.0',
      author: 'System',
      tags: ['control', 'start', 'trigger'],
      defaultConfig: {
        autoStart: false,
        delay: 0
      },
      pins: {
        inputs: [], // 开始节点没有输入
        outputs: [
          { name: 'trigger', dataType: 'any', description: '触发信号' },
          { name: 'timestamp', dataType: 'string', description: '开始时间戳' }
        ]
      }
    };
  }

  /**
   * 创建服务器端实例
   */
  static createServerInstance(config: any = {}): ServerStartNode {
    const instance = new ServerStartNode();
    instance.updateConfig(config);
    return instance;
  }

  /**
   * 验证节点配置
   */
  validateConfig(config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (config.delay !== undefined) {
      if (typeof config.delay !== 'number' || config.delay < 0) {
        errors.push('延迟时间必须是非负数');
      }
      if (config.delay > 300) {
        errors.push('延迟时间不能超过300秒');
      }
    }
    
    if (config.autoStart !== undefined && typeof config.autoStart !== 'boolean') {
      errors.push('自动开始必须是布尔值');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取节点的默认配置
   */
  getDefaultConfig(): any {
    return {
      autoStart: false,
      delay: 0
    };
  }

  /**
   * 获取节点的引脚定义
   */
  getPinDefinitions(): { inputs: any[]; outputs: any[] } {
    return {
      inputs: [],
      outputs: [
        { name: 'trigger', dataType: 'any', description: '触发信号' },
        { name: 'timestamp', dataType: 'string', description: '开始时间戳' }
      ]
    };
  }

  /**
   * 处理节点启动
   */
  async onNodeStart(): Promise<void> {
    this.info('开始节点启动');
    
    const { autoStart } = this.config;
    if (autoStart) {
      this.info('自动开始已启用，准备触发');
      // 可以在这里实现自动触发逻辑
    }
  }

  /**
   * 处理节点停止
   */
  async onNodeStop(): Promise<void> {
    this.info('开始节点停止');
  }

  /**
   * 处理节点重置
   */
  async onNodeReset(): Promise<void> {
    this.info('开始节点重置');
    await super.onNodeReset();
  }

  /**
   * 获取节点状态信息
   */
  getStatusInfo(): any {
    return {
      ...super.getStatusInfo(),
      autoStart: this.config.autoStart,
      delay: this.config.delay,
      executionMode: this.executionMode
    };
  }

  /**
   * 获取节点性能指标
   */
  getPerformanceMetrics(): any {
    return {
      ...super.getPerformanceMetrics(),
      configuredDelay: this.config.delay,
      autoStartEnabled: this.config.autoStart
    };
  }
}

// 默认导出
export default ServerStartNode;
