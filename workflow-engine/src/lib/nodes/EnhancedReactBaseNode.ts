/**
 * 增强的React基础节点
 * 
 * 支持前端工作流引擎的协同工作模式：
 * 1. 节点设计师可以自由选择前端/后端执行
 * 2. 前端处理UI和轻量级逻辑
 * 3. 后端处理重量级计算和敏感操作
 * 4. 无缝的前后端协同调用
 */

import { ReactBaseNode } from './ReactBaseNode';
import { NodeData, ExecutionMode } from './BaseNode';
import { BackendCallRequest, BackendCallResponse } from '../core/FrontendWorkflowEngine';

// 后端方法装饰器
export function BackendMethod(options?: { timeout?: number; priority?: 'low' | 'normal' | 'high' }) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // 标记方法为后端方法
    if (!target._backendMethods) {
      target._backendMethods = new Map();
    }
    target._backendMethods.set(propertyKey, {
      timeout: options?.timeout || 30000,
      priority: options?.priority || 'normal'
    });
  };
}

// 前端方法装饰器
export function FrontendMethod() {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // 标记方法为前端方法
    if (!target._frontendMethods) {
      target._frontendMethods = new Set();
    }
    target._frontendMethods.add(propertyKey);
  };
}

/**
 * 增强的React基础节点类
 */
export abstract class EnhancedReactBaseNode extends ReactBaseNode {
  // 后端方法注册表
  private _backendMethods: Map<string, { timeout: number; priority: string }> = new Map();
  private _frontendMethods: Set<string> = new Set();
  
  // 输出数据存储
  private outputData: Map<string, any> = new Map();
  
  // 后端调用缓存
  private backendCallCache: Map<string, { result: any; timestamp: number; ttl: number }> = new Map();

  constructor(type: string, name?: string) {
    super(type, name);
    
    // 从原型链复制方法注册信息
    this.copyMethodRegistrations();
    
    // 设置默认执行模式为混合模式
    this.executionMode = ExecutionMode.HYBRID;
  }

  /**
   * 从原型链复制方法注册信息
   */
  private copyMethodRegistrations(): void {
    let proto = Object.getPrototypeOf(this);
    while (proto && proto !== EnhancedReactBaseNode.prototype) {
      if (proto._backendMethods) {
        for (const [method, config] of proto._backendMethods) {
          this._backendMethods.set(method, config);
        }
      }
      if (proto._frontendMethods) {
        for (const method of proto._frontendMethods) {
          this._frontendMethods.add(method);
        }
      }
      proto = Object.getPrototypeOf(proto);
    }
  }

  /**
   * 调用后端方法
   */
  protected async callBackend<T = any>(
    methodName: string, 
    inputData: any, 
    options?: { 
      cache?: boolean; 
      cacheTTL?: number; 
      timeout?: number;
      priority?: 'low' | 'normal' | 'high';
    }
  ): Promise<T> {
    // 检查缓存
    if (options?.cache) {
      const cached = this.getFromCache(methodName, inputData);
      if (cached) {
        console.log(`📦 Using cached result for ${this.id}.${methodName}`);
        return cached;
      }
    }

    // 获取方法配置
    const methodConfig = this._backendMethods.get(methodName) || {
      timeout: options?.timeout || 30000,
      priority: options?.priority || 'normal'
    };

    // 创建后端调用请求
    const request: BackendCallRequest = {
      nodeId: this.id,
      method: methodName,
      inputData,
      context: {
        workflowId: this.getWorkflowId(),
        nodeId: this.id,
        inputData,
        executionMode: this.executionMode,
        timestamp: Date.now()
      }
    };

    try {
      console.log(`🔄 Calling backend method: ${this.id}.${methodName}`);
      
      // 发出后端调用请求事件
      this.emit('backendCallRequested', request);
      
      // 等待后端调用完成
      const response = await this.waitForBackendCall(request, methodConfig.timeout);
      
      if (!response.success) {
        throw new Error(response.error || 'Backend call failed');
      }

      // 缓存结果
      if (options?.cache) {
        this.cacheResult(methodName, inputData, response.result, options.cacheTTL || 300000); // 默认5分钟
      }

      console.log(`✅ Backend call completed: ${this.id}.${methodName} (${response.executionTime}ms)`);
      return response.result;

    } catch (error) {
      console.error(`❌ Backend call failed: ${this.id}.${methodName}`, error);
      throw error;
    }
  }

  /**
   * 等待后端调用完成
   */
  private waitForBackendCall(request: BackendCallRequest, timeout: number): Promise<BackendCallResponse> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.removeAllListeners('backendCallCompleted');
        this.removeAllListeners('backendCallError');
        reject(new Error(`Backend call timeout: ${request.method}`));
      }, timeout);

      // 监听完成事件
      const onCompleted = (event: { request: BackendCallRequest; response: BackendCallResponse }) => {
        if (event.request.method === request.method) {
          clearTimeout(timeoutId);
          this.removeAllListeners('backendCallCompleted');
          this.removeAllListeners('backendCallError');
          resolve(event.response);
        }
      };

      // 监听错误事件
      const onError = (event: { request: BackendCallRequest; error: any }) => {
        if (event.request.method === request.method) {
          clearTimeout(timeoutId);
          this.removeAllListeners('backendCallCompleted');
          this.removeAllListeners('backendCallError');
          reject(event.error);
        }
      };

      this.on('backendCallCompleted', onCompleted);
      this.on('backendCallError', onError);
    });
  }

  /**
   * 获取缓存结果
   */
  private getFromCache(methodName: string, inputData: any): any | null {
    const cacheKey = this.generateCacheKey(methodName, inputData);
    const cached = this.backendCallCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.result;
    }
    
    // 清理过期缓存
    if (cached) {
      this.backendCallCache.delete(cacheKey);
    }
    
    return null;
  }

  /**
   * 缓存结果
   */
  private cacheResult(methodName: string, inputData: any, result: any, ttl: number): void {
    const cacheKey = this.generateCacheKey(methodName, inputData);
    this.backendCallCache.set(cacheKey, {
      result,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(methodName: string, inputData: any): string {
    const dataHash = JSON.stringify(inputData);
    return `${this.id}_${methodName}_${btoa(dataHash).slice(0, 16)}`;
  }

  /**
   * 获取工作流ID
   */
  private getWorkflowId(): string {
    // 从上下文或全局状态获取工作流ID
    // 这里需要根据实际实现调整
    return 'current_workflow';
  }

  /**
   * 设置输出数据
   */
  setOutputData(data: any, pinId: string = 'default'): void {
    this.outputData.set(pinId, data);
    this.emit('outputDataChanged', { pinId, data });
  }

  /**
   * 获取输出数据
   */
  getOutputData(pinId: string = 'default'): any {
    return this.outputData.get(pinId);
  }

  /**
   * 获取所有输出数据
   */
  getAllOutputData(): Map<string, any> {
    return new Map(this.outputData);
  }

  /**
   * 清理输出数据
   */
  clearOutputData(): void {
    this.outputData.clear();
    this.emit('outputDataCleared');
  }

  /**
   * 检查是否应该使用后端处理
   * 子类可以重写此方法来实现自定义逻辑
   */
  shouldUseBackend(inputData: NodeData): boolean {
    // 默认实现：检查是否有后端方法被调用
    return this._backendMethods.size > 0;
  }

  /**
   * 前端处理方法 - 子类必须实现
   */
  abstract processFrontend(inputData: NodeData): Promise<NodeData>;

  /**
   * 后端处理方法 - 子类可以选择实现
   */
  async processBackend(inputData: NodeData): Promise<NodeData> {
    // 默认实现：如果没有后端方法，返回输入数据
    if (this._backendMethods.size === 0) {
      return inputData;
    }
    
    // 如果有后端方法，子类应该重写此方法
    throw new Error(`Node ${this.id} has backend methods but processBackend is not implemented`);
  }

  /**
   * 获取后端方法列表
   */
  getBackendMethods(): string[] {
    return Array.from(this._backendMethods.keys());
  }

  /**
   * 获取前端方法列表
   */
  getFrontendMethods(): string[] {
    return Array.from(this._frontendMethods);
  }

  /**
   * 检查方法是否为后端方法
   */
  isBackendMethod(methodName: string): boolean {
    return this._backendMethods.has(methodName);
  }

  /**
   * 检查方法是否为前端方法
   */
  isFrontendMethod(methodName: string): boolean {
    return this._frontendMethods.has(methodName);
  }

  /**
   * 获取节点执行能力信息
   */
  getExecutionCapabilities() {
    return {
      executionMode: this.executionMode,
      backendMethods: this.getBackendMethods(),
      frontendMethods: this.getFrontendMethods(),
      supportsCache: true,
      supportsPriority: true
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 清理缓存
    this.backendCallCache.clear();
    
    // 清理输出数据
    this.clearOutputData();
    
    // 调用父类清理方法
    super.dispose?.();
  }
}

/**
 * 工具函数：创建混合执行节点
 */
export function createHybridNode<T extends EnhancedReactBaseNode>(
  NodeClass: new (...args: any[]) => T,
  config: {
    frontendMethods?: string[];
    backendMethods?: { [key: string]: { timeout?: number; priority?: 'low' | 'normal' | 'high' } };
  }
) {
  return class extends NodeClass {
    constructor(...args: any[]) {
      super(...args);
      
      // 注册前端方法
      if (config.frontendMethods) {
        for (const method of config.frontendMethods) {
          this._frontendMethods.add(method);
        }
      }
      
      // 注册后端方法
      if (config.backendMethods) {
        for (const [method, options] of Object.entries(config.backendMethods)) {
          this._backendMethods.set(method, {
            timeout: options.timeout || 30000,
            priority: options.priority || 'normal'
          });
        }
      }
    }
  };
}

export default EnhancedReactBaseNode;
