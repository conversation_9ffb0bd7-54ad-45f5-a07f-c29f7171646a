'use client';

import React, { useState, useEffect } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData, NodeStatus } from '../BaseNode';
import { Calculator, Filter, ArrowUpDown, BarChart3, Plus, Trash2 } from 'lucide-react';

// 数据处理操作类型
type ProcessingOperation = 'filter' | 'map' | 'reduce' | 'sort' | 'group' | 'aggregate';

// 处理规则接口
interface ProcessingRule {
  id: string;
  operation: ProcessingOperation;
  field: string;
  condition?: string;
  value?: any;
  enabled: boolean;
}

// React组件：数据处理器UI
interface DataProcessorUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const DataProcessorUI: React.FC<DataProcessorUIProps> = ({ nodeId, config, status, onAction }) => {
  const [inputData, setInputData] = useState<any[]>([]);
  const [outputData, setOutputData] = useState<any[]>([]);
  const [processing, setProcessing] = useState(false);
  const [stats, setStats] = useState<any>({});

  const rules = config.rules || [];

  // 模拟数据处理
  const processData = async (data: any[]) => {
    setProcessing(true);
    onAction('processingStarted', { inputCount: data.length });

    try {
      let result = [...data];
      
      for (const rule of rules.filter(r => r.enabled)) {
        switch (rule.operation) {
          case 'filter':
            result = result.filter(item => {
              const fieldValue = item[rule.field];
              switch (rule.condition) {
                case 'equals': return fieldValue === rule.value;
                case 'contains': return String(fieldValue).includes(rule.value);
                case 'greater': return Number(fieldValue) > Number(rule.value);
                case 'less': return Number(fieldValue) < Number(rule.value);
                default: return true;
              }
            });
            break;
            
          case 'map':
            result = result.map(item => ({
              ...item,
              [rule.field]: rule.value
            }));
            break;
            
          case 'sort':
            result.sort((a, b) => {
              const aVal = a[rule.field];
              const bVal = b[rule.field];
              if (rule.value === 'desc') {
                return bVal > aVal ? 1 : -1;
              }
              return aVal > bVal ? 1 : -1;
            });
            break;
            
          case 'group':
            const grouped = result.reduce((acc, item) => {
              const key = item[rule.field];
              if (!acc[key]) acc[key] = [];
              acc[key].push(item);
              return acc;
            }, {});
            result = Object.entries(grouped).map(([key, items]) => ({
              [rule.field]: key,
              items,
              count: (items as any[]).length
            }));
            break;
        }
      }

      setOutputData(result);
      
      // 计算统计信息
      const newStats = {
        inputCount: data.length,
        outputCount: result.length,
        processingTime: Math.random() * 100 + 50, // 模拟处理时间
        rulesApplied: rules.filter(r => r.enabled).length
      };
      setStats(newStats);
      
      onAction('processingCompleted', { 
        inputCount: data.length, 
        outputCount: result.length,
        stats: newStats
      });
      
    } catch (error) {
      onAction('processingError', { error: error.message });
    } finally {
      setProcessing(false);
    }
  };

  // 生成示例数据
  const generateSampleData = () => {
    const sampleData = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      name: `Item ${i + 1}`,
      value: Math.floor(Math.random() * 100),
      category: ['A', 'B', 'C'][Math.floor(Math.random() * 3)],
      active: Math.random() > 0.5,
      timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString()
    }));
    
    setInputData(sampleData);
    processData(sampleData);
  };

  const clearData = () => {
    setInputData([]);
    setOutputData([]);
    setStats({});
    onAction('dataCleared');
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-lg max-w-2xl">
      <div className="mb-4">
        <h3 className="text-lg font-bold mb-2 flex items-center">
          <Calculator className="mr-2" size={20} />
          数据处理器
        </h3>
        
        {status === 'running' && (
          <div className="text-xs text-blue-600 mb-2">
            ● 处理中...
          </div>
        )}
      </div>

      {/* 控制按钮 */}
      <div className="flex items-center space-x-2 mb-4">
        <button
          onClick={generateSampleData}
          className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          <BarChart3 size={16} />
          <span>生成示例数据</span>
        </button>
        
        <button
          onClick={clearData}
          className="flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
        >
          <Trash2 size={16} />
          <span>清空数据</span>
        </button>
      </div>

      {/* 处理规则显示 */}
      {rules.length > 0 && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">活动规则:</h4>
          <div className="space-y-1">
            {rules.filter(r => r.enabled).map((rule, index) => (
              <div key={rule.id} className="text-xs text-gray-600 flex items-center">
                <span className="w-4 h-4 bg-green-500 rounded-full mr-2"></span>
                {index + 1}. {rule.operation} on {rule.field}
                {rule.condition && ` (${rule.condition}: ${rule.value})`}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 统计信息 */}
      {Object.keys(stats).length > 0 && (
        <div className="mb-4 grid grid-cols-2 gap-4">
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-600 font-medium">输入数据</div>
            <div className="text-2xl font-bold text-blue-800">{stats.inputCount}</div>
          </div>
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="text-sm text-green-600 font-medium">输出数据</div>
            <div className="text-2xl font-bold text-green-800">{stats.outputCount}</div>
          </div>
        </div>
      )}

      {/* 数据预览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 输入数据 */}
        <div>
          <h4 className="text-sm font-medium mb-2 flex items-center">
            <ArrowUpDown className="mr-1" size={14} />
            输入数据 ({inputData.length})
          </h4>
          <div className="h-40 overflow-auto border border-gray-200 rounded p-2 bg-gray-50">
            {inputData.length > 0 ? (
              <pre className="text-xs">
                {JSON.stringify(inputData.slice(0, 3), null, 2)}
                {inputData.length > 3 && '\n...'}
              </pre>
            ) : (
              <div className="text-gray-500 text-sm text-center py-8">
                暂无数据
              </div>
            )}
          </div>
        </div>

        {/* 输出数据 */}
        <div>
          <h4 className="text-sm font-medium mb-2 flex items-center">
            <Filter className="mr-1" size={14} />
            输出数据 ({outputData.length})
          </h4>
          <div className="h-40 overflow-auto border border-gray-200 rounded p-2 bg-green-50">
            {outputData.length > 0 ? (
              <pre className="text-xs">
                {JSON.stringify(outputData.slice(0, 3), null, 2)}
                {outputData.length > 3 && '\n...'}
              </pre>
            ) : (
              <div className="text-gray-500 text-sm text-center py-8">
                暂无数据
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 处理状态 */}
      {processing && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
            <span className="text-sm text-yellow-800">正在处理数据...</span>
          </div>
        </div>
      )}

      {/* 详细统计 */}
      {stats.processingTime && (
        <div className="mt-4 text-xs text-gray-500 space-y-1">
          <div>处理时间: {stats.processingTime.toFixed(1)}ms</div>
          <div>应用规则: {stats.rulesApplied}</div>
          <div>数据减少: {((1 - stats.outputCount / stats.inputCount) * 100).toFixed(1)}%</div>
        </div>
      )}
    </div>
  );
};

// React组件：数据处理器节点图标
interface DataProcessorIconProps {
  nodeId: string;
  status: string;
  isRunning: boolean;
}

const DataProcessorIcon: React.FC<DataProcessorIconProps> = ({ nodeId, status, isRunning }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-green-400 to-blue-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isRunning ? 'animate-pulse' : ''}
    `}>
      <Calculator size={16} />
    </div>
  );
};

// React组件：数据处理器节点属性面板
interface DataProcessorPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const DataProcessorProperties: React.FC<DataProcessorPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  const [rules, setRules] = useState<ProcessingRule[]>(config.rules || []);

  const addRule = () => {
    const newRule: ProcessingRule = {
      id: `rule_${Date.now()}`,
      operation: 'filter',
      field: '',
      condition: 'equals',
      value: '',
      enabled: true
    };
    const newRules = [...rules, newRule];
    setRules(newRules);
    onConfigChange('rules', newRules);
  };

  const removeRule = (ruleId: string) => {
    const newRules = rules.filter(r => r.id !== ruleId);
    setRules(newRules);
    onConfigChange('rules', newRules);
  };

  const updateRule = (ruleId: string, updates: Partial<ProcessingRule>) => {
    const newRules = rules.map(r => 
      r.id === ruleId ? { ...r, ...updates } : r
    );
    setRules(newRules);
    onConfigChange('rules', newRules);
  };

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          节点名称
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium text-gray-700">
            处理规则
          </label>
          <button
            onClick={addRule}
            className="flex items-center space-x-1 px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            <Plus size={12} />
            <span>添加规则</span>
          </button>
        </div>
        
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {rules.map((rule) => (
            <div key={rule.id} className="p-3 bg-white border border-gray-200 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <select
                  value={rule.operation}
                  onChange={(e) => updateRule(rule.id, { operation: e.target.value as ProcessingOperation })}
                  className="p-1 border border-gray-300 rounded text-sm"
                >
                  <option value="filter">过滤</option>
                  <option value="map">映射</option>
                  <option value="sort">排序</option>
                  <option value="group">分组</option>
                  <option value="aggregate">聚合</option>
                </select>
                
                <div className="flex items-center space-x-2">
                  <label className="flex items-center space-x-1">
                    <input
                      type="checkbox"
                      checked={rule.enabled}
                      onChange={(e) => updateRule(rule.id, { enabled: e.target.checked })}
                      className="rounded border-gray-300"
                    />
                    <span className="text-xs">启用</span>
                  </label>
                  
                  <button
                    onClick={() => removeRule(rule.id)}
                    className="p-1 text-red-500 hover:text-red-700"
                  >
                    <Trash2 size={12} />
                  </button>
                </div>
              </div>
              
              <div className="grid grid-cols-3 gap-2">
                <input
                  type="text"
                  placeholder="字段名"
                  value={rule.field}
                  onChange={(e) => updateRule(rule.id, { field: e.target.value })}
                  className="p-1 border border-gray-300 rounded text-sm"
                />
                
                {rule.operation === 'filter' && (
                  <select
                    value={rule.condition || 'equals'}
                    onChange={(e) => updateRule(rule.id, { condition: e.target.value })}
                    className="p-1 border border-gray-300 rounded text-sm"
                  >
                    <option value="equals">等于</option>
                    <option value="contains">包含</option>
                    <option value="greater">大于</option>
                    <option value="less">小于</option>
                  </select>
                )}
                
                {rule.operation === 'sort' && (
                  <select
                    value={rule.value || 'asc'}
                    onChange={(e) => updateRule(rule.id, { value: e.target.value })}
                    className="p-1 border border-gray-300 rounded text-sm"
                  >
                    <option value="asc">升序</option>
                    <option value="desc">降序</option>
                  </select>
                )}
                
                {(rule.operation === 'filter' || rule.operation === 'map') && (
                  <input
                    type="text"
                    placeholder="值"
                    value={rule.value || ''}
                    onChange={(e) => updateRule(rule.id, { value: e.target.value })}
                    className="p-1 border border-gray-300 rounded text-sm"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoProcess || false}
            onChange={(e) => onConfigChange('autoProcess', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">自动处理</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.showPreview !== false}
            onChange={(e) => onConfigChange('showPreview', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">显示预览</span>
        </label>
      </div>
    </div>
  );
};

// 数据处理器节点类
export class ReactDataProcessorNode extends ReactBaseNode {
  constructor() {
    super('react-data-processor', '数据处理器节点');
    this.description = '对数据进行转换、过滤、聚合等处理操作';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚（都是可选的）
    this.addInputPin({
      name: 'data',
      dataType: 'array',
      description: '输入数据数组',
      required: false
    });

    this.addInputPin({
      name: 'rules',
      dataType: 'array',
      description: '处理规则',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'processedData',
      dataType: 'array',
      description: '处理后的数据'
    });

    this.addOutputPin({
      name: 'statistics',
      dataType: 'object',
      description: '处理统计信息'
    });

    this.addOutputPin({
      name: 'success',
      dataType: 'boolean',
      description: '处理是否成功'
    });

    // 默认配置
    this.config = {
      rules: [],
      autoProcess: true,
      showPreview: true
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    const data = inputData.data || [];
    const rules = inputData.rules || this.config.rules || [];

    try {
      let result = [...data];
      let appliedRules = 0;

      // 应用处理规则
      for (const rule of rules.filter(r => r.enabled)) {
        // 这里实现实际的数据处理逻辑
        appliedRules++;
      }

      const statistics = {
        inputCount: data.length,
        outputCount: result.length,
        rulesApplied: appliedRules,
        processingTime: Date.now()
      };

      return {
        processedData: result,
        statistics,
        success: true
      };
    } catch (error) {
      return {
        processedData: [],
        statistics: { error: error.message },
        success: false
      };
    }
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(DataProcessorUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = this.getIconProps();
    return React.createElement(DataProcessorIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(DataProcessorProperties, props);
  }
}

// 导出组件供外部使用
export { DataProcessorUI, DataProcessorIcon, DataProcessorProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-data-processor',
  name: '数据处理器节点 (React)',
  description: '对数据进行转换、过滤、聚合等处理',
  category: '处理',
  icon: 'calculator',
  version: '2.0.0',
  author: 'System',
  tags: ['data', 'processing', 'transform', 'filter', 'aggregate', 'react']
};
