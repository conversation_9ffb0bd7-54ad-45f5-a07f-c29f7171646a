/**
 * 混合数学计算节点示例
 * 
 * 展示前后端协同工作的完整实现：
 * 1. 前端处理UI交互和简单计算
 * 2. 后端处理复杂数学运算和数据密集型操作
 * 3. 智能选择执行位置
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Calculator, Cpu, Zap, Settings } from 'lucide-react';
import { EnhancedReactBaseNode, BackendMethod, FrontendMethod } from '../EnhancedReactBaseNode';
import { NodeData, Pin, NodeStatus } from '../BaseNode';
import { BaseNodePropertyPanel } from '../../../components/BaseNodePropertyPanel';

// 节点配置接口
interface HybridMathConfig {
  operation: 'add' | 'multiply' | 'power' | 'factorial' | 'prime_check' | 'matrix_multiply';
  precision: number;
  useCache: boolean;
  forceBackend: boolean;
  maxFrontendValue: number;
}

// 计算结果接口
interface MathResult {
  result: number | number[][] | boolean;
  executionTime: number;
  executedOn: 'frontend' | 'backend';
  cached: boolean;
}

/**
 * 混合数学计算节点
 */
export class ReactHybridMathNode extends EnhancedReactBaseNode {
  private config: HybridMathConfig = {
    operation: 'add',
    precision: 10,
    useCache: true,
    forceBackend: false,
    maxFrontendValue: 1000000
  };

  constructor() {
    super('react-hybrid-math', '混合数学计算');
    
    // 定义输入引脚
    this.inputs = [
      new Pin('a', '数值A', 'number'),
      new Pin('b', '数值B', 'number'),
      new Pin('matrix_a', '矩阵A', 'array'),
      new Pin('matrix_b', '矩阵B', 'array')
    ];
    
    // 定义输出引脚
    this.outputs = [
      new Pin('result', '计算结果', 'any'),
      new Pin('metadata', '执行信息', 'object')
    ];
    
    console.log(`✅ ReactHybridMathNode created with config:`, this.config);
  }

  /**
   * 前端处理方法 - 处理简单计算和UI交互
   */
  @FrontendMethod()
  async processFrontend(inputData: NodeData): Promise<NodeData> {
    const startTime = Date.now();
    
    try {
      console.log(`🎯 Frontend processing: ${this.id}`, inputData);
      
      const a = inputData.a || 0;
      const b = inputData.b || 0;
      
      // 检查是否应该在前端执行
      if (this.shouldExecuteOnFrontend(inputData)) {
        const result = await this.executeOnFrontend(inputData);
        const executionTime = Date.now() - startTime;
        
        const output = {
          result: result,
          metadata: {
            result,
            executionTime,
            executedOn: 'frontend' as const,
            cached: false
          }
        };
        
        // 设置输出数据
        this.setOutputData(output.result, 'result');
        this.setOutputData(output.metadata, 'metadata');
        
        console.log(`✅ Frontend execution completed: ${this.id} (${executionTime}ms)`);
        return output;
      } else {
        // 需要后端处理
        const result = await this.callBackend<MathResult>('executeComplexMath', inputData, {
          cache: this.config.useCache,
          cacheTTL: 300000, // 5分钟缓存
          timeout: 30000
        });
        
        const output = {
          result: result.result,
          metadata: result
        };
        
        // 设置输出数据
        this.setOutputData(output.result, 'result');
        this.setOutputData(output.metadata, 'metadata');
        
        console.log(`✅ Backend execution completed: ${this.id} (${result.executionTime}ms)`);
        return output;
      }
      
    } catch (error) {
      console.error(`❌ Error in frontend processing: ${this.id}`, error);
      throw error;
    }
  }

  /**
   * 后端处理方法 - 处理复杂计算
   */
  @BackendMethod({ timeout: 60000, priority: 'high' })
  async processBackend(inputData: NodeData): Promise<NodeData> {
    // 这个方法实际上会在服务器端执行
    // 这里只是定义接口，实际实现在服务器端
    throw new Error('This method should be executed on backend');
  }

  /**
   * 判断是否应该在前端执行
   */
  private shouldExecuteOnFrontend(inputData: NodeData): boolean {
    if (this.config.forceBackend) {
      return false;
    }
    
    const a = inputData.a || 0;
    const b = inputData.b || 0;
    
    switch (this.config.operation) {
      case 'add':
      case 'multiply':
        // 简单运算在前端执行
        return Math.abs(a) < this.config.maxFrontendValue && Math.abs(b) < this.config.maxFrontendValue;
      
      case 'power':
        // 小指数在前端执行
        return Math.abs(a) < 1000 && Math.abs(b) < 10;
      
      case 'factorial':
        // 小数值阶乘在前端执行
        return a < 20;
      
      case 'prime_check':
        // 小数值质数检查在前端执行
        return a < 1000000;
      
      case 'matrix_multiply':
        // 小矩阵在前端执行
        const matrixA = inputData.matrix_a || [];
        const matrixB = inputData.matrix_b || [];
        return matrixA.length < 100 && matrixB.length < 100;
      
      default:
        return false;
    }
  }

  /**
   * 前端执行计算
   */
  @FrontendMethod()
  private async executeOnFrontend(inputData: NodeData): Promise<number | number[][] | boolean> {
    const a = inputData.a || 0;
    const b = inputData.b || 0;
    
    switch (this.config.operation) {
      case 'add':
        return this.precisionAdd(a, b);
      
      case 'multiply':
        return this.precisionMultiply(a, b);
      
      case 'power':
        return Math.pow(a, b);
      
      case 'factorial':
        return this.factorial(a);
      
      case 'prime_check':
        return this.isPrime(a);
      
      case 'matrix_multiply':
        const matrixA = inputData.matrix_a || [];
        const matrixB = inputData.matrix_b || [];
        return this.multiplyMatrices(matrixA, matrixB);
      
      default:
        throw new Error(`Unsupported operation: ${this.config.operation}`);
    }
  }

  /**
   * 精确加法
   */
  @FrontendMethod()
  private precisionAdd(a: number, b: number): number {
    return parseFloat((a + b).toFixed(this.config.precision));
  }

  /**
   * 精确乘法
   */
  @FrontendMethod()
  private precisionMultiply(a: number, b: number): number {
    return parseFloat((a * b).toFixed(this.config.precision));
  }

  /**
   * 阶乘计算
   */
  @FrontendMethod()
  private factorial(n: number): number {
    if (n < 0) throw new Error('Factorial of negative number is undefined');
    if (n === 0 || n === 1) return 1;
    
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  }

  /**
   * 质数检查
   */
  @FrontendMethod()
  private isPrime(n: number): boolean {
    if (n < 2) return false;
    if (n === 2) return true;
    if (n % 2 === 0) return false;
    
    for (let i = 3; i <= Math.sqrt(n); i += 2) {
      if (n % i === 0) return false;
    }
    return true;
  }

  /**
   * 矩阵乘法
   */
  @FrontendMethod()
  private multiplyMatrices(a: number[][], b: number[][]): number[][] {
    if (a[0].length !== b.length) {
      throw new Error('Matrix dimensions do not match for multiplication');
    }
    
    const result: number[][] = [];
    for (let i = 0; i < a.length; i++) {
      result[i] = [];
      for (let j = 0; j < b[0].length; j++) {
        let sum = 0;
        for (let k = 0; k < b.length; k++) {
          sum += a[i][k] * b[k][j];
        }
        result[i][j] = sum;
      }
    }
    return result;
  }

  /**
   * 检查是否应该使用后端
   */
  shouldUseBackend(inputData: NodeData): boolean {
    return !this.shouldExecuteOnFrontend(inputData);
  }

  /**
   * 渲染节点图标
   */
  renderIconComponent(): React.ReactElement {
    return (
      <div className="flex items-center justify-center w-4 h-4">
        {this.status === NodeStatus.RUNNING ? (
          <Cpu className="w-3 h-3 text-blue-500 animate-spin" />
        ) : (
          <Calculator className="w-3 h-3 text-purple-500" />
        )}
      </div>
    );
  }

  /**
   * 渲染节点UI
   */
  renderUIComponent(): React.ReactElement {
    return <HybridMathNodeUI node={this} />;
  }

  /**
   * 渲染属性面板
   */
  getPropertyPanelComponent(): React.ReactElement {
    return <HybridMathNodePropertyPanel node={this} />;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<HybridMathConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configChanged', this.config);
    console.log(`🔧 ${this.id} 配置已更新:`, this.config);
  }

  /**
   * 获取配置
   */
  getConfig(): HybridMathConfig {
    return { ...this.config };
  }
}

/**
 * 节点UI组件
 */
const HybridMathNodeUI: React.FC<{ node: ReactHybridMathNode }> = ({ node }) => {
  const [config, setConfig] = useState(node.getConfig());
  const [result, setResult] = useState<any>(null);
  const [metadata, setMetadata] = useState<any>(null);

  useEffect(() => {
    const handleConfigChange = (newConfig: HybridMathConfig) => {
      setConfig(newConfig);
    };

    const handleOutputChange = () => {
      setResult(node.getOutputData('result'));
      setMetadata(node.getOutputData('metadata'));
    };

    node.on('configChanged', handleConfigChange);
    node.on('outputDataChanged', handleOutputChange);

    return () => {
      node.off('configChanged', handleConfigChange);
      node.off('outputDataChanged', handleOutputChange);
    };
  }, [node]);

  return (
    <div className="p-4 bg-white border rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-3">
        <Calculator className="w-5 h-5 text-purple-500" />
        <h3 className="font-medium">混合数学计算</h3>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">运算:</span>
          <span className="font-medium">{config.operation}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">精度:</span>
          <span className="font-medium">{config.precision}</span>
        </div>
        
        {metadata && (
          <div className="mt-3 p-2 bg-gray-50 rounded">
            <div className="flex items-center gap-1 mb-1">
              {metadata.executedOn === 'frontend' ? (
                <Zap className="w-3 h-3 text-blue-500" />
              ) : (
                <Cpu className="w-3 h-3 text-green-500" />
              )}
              <span className="text-xs font-medium">
                {metadata.executedOn === 'frontend' ? '前端执行' : '后端执行'}
              </span>
            </div>
            <div className="text-xs text-gray-600">
              执行时间: {metadata.executionTime}ms
              {metadata.cached && ' (缓存)'}
            </div>
          </div>
        )}
        
        {result !== null && (
          <div className="mt-3 p-2 bg-blue-50 rounded">
            <div className="text-xs text-blue-600 mb-1">计算结果:</div>
            <div className="font-mono text-sm">
              {typeof result === 'object' ? JSON.stringify(result) : String(result)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * 属性面板组件
 */
const HybridMathNodePropertyPanel: React.FC<{ node: ReactHybridMathNode }> = ({ node }) => {
  const [config, setConfig] = useState(node.getConfig());
  const props = node.getPropertyProps();

  const handleConfigChange = (key: keyof HybridMathConfig, value: any) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    node.updateConfig(newConfig);
  };

  return (
    <BaseNodePropertyPanel
      nodeId={props.nodeId}
      nodeType={props.type}
      name={props.name}
      description={props.description}
      status={props.status}
      onNameChange={props.onNameChange}
      onDescriptionChange={props.onDescriptionChange}
      title="混合数学计算配置"
    >
      {/* 运算类型 */}
      <div>
        <label className="block text-sm font-medium mb-1">运算类型</label>
        <select
          value={config.operation}
          onChange={(e) => handleConfigChange('operation', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="add">加法</option>
          <option value="multiply">乘法</option>
          <option value="power">幂运算</option>
          <option value="factorial">阶乘</option>
          <option value="prime_check">质数检查</option>
          <option value="matrix_multiply">矩阵乘法</option>
        </select>
      </div>

      {/* 精度设置 */}
      <div>
        <label className="block text-sm font-medium mb-1">计算精度</label>
        <input
          type="number"
          min={0}
          max={15}
          value={config.precision}
          onChange={(e) => handleConfigChange('precision', parseInt(e.target.value) || 10)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
        />
        <p className="text-xs text-gray-500 mt-1">小数点后保留位数</p>
      </div>

      {/* 前端执行阈值 */}
      <div>
        <label className="block text-sm font-medium mb-1">前端执行阈值</label>
        <input
          type="number"
          min={1000}
          max={10000000}
          value={config.maxFrontendValue}
          onChange={(e) => handleConfigChange('maxFrontendValue', parseInt(e.target.value) || 1000000)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
        />
        <p className="text-xs text-gray-500 mt-1">超过此值将使用后端计算</p>
      </div>

      {/* 缓存设置 */}
      <div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="useCache"
            checked={config.useCache}
            onChange={(e) => handleConfigChange('useCache', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="useCache" className="text-sm font-medium">启用结果缓存</label>
        </div>
        <p className="text-xs text-gray-500 mt-1">缓存后端计算结果以提高性能</p>
      </div>

      {/* 强制后端执行 */}
      <div>
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="forceBackend"
            checked={config.forceBackend}
            onChange={(e) => handleConfigChange('forceBackend', e.target.checked)}
            className="rounded"
          />
          <label htmlFor="forceBackend" className="text-sm font-medium">强制后端执行</label>
        </div>
        <p className="text-xs text-gray-500 mt-1">所有计算都在后端执行</p>
      </div>

      {/* 执行能力信息 */}
      <div className="p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium mb-2">执行能力</h4>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>前端方法:</span>
            <span>{node.getFrontendMethods().length}</span>
          </div>
          <div className="flex justify-between">
            <span>后端方法:</span>
            <span>{node.getBackendMethods().length}</span>
          </div>
          <div className="flex justify-between">
            <span>支持缓存:</span>
            <span>✅</span>
          </div>
        </div>
      </div>
    </BaseNodePropertyPanel>
  );
};

// 导出节点类和相关组件
export default ReactHybridMathNode;
export { HybridMathNodeUI, HybridMathNodePropertyPanel };
