'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData, NodeStatus } from '../BaseNode';
import { Video, Play, Pause, Volume2, VolumeX, RotateCcw } from 'lucide-react';

// React组件：视频显示UI
interface VideoDisplayUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const VideoDisplayUI: React.FC<VideoDisplayUIProps> = ({ nodeId, config, status, onAction }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);

  const videoUrl = config.videoUrl || '';
  const autoplay = config.autoplay || false;
  const loop = config.loop || false;
  const controls = config.showControls !== false;

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => setCurrentTime(video.currentTime);
    const handleDurationChange = () => setDuration(video.duration);
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('durationchange', handleDurationChange);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('durationchange', handleDurationChange);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, []);

  const handlePlayPause = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
      onAction('videoPaused', { currentTime: video.currentTime });
    } else {
      video.play();
      onAction('videoPlayed', { currentTime: video.currentTime });
    }
  };

  const handleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
    onAction('videoMuted', { muted: video.muted });
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = parseFloat(e.target.value);
    video.currentTime = newTime;
    setCurrentTime(newTime);
    onAction('videoSeeked', { currentTime: newTime });
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = parseFloat(e.target.value);
    video.volume = newVolume;
    setVolume(newVolume);
    onAction('volumeChanged', { volume: newVolume });
  };

  const handleRestart = () => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = 0;
    video.play();
    onAction('videoRestarted');
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!videoUrl) {
    return (
      <div className="p-6 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg text-center">
        <Video size={48} className="mx-auto mb-4 text-gray-400" />
        <p className="text-gray-500 mb-2">请设置视频URL</p>
        <p className="text-xs text-gray-400">在属性面板中配置视频源</p>
      </div>
    );
  }

  return (
    <div className="bg-black rounded-lg overflow-hidden shadow-lg max-w-md">
      <div className="relative">
        <video
          ref={videoRef}
          src={videoUrl}
          autoPlay={autoplay}
          loop={loop}
          muted={isMuted}
          className="w-full h-auto"
          onError={() => onAction('videoError', { url: videoUrl })}
          onLoadStart={() => onAction('videoLoadStart')}
          onLoadedData={() => onAction('videoLoaded')}
        />
        
        {status === 'running' && (
          <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
            LIVE
          </div>
        )}
      </div>

      {controls && (
        <div className="bg-gray-900 text-white p-3 space-y-2">
          {/* 进度条 */}
          <div className="flex items-center space-x-2 text-xs">
            <span>{formatTime(currentTime)}</span>
            <input
              type="range"
              min="0"
              max={duration || 0}
              value={currentTime}
              onChange={handleSeek}
              className="flex-1 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
            />
            <span>{formatTime(duration)}</span>
          </div>

          {/* 控制按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={handlePlayPause}
                className="p-2 hover:bg-gray-700 rounded transition-colors"
              >
                {isPlaying ? <Pause size={16} /> : <Play size={16} />}
              </button>
              
              <button
                onClick={handleRestart}
                className="p-2 hover:bg-gray-700 rounded transition-colors"
              >
                <RotateCcw size={16} />
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleMute}
                className="p-2 hover:bg-gray-700 rounded transition-colors"
              >
                {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
              </button>
              
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
                className="w-16 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// React组件：视频显示节点图标
interface VideoDisplayIconProps {
  nodeId: string;
  status: string;
  isRunning: boolean;
}

const VideoDisplayIcon: React.FC<VideoDisplayIconProps> = ({ nodeId, status, isRunning }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isRunning ? 'animate-pulse' : ''}
    `}>
      <Video size={16} />
    </div>
  );
};

// React组件：视频显示节点属性面板
interface VideoDisplayPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const VideoDisplayProperties: React.FC<VideoDisplayPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          节点名称
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          视频URL
        </label>
        <input
          type="url"
          value={config.videoUrl || ''}
          onChange={(e) => onConfigChange('videoUrl', e.target.value)}
          placeholder="https://example.com/video.mp4"
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <p className="text-xs text-gray-500 mt-1">
          支持MP4、WebM、OGV等格式
        </p>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoplay || false}
            onChange={(e) => onConfigChange('autoplay', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">自动播放</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.loop || false}
            onChange={(e) => onConfigChange('loop', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">循环播放</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.showControls !== false}
            onChange={(e) => onConfigChange('showControls', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">显示控制栏</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.muted || false}
            onChange={(e) => onConfigChange('muted', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">静音</span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认音量
        </label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={config.volume || 1}
          onChange={(e) => onConfigChange('volume', parseFloat(e.target.value))}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>0%</span>
          <span>{Math.round((config.volume || 1) * 100)}%</span>
          <span>100%</span>
        </div>
      </div>
    </div>
  );
};

// 视频显示节点类
export class ReactVideoDisplayNode extends ReactBaseNode {
  constructor() {
    super('react-video-display', '视频显示节点');
    this.description = '播放和显示视频内容，支持多种格式和控制选项';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚
    this.addInputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发视频播放'
    });

    this.addInputPin({
      name: 'videoUrl',
      dataType: 'string',
      description: '视频URL'
    });

    // 输出引脚
    this.addOutputPin({
      name: 'videoState',
      dataType: 'object',
      description: '视频播放状态'
    });

    this.addOutputPin({
      name: 'currentTime',
      dataType: 'number',
      description: '当前播放时间'
    });

    this.addOutputPin({
      name: 'events',
      dataType: 'object',
      description: '视频事件'
    });

    // 默认配置
    this.config = {
      videoUrl: '',
      autoplay: false,
      loop: false,
      showControls: true,
      muted: false,
      volume: 1
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    // 如果输入包含视频URL，更新配置
    if (inputData.videoUrl) {
      this.config.videoUrl = inputData.videoUrl;
    }

    // 视频显示节点主要是UI交互，这里返回当前状态
    return {
      videoState: {
        url: this.config.videoUrl,
        autoplay: this.config.autoplay,
        loop: this.config.loop,
        muted: this.config.muted,
        volume: this.config.volume
      },
      currentTime: 0,
      events: {
        initialized: new Date().toISOString()
      }
    };
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(VideoDisplayUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = this.getIconProps();
    return React.createElement(VideoDisplayIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(VideoDisplayProperties, props);
  }
}

// 导出组件供外部使用
export { VideoDisplayUI, VideoDisplayIcon, VideoDisplayProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-video-display',
  name: '视频显示节点 (React)',
  description: '播放和显示视频内容',
  category: '显示',
  icon: 'video',
  version: '2.0.0',
  author: 'System',
  tags: ['video', 'display', 'media', 'player', 'react']
};
