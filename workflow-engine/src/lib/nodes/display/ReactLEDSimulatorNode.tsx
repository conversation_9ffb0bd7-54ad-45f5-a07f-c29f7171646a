'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData, NodeStatus } from '../BaseNode';
import { Lightbulb, Play, Pause, RotateCcw, Palette } from 'lucide-react';

// LED状态接口
interface LEDState {
  id: number;
  color: string;
  brightness: number;
  isOn: boolean;
}

// React组件：LED模拟器UI
interface LEDSimulatorUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const LEDSimulatorUI: React.FC<LEDSimulatorUIProps> = ({ nodeId, config, status, onAction }) => {
  const [leds, setLeds] = useState<LEDState[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentPattern, setCurrentPattern] = useState('static');
  const [animationSpeed, setAnimationSpeed] = useState(500);

  const ledCount = config.ledCount || 10;
  const patterns = ['static', 'rainbow', 'wave', 'blink', 'chase'];

  // 初始化LED
  useEffect(() => {
    const initialLeds: LEDState[] = Array.from({ length: ledCount }, (_, i) => ({
      id: i,
      color: config.defaultColor || '#3B82F6',
      brightness: config.defaultBrightness || 100,
      isOn: true
    }));
    setLeds(initialLeds);
  }, [ledCount, config.defaultColor, config.defaultBrightness]);

  // 动画效果
  const runAnimation = useCallback(() => {
    if (!isRunning) return;

    setLeds(prevLeds => {
      const newLeds = [...prevLeds];
      
      switch (currentPattern) {
        case 'rainbow':
          return newLeds.map((led, i) => ({
            ...led,
            color: `hsl(${(i * 360 / ledCount + Date.now() / 50) % 360}, 70%, 50%)`
          }));
          
        case 'wave':
          return newLeds.map((led, i) => ({
            ...led,
            brightness: Math.abs(Math.sin((i + Date.now() / 200) * 0.5)) * 100
          }));
          
        case 'blink':
          const isOn = Math.floor(Date.now() / animationSpeed) % 2 === 0;
          return newLeds.map(led => ({ ...led, isOn }));
          
        case 'chase':
          const activeIndex = Math.floor(Date.now() / animationSpeed) % ledCount;
          return newLeds.map((led, i) => ({
            ...led,
            brightness: i === activeIndex ? 100 : 20
          }));
          
        default:
          return newLeds;
      }
    });
  }, [isRunning, currentPattern, animationSpeed, ledCount]);

  // 动画循环
  useEffect(() => {
    if (!isRunning) return;
    
    const interval = setInterval(runAnimation, 50);
    return () => clearInterval(interval);
  }, [runAnimation, isRunning]);

  const handleStart = () => {
    setIsRunning(true);
    onAction('ledStart', { pattern: currentPattern, speed: animationSpeed });
  };

  const handleStop = () => {
    setIsRunning(false);
    onAction('ledStop');
  };

  const handleReset = () => {
    setIsRunning(false);
    setLeds(prevLeds => prevLeds.map(led => ({
      ...led,
      color: config.defaultColor || '#3B82F6',
      brightness: config.defaultBrightness || 100,
      isOn: true
    })));
    onAction('ledReset');
  };

  const handlePatternChange = (pattern: string) => {
    setCurrentPattern(pattern);
    onAction('patternChanged', { pattern });
  };

  const handleLedClick = (ledId: number) => {
    setLeds(prevLeds => prevLeds.map(led => 
      led.id === ledId ? { ...led, isOn: !led.isOn } : led
    ));
    onAction('ledToggled', { ledId });
  };

  const getLedStyle = (led: LEDState) => {
    const opacity = led.isOn ? led.brightness / 100 : 0.1;
    return {
      backgroundColor: led.color,
      opacity,
      boxShadow: led.isOn ? `0 0 10px ${led.color}` : 'none'
    };
  };

  return (
    <div className="p-4 bg-gray-900 rounded-lg text-white max-w-md">
      <div className="mb-4">
        <h3 className="text-lg font-bold mb-2 flex items-center">
          <Lightbulb className="mr-2" size={20} />
          LED模拟器
        </h3>
        
        {status === 'running' && (
          <div className="text-xs text-green-400 mb-2">
            ● 运行中 - {currentPattern} 模式
          </div>
        )}
      </div>

      {/* LED灯带 */}
      <div className="mb-4 p-3 bg-black rounded-lg">
        <div className="grid grid-cols-5 gap-2">
          {leds.map((led) => (
            <div
              key={led.id}
              className="w-8 h-8 rounded-full border-2 border-gray-600 cursor-pointer transition-all duration-100 hover:scale-110"
              style={getLedStyle(led)}
              onClick={() => handleLedClick(led.id)}
              title={`LED ${led.id + 1} - ${led.isOn ? 'ON' : 'OFF'}`}
            />
          ))}
        </div>
      </div>

      {/* 控制面板 */}
      <div className="space-y-3">
        {/* 播放控制 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={isRunning ? handleStop : handleStart}
            className={`flex items-center space-x-1 px-3 py-2 rounded transition-colors ${
              isRunning 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            {isRunning ? <Pause size={16} /> : <Play size={16} />}
            <span>{isRunning ? '停止' : '开始'}</span>
          </button>
          
          <button
            onClick={handleReset}
            className="flex items-center space-x-1 px-3 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors"
          >
            <RotateCcw size={16} />
            <span>重置</span>
          </button>
        </div>

        {/* 模式选择 */}
        <div>
          <label className="block text-sm font-medium mb-1">动画模式</label>
          <select
            value={currentPattern}
            onChange={(e) => handlePatternChange(e.target.value)}
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-sm"
          >
            {patterns.map(pattern => (
              <option key={pattern} value={pattern}>
                {pattern === 'static' && '静态'}
                {pattern === 'rainbow' && '彩虹'}
                {pattern === 'wave' && '波浪'}
                {pattern === 'blink' && '闪烁'}
                {pattern === 'chase' && '追逐'}
              </option>
            ))}
          </select>
        </div>

        {/* 速度控制 */}
        <div>
          <label className="block text-sm font-medium mb-1">
            动画速度: {animationSpeed}ms
          </label>
          <input
            type="range"
            min="100"
            max="2000"
            step="100"
            value={animationSpeed}
            onChange={(e) => setAnimationSpeed(parseInt(e.target.value))}
            className="w-full"
          />
        </div>

        {/* 统计信息 */}
        <div className="text-xs text-gray-400 pt-2 border-t border-gray-700">
          <div>LED数量: {ledCount}</div>
          <div>激活LED: {leds.filter(led => led.isOn).length}</div>
          <div>当前模式: {currentPattern}</div>
        </div>
      </div>
    </div>
  );
};

// React组件：LED模拟器节点图标
interface LEDSimulatorIconProps {
  nodeId: string;
  status: string;
  isRunning: boolean;
}

const LEDSimulatorIcon: React.FC<LEDSimulatorIconProps> = ({ nodeId, status, isRunning }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isRunning ? 'animate-pulse' : ''}
    `}>
      <Lightbulb size={16} />
    </div>
  );
};

// React组件：LED模拟器节点属性面板
interface LEDSimulatorPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const LEDSimulatorProperties: React.FC<LEDSimulatorPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          节点名称
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          LED数量
        </label>
        <input
          type="number"
          min="1"
          max="50"
          value={config.ledCount || 10}
          onChange={(e) => onConfigChange('ledCount', parseInt(e.target.value))}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
        <p className="text-xs text-gray-500 mt-1">
          设置LED灯的数量 (1-50)
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认颜色
        </label>
        <div className="flex items-center space-x-2">
          <input
            type="color"
            value={config.defaultColor || '#3B82F6'}
            onChange={(e) => onConfigChange('defaultColor', e.target.value)}
            className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
          />
          <input
            type="text"
            value={config.defaultColor || '#3B82F6'}
            onChange={(e) => onConfigChange('defaultColor', e.target.value)}
            className="flex-1 p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认亮度: {config.defaultBrightness || 100}%
        </label>
        <input
          type="range"
          min="0"
          max="100"
          value={config.defaultBrightness || 100}
          onChange={(e) => onConfigChange('defaultBrightness', parseInt(e.target.value))}
          className="w-full"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认动画模式
        </label>
        <select
          value={config.defaultPattern || 'static'}
          onChange={(e) => onConfigChange('defaultPattern', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="static">静态</option>
          <option value="rainbow">彩虹</option>
          <option value="wave">波浪</option>
          <option value="blink">闪烁</option>
          <option value="chase">追逐</option>
        </select>
      </div>

      <div className="flex items-center space-x-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoStart || false}
            onChange={(e) => onConfigChange('autoStart', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">自动开始</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.showControls !== false}
            onChange={(e) => onConfigChange('showControls', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">显示控制面板</span>
        </label>
      </div>
    </div>
  );
};

// LED模拟器节点类
export class ReactLEDSimulatorNode extends ReactBaseNode {
  constructor() {
    super('react-led-simulator', 'LED模拟器节点');
    this.description = '模拟LED灯带显示效果，支持多种动画模式';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚
    this.addInputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发LED动画'
    });

    this.addInputPin({
      name: 'pattern',
      dataType: 'string',
      description: '动画模式'
    });

    this.addInputPin({
      name: 'color',
      dataType: 'string',
      description: 'LED颜色'
    });

    // 输出引脚
    this.addOutputPin({
      name: 'ledStates',
      dataType: 'array',
      description: 'LED状态数组'
    });

    this.addOutputPin({
      name: 'isRunning',
      dataType: 'boolean',
      description: '是否正在运行'
    });

    this.addOutputPin({
      name: 'currentPattern',
      dataType: 'string',
      description: '当前动画模式'
    });

    // 默认配置
    this.config = {
      ledCount: 10,
      defaultColor: '#3B82F6',
      defaultBrightness: 100,
      defaultPattern: 'static',
      autoStart: false,
      showControls: true
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    // 处理输入数据
    if (inputData.pattern) {
      this.config.defaultPattern = inputData.pattern;
    }
    if (inputData.color) {
      this.config.defaultColor = inputData.color;
    }

    // 初始化LED状态
    const ledStates = Array.from({ length: this.config.ledCount }, (_, i) => ({
      id: i,
      color: this.config.defaultColor,
      brightness: this.config.defaultBrightness,
      isOn: true
    }));

    // 触发LED更新事件
    this.emit('ledUpdate', ledStates);

    return {
      ledStates,
      isRunning: this.config.autoStart,
      currentPattern: this.config.defaultPattern
    };
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(LEDSimulatorUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = this.getIconProps();
    return React.createElement(LEDSimulatorIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(LEDSimulatorProperties, props);
  }
}

// 导出组件供外部使用
export { LEDSimulatorUI, LEDSimulatorIcon, LEDSimulatorProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-led-simulator',
  name: 'LED模拟器节点 (React)',
  description: '模拟LED灯带显示效果',
  category: '显示',
  icon: 'lightbulb',
  version: '2.0.0',
  author: 'System',
  tags: ['led', 'display', 'animation', 'simulator', 'react']
};
