'use client';

import React, { useState } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData } from '../BaseNode';
import { GitCompare } from 'lucide-react';

// 比较操作类型
type ComparisonType = 'equal' | 'notEqual' | 'greater' | 'greaterEqual' | 'less' | 'lessEqual' | 'between' | 'outside';

// React组件：比较节点UI
interface ComparisonUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const ComparisonUI: React.FC<ComparisonUIProps> = ({ nodeId, config, status, onAction }) => {
  const [valueA, setValueA] = useState(config.valueA || 0);
  const [valueB, setValueB] = useState(config.valueB || 0);
  const [valueC, setValueC] = useState(config.valueC || 0);
  const [result, setResult] = useState<boolean | null>(null);

  const comparisons = {
    equal: { label: '等于 (=)', symbol: '=' },
    notEqual: { label: '不等于 (≠)', symbol: '≠' },
    greater: { label: '大于 (>)', symbol: '>' },
    greaterEqual: { label: '大于等于 (≥)', symbol: '≥' },
    less: { label: '小于 (<)', symbol: '<' },
    lessEqual: { label: '小于等于 (≤)', symbol: '≤' },
    between: { label: '在范围内', symbol: '∈' },
    outside: { label: '在范围外', symbol: '∉' }
  };

  const compare = () => {
    const numA = parseFloat(valueA.toString());
    const numB = parseFloat(valueB.toString());
    const numC = parseFloat(valueC.toString());
    const operation = config.comparison || 'equal';

    let compResult: boolean;

    switch (operation) {
      case 'equal':
        compResult = Math.abs(numA - numB) < (config.tolerance || 1e-10);
        break;
      case 'notEqual':
        compResult = Math.abs(numA - numB) >= (config.tolerance || 1e-10);
        break;
      case 'greater':
        compResult = numA > numB;
        break;
      case 'greaterEqual':
        compResult = numA >= numB;
        break;
      case 'less':
        compResult = numA < numB;
        break;
      case 'lessEqual':
        compResult = numA <= numB;
        break;
      case 'between':
        const min = Math.min(numB, numC);
        const max = Math.max(numB, numC);
        compResult = numA >= min && numA <= max;
        break;
      case 'outside':
        const minOut = Math.min(numB, numC);
        const maxOut = Math.max(numB, numC);
        compResult = numA < minOut || numA > maxOut;
        break;
      default:
        compResult = false;
    }

    setResult(compResult);
    onAction('compare', { 
      valueA: numA, 
      valueB: numB, 
      valueC: numC, 
      result: compResult, 
      operation 
    });
  };

  const currentComp = comparisons[config.comparison as ComparisonType] || comparisons.equal;
  const needsThreeValues = ['between', 'outside'].includes(config.comparison);

  return (
    <div className="p-5 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg text-white min-w-[300px]">
      <div className="text-lg font-bold mb-3 text-center">⚖️ 数值比较</div>
      
      <div className="space-y-3">
        <div className="text-center text-sm opacity-90">
          {currentComp.label}
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="number"
              value={valueA}
              onChange={(e) => setValueA(parseFloat(e.target.value) || 0)}
              className="flex-1 p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30"
              placeholder="值 A"
              step="any"
            />
            <span className="text-lg font-bold min-w-[24px] text-center">
              {needsThreeValues ? currentComp.symbol : currentComp.symbol}
            </span>
            <input
              type="number"
              value={valueB}
              onChange={(e) => setValueB(parseFloat(e.target.value) || 0)}
              className="flex-1 p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30"
              placeholder={needsThreeValues ? "最小值" : "值 B"}
              step="any"
            />
          </div>

          {needsThreeValues && (
            <div className="flex items-center justify-center">
              <span className="text-sm opacity-80 mr-2">和</span>
              <input
                type="number"
                value={valueC}
                onChange={(e) => setValueC(parseFloat(e.target.value) || 0)}
                className="flex-1 p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30"
                placeholder="最大值"
                step="any"
              />
            </div>
          )}
        </div>

        <button
          onClick={compare}
          className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white py-2 rounded transition-all duration-300"
        >
          比较
        </button>

        {result !== null && (
          <div className={`text-center p-3 rounded ${
            result ? 'bg-green-500/30' : 'bg-red-500/30'
          }`}>
            <div className="text-sm opacity-90">结果</div>
            <div className="text-2xl font-bold">
              {result ? '✓ 真' : '✗ 假'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// React组件：比较节点图标
interface ComparisonIconProps {
  nodeId: string;
  status: string;
  isComparing: boolean;
}

const ComparisonIcon: React.FC<ComparisonIconProps> = ({ nodeId, status, isComparing }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-orange-400 to-red-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isComparing ? 'animate-pulse' : ''}
    `}>
      <GitCompare size={16} />
    </div>
  );
};

// React组件：比较节点属性面板
interface ComparisonPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const ComparisonProperties: React.FC<ComparisonPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  const comparisons = [
    { value: 'equal', label: '等于 (=)' },
    { value: 'notEqual', label: '不等于 (≠)' },
    { value: 'greater', label: '大于 (>)' },
    { value: 'greaterEqual', label: '大于等于 (≥)' },
    { value: 'less', label: '小于 (<)' },
    { value: 'lessEqual', label: '小于等于 (≤)' },
    { value: 'between', label: '在范围内' },
    { value: 'outside', label: '在范围外' }
  ];

  const needsThreeValues = ['between', 'outside'].includes(config.comparison);

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          比较类型
        </label>
        <select
          value={config.comparison || 'equal'}
          onChange={(e) => onConfigChange('comparison', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        >
          {comparisons.map(comp => (
            <option key={comp.value} value={comp.value}>{comp.label}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认值 A
        </label>
        <input
          type="number"
          value={config.valueA || 0}
          onChange={(e) => onConfigChange('valueA', parseFloat(e.target.value) || 0)}
          step="any"
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {needsThreeValues ? '范围最小值' : '默认值 B'}
        </label>
        <input
          type="number"
          value={config.valueB || 0}
          onChange={(e) => onConfigChange('valueB', parseFloat(e.target.value) || 0)}
          step="any"
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      {needsThreeValues && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            范围最大值
          </label>
          <input
            type="number"
            value={config.valueC || 0}
            onChange={(e) => onConfigChange('valueC', parseFloat(e.target.value) || 0)}
            step="any"
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
      )}

      {['equal', 'notEqual'].includes(config.comparison) && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            容差 (浮点数比较)
          </label>
          <input
            type="number"
            value={config.tolerance || 1e-10}
            onChange={(e) => onConfigChange('tolerance', parseFloat(e.target.value) || 1e-10)}
            step="any"
            min="0"
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
      )}

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoCompare || false}
            onChange={(e) => onConfigChange('autoCompare', e.target.checked)}
            className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
          />
          <span className="text-sm text-gray-700">自动比较</span>
        </label>
      </div>
    </div>
  );
};

// 比较节点类
export class ReactComparisonNode extends ReactBaseNode {
  constructor() {
    super('react-comparison', '数值比较节点');
    this.description = '比较数值：等于、大于、小于、范围判断等';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚（都是可选的）
    this.addInputPin({
      name: 'valueA',
      dataType: 'number',
      description: '比较值 A',
      required: false
    });

    this.addInputPin({
      name: 'valueB',
      dataType: 'number',
      description: '比较值 B',
      required: false
    });

    this.addInputPin({
      name: 'valueC',
      dataType: 'number',
      description: '比较值 C (范围比较)',
      required: false
    });

    this.addInputPin({
      name: 'comparison',
      dataType: 'string',
      description: '比较类型',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'result',
      dataType: 'boolean',
      description: '比较结果'
    });

    this.addOutputPin({
      name: 'valueA',
      dataType: 'number',
      description: '输入值 A'
    });

    this.addOutputPin({
      name: 'valueB',
      dataType: 'number',
      description: '输入值 B'
    });

    // 默认配置
    this.config = {
      comparison: 'equal',
      valueA: 0,
      valueB: 0,
      valueC: 0,
      tolerance: 1e-10,
      autoCompare: true
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    const valueA = inputData.valueA ?? this.config.valueA ?? 0;
    const valueB = inputData.valueB ?? this.config.valueB ?? 0;
    const valueC = inputData.valueC ?? this.config.valueC ?? 0;
    const comparison = inputData.comparison ?? this.config.comparison ?? 'equal';
    const tolerance = this.config.tolerance || 1e-10;

    let result: boolean;

    switch (comparison) {
      case 'equal':
        result = Math.abs(valueA - valueB) < tolerance;
        break;
      case 'notEqual':
        result = Math.abs(valueA - valueB) >= tolerance;
        break;
      case 'greater':
        result = valueA > valueB;
        break;
      case 'greaterEqual':
        result = valueA >= valueB;
        break;
      case 'less':
        result = valueA < valueB;
        break;
      case 'lessEqual':
        result = valueA <= valueB;
        break;
      case 'between':
        const min = Math.min(valueB, valueC);
        const max = Math.max(valueB, valueC);
        result = valueA >= min && valueA <= max;
        break;
      case 'outside':
        const minOut = Math.min(valueB, valueC);
        const maxOut = Math.max(valueB, valueC);
        result = valueA < minOut || valueA > maxOut;
        break;
      default:
        result = false;
    }

    return {
      result,
      valueA,
      valueB,
      valueC,
      comparison,
      tolerance
    };
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(ComparisonUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = { ...this.getIconProps(), isComparing: false };
    return React.createElement(ComparisonIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(ComparisonProperties, props);
  }
}

// 导出组件供外部使用
export { ComparisonUI, ComparisonIcon, ComparisonProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-comparison',
  name: '数值比较节点',
  description: '比较数值：等于、大于、小于、范围判断等',
  category: '数学',
  icon: 'compare',
  version: '1.0.0',
  author: 'System',
  tags: ['math', 'comparison', 'logic', 'condition', 'react']
};
