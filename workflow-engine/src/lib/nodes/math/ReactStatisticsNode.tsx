'use client';

import React, { useState } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData } from '../BaseNode';
import { BarChart3 } from 'lucide-react';

// React组件：统计节点UI
interface StatisticsUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const StatisticsUI: React.FC<StatisticsUIProps> = ({ nodeId, config, status, onAction }) => {
  const [inputData, setInputData] = useState(config.inputData || '1,2,3,4,5');
  const [results, setResults] = useState<any>(null);

  const parseNumbers = (data: string): number[] => {
    return data.split(',')
      .map(s => parseFloat(s.trim()))
      .filter(n => !isNaN(n));
  };

  const calculateStatistics = () => {
    const numbers = parseNumbers(inputData);
    
    if (numbers.length === 0) {
      setResults({ error: '无有效数据' });
      return;
    }

    const sorted = [...numbers].sort((a, b) => a - b);
    const sum = numbers.reduce((acc, val) => acc + val, 0);
    const mean = sum / numbers.length;
    
    // 中位数
    const median = numbers.length % 2 === 0
      ? (sorted[numbers.length / 2 - 1] + sorted[numbers.length / 2]) / 2
      : sorted[Math.floor(numbers.length / 2)];
    
    // 方差和标准差
    const variance = numbers.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numbers.length;
    const standardDeviation = Math.sqrt(variance);
    
    // 最值和范围
    const min = Math.min(...numbers);
    const max = Math.max(...numbers);
    const range = max - min;

    const stats = {
      count: numbers.length,
      sum,
      mean,
      median,
      min,
      max,
      range,
      variance,
      standardDeviation
    };

    setResults(stats);
    onAction('calculate', { input: numbers, results: stats });
  };

  return (
    <div className="p-5 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg text-white min-w-[320px]">
      <div className="text-lg font-bold mb-3 text-center">📊 统计计算</div>
      
      <div className="space-y-3">
        <div>
          <label className="block text-sm mb-1 opacity-90">数据 (用逗号分隔)</label>
          <textarea
            value={inputData}
            onChange={(e) => setInputData(e.target.value)}
            className="w-full p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30 resize-none"
            placeholder="1,2,3,4,5"
            rows={2}
          />
        </div>

        <button
          onClick={calculateStatistics}
          className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white py-2 rounded transition-all duration-300"
        >
          计算统计量
        </button>

        {results && (
          <div className="space-y-2">
            {results.error ? (
              <div className="text-center p-2 bg-red-500/30 rounded text-red-100">
                {results.error}
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">数量</div>
                  <div className="font-bold">{results.count}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">总和</div>
                  <div className="font-bold">{results.sum.toFixed(2)}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">平均值</div>
                  <div className="font-bold">{results.mean.toFixed(2)}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">中位数</div>
                  <div className="font-bold">{results.median.toFixed(2)}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">最小值</div>
                  <div className="font-bold">{results.min.toFixed(2)}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">最大值</div>
                  <div className="font-bold">{results.max.toFixed(2)}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">范围</div>
                  <div className="font-bold">{results.range.toFixed(2)}</div>
                </div>
                <div className="bg-white/20 p-2 rounded">
                  <div className="opacity-80">标准差</div>
                  <div className="font-bold">{results.standardDeviation.toFixed(2)}</div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// React组件：统计节点图标
interface StatisticsIconProps {
  nodeId: string;
  status: string;
  isCalculating: boolean;
}

const StatisticsIcon: React.FC<StatisticsIconProps> = ({ nodeId, status, isCalculating }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-blue-400 to-cyan-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isCalculating ? 'animate-pulse' : ''}
    `}>
      <BarChart3 size={16} />
    </div>
  );
};

// React组件：统计节点属性面板
interface StatisticsPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const StatisticsProperties: React.FC<StatisticsPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认数据
        </label>
        <textarea
          value={config.inputData || '1,2,3,4,5'}
          onChange={(e) => onConfigChange('inputData', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
          placeholder="用逗号分隔的数字"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          输出选项
        </label>
        <div className="space-y-2">
          {[
            { key: 'outputMean', label: '平均值' },
            { key: 'outputMedian', label: '中位数' },
            { key: 'outputStdDev', label: '标准差' },
            { key: 'outputVariance', label: '方差' },
            { key: 'outputRange', label: '范围' },
            { key: 'outputMinMax', label: '最值' }
          ].map(option => (
            <label key={option.key} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config[option.key] !== false}
                onChange={(e) => onConfigChange(option.key, e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          精度 (小数位数)
        </label>
        <input
          type="number"
          value={config.precision || 2}
          onChange={(e) => onConfigChange('precision', parseInt(e.target.value) || 2)}
          min="0"
          max="10"
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>
  );
};

// 统计节点类
export class ReactStatisticsNode extends ReactBaseNode {
  constructor() {
    super('react-statistics', '统计计算节点');
    this.description = '计算数据集的统计量：平均值、中位数、标准差等';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚（可选的）
    this.addInputPin({
      name: 'data',
      dataType: 'array',
      description: '数据数组',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'mean',
      dataType: 'number',
      description: '平均值'
    });

    this.addOutputPin({
      name: 'median',
      dataType: 'number',
      description: '中位数'
    });

    this.addOutputPin({
      name: 'standardDeviation',
      dataType: 'number',
      description: '标准差'
    });

    this.addOutputPin({
      name: 'variance',
      dataType: 'number',
      description: '方差'
    });

    this.addOutputPin({
      name: 'min',
      dataType: 'number',
      description: '最小值'
    });

    this.addOutputPin({
      name: 'max',
      dataType: 'number',
      description: '最大值'
    });

    this.addOutputPin({
      name: 'range',
      dataType: 'number',
      description: '范围'
    });

    this.addOutputPin({
      name: 'count',
      dataType: 'number',
      description: '数据数量'
    });

    // 默认配置
    this.config = {
      inputData: '1,2,3,4,5',
      precision: 2,
      outputMean: true,
      outputMedian: true,
      outputStdDev: true,
      outputVariance: true,
      outputRange: true,
      outputMinMax: true
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    let numbers: number[] = [];

    // 处理输入数据
    if (inputData.data && Array.isArray(inputData.data)) {
      numbers = inputData.data.filter(n => typeof n === 'number' && !isNaN(n));
    } else if (typeof inputData.data === 'string') {
      numbers = inputData.data.split(',')
        .map(s => parseFloat(s.trim()))
        .filter(n => !isNaN(n));
    } else if (this.config.inputData) {
      numbers = this.config.inputData.split(',')
        .map((s: string) => parseFloat(s.trim()))
        .filter((n: number) => !isNaN(n));
    }

    if (numbers.length === 0) {
      return {
        error: 'No valid data provided',
        count: 0
      };
    }

    const precision = this.config.precision || 2;
    const sorted = [...numbers].sort((a, b) => a - b);
    const sum = numbers.reduce((acc, val) => acc + val, 0);
    const mean = sum / numbers.length;
    
    // 中位数
    const median = numbers.length % 2 === 0
      ? (sorted[numbers.length / 2 - 1] + sorted[numbers.length / 2]) / 2
      : sorted[Math.floor(numbers.length / 2)];
    
    // 方差和标准差
    const variance = numbers.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numbers.length;
    const standardDeviation = Math.sqrt(variance);
    
    // 最值和范围
    const min = Math.min(...numbers);
    const max = Math.max(...numbers);
    const range = max - min;

    const result: NodeData = {
      count: numbers.length,
      sum: parseFloat(sum.toFixed(precision))
    };

    // 根据配置添加输出
    if (this.config.outputMean) result.mean = parseFloat(mean.toFixed(precision));
    if (this.config.outputMedian) result.median = parseFloat(median.toFixed(precision));
    if (this.config.outputStdDev) result.standardDeviation = parseFloat(standardDeviation.toFixed(precision));
    if (this.config.outputVariance) result.variance = parseFloat(variance.toFixed(precision));
    if (this.config.outputMinMax) {
      result.min = parseFloat(min.toFixed(precision));
      result.max = parseFloat(max.toFixed(precision));
    }
    if (this.config.outputRange) result.range = parseFloat(range.toFixed(precision));

    return result;
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(StatisticsUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = { ...this.getIconProps(), isCalculating: false };
    return React.createElement(StatisticsIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(StatisticsProperties, props);
  }
}

// 导出组件供外部使用
export { StatisticsUI, StatisticsIcon, StatisticsProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-statistics',
  name: '统计计算节点',
  description: '计算数据集的统计量：平均值、中位数、标准差等',
  category: '数学',
  icon: 'chart',
  version: '1.0.0',
  author: 'System',
  tags: ['math', 'statistics', 'analysis', 'data', 'react']
};
