'use client';

import React, { useState } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData } from '../BaseNode';
import { Calculator } from 'lucide-react';

// 运算类型
type OperationType = 'add' | 'subtract' | 'multiply' | 'divide' | 'power' | 'modulo';

// React组件：基本运算节点UI
interface BasicMathUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const BasicMathUI: React.FC<BasicMathUIProps> = ({ nodeId, config, status, onAction }) => {
  const [a, setA] = useState(config.valueA || 0);
  const [b, setB] = useState(config.valueB || 0);
  const [result, setResult] = useState<number | null>(null);

  const operations = {
    add: { label: '加法 (+)', symbol: '+' },
    subtract: { label: '减法 (-)', symbol: '-' },
    multiply: { label: '乘法 (×)', symbol: '×' },
    divide: { label: '除法 (÷)', symbol: '÷' },
    power: { label: '幂运算 (^)', symbol: '^' },
    modulo: { label: '取模 (%)', symbol: '%' }
  };

  const calculate = () => {
    let calcResult: number;
    const numA = parseFloat(a.toString());
    const numB = parseFloat(b.toString());

    switch (config.operation || 'add') {
      case 'add':
        calcResult = numA + numB;
        break;
      case 'subtract':
        calcResult = numA - numB;
        break;
      case 'multiply':
        calcResult = numA * numB;
        break;
      case 'divide':
        calcResult = numB !== 0 ? numA / numB : NaN;
        break;
      case 'power':
        calcResult = Math.pow(numA, numB);
        break;
      case 'modulo':
        calcResult = numB !== 0 ? numA % numB : NaN;
        break;
      default:
        calcResult = 0;
    }

    setResult(calcResult);
    onAction('calculate', { a: numA, b: numB, result: calcResult, operation: config.operation });
  };

  const currentOp = operations[config.operation as OperationType] || operations.add;

  return (
    <div className="p-5 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg text-white min-w-[280px]">
      <div className="text-lg font-bold mb-3 text-center">🧮 基本运算</div>
      
      <div className="space-y-3">
        <div className="text-center text-sm opacity-90">
          {currentOp.label}
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="number"
            value={a}
            onChange={(e) => setA(parseFloat(e.target.value) || 0)}
            className="flex-1 p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30"
            placeholder="数值 A"
          />
          <span className="text-xl font-bold">{currentOp.symbol}</span>
          <input
            type="number"
            value={b}
            onChange={(e) => setB(parseFloat(e.target.value) || 0)}
            className="flex-1 p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30"
            placeholder="数值 B"
          />
        </div>

        <button
          onClick={calculate}
          className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white py-2 rounded transition-all duration-300"
        >
          计算
        </button>

        {result !== null && (
          <div className="text-center p-3 bg-white/20 rounded">
            <div className="text-sm opacity-90">结果</div>
            <div className="text-2xl font-bold">
              {isNaN(result) ? '错误' : result.toFixed(6).replace(/\.?0+$/, '')}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// React组件：基本运算节点图标
interface BasicMathIconProps {
  nodeId: string;
  status: string;
  isCalculating: boolean;
}

const BasicMathIcon: React.FC<BasicMathIconProps> = ({ nodeId, status, isCalculating }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isCalculating ? 'animate-pulse' : ''}
    `}>
      <Calculator size={16} />
    </div>
  );
};

// React组件：基本运算节点属性面板
interface BasicMathPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const BasicMathProperties: React.FC<BasicMathPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  const operations = [
    { value: 'add', label: '加法 (+)' },
    { value: 'subtract', label: '减法 (-)' },
    { value: 'multiply', label: '乘法 (×)' },
    { value: 'divide', label: '除法 (÷)' },
    { value: 'power', label: '幂运算 (^)' },
    { value: 'modulo', label: '取模 (%)' }
  ];

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      {/* 基础属性 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          节点名称
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
          placeholder="自定义节点名称"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          描述
        </label>
        <textarea
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          rows={2}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
          placeholder="节点描述"
        />
      </div>

      {/* 节点特定配置 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          运算类型
        </label>
        <select
          value={config.operation || 'add'}
          onChange={(e) => onConfigChange('operation', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
        >
          {operations.map(op => (
            <option key={op.value} value={op.value}>{op.label}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认值 A
        </label>
        <input
          type="number"
          value={config.valueA || 0}
          onChange={(e) => onConfigChange('valueA', parseFloat(e.target.value) || 0)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认值 B
        </label>
        <input
          type="number"
          value={config.valueB || 0}
          onChange={(e) => onConfigChange('valueB', parseFloat(e.target.value) || 0)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-green-500 focus:border-green-500"
        />
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoCalculate || false}
            onChange={(e) => onConfigChange('autoCalculate', e.target.checked)}
            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
          />
          <span className="text-sm text-gray-700">自动计算</span>
        </label>
      </div>
    </div>
  );
};

// 基本运算节点类
export class ReactBasicMathNode extends ReactBaseNode {
  constructor() {
    super('react-basic-math', '基本运算节点');
    this.description = '执行基本数学运算：加减乘除、幂运算、取模';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚（都是可选的，可以使用配置中的默认值）
    this.addInputPin({
      name: 'valueA',
      dataType: 'number',
      description: '数值 A',
      required: false
    });

    this.addInputPin({
      name: 'valueB',
      dataType: 'number',
      description: '数值 B',
      required: false
    });

    this.addInputPin({
      name: 'operation',
      dataType: 'string',
      description: '运算类型',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'result',
      dataType: 'number',
      description: '运算结果'
    });

    this.addOutputPin({
      name: 'isValid',
      dataType: 'boolean',
      description: '结果是否有效'
    });

    // 默认配置
    this.config = {
      operation: 'add',
      valueA: 0,
      valueB: 0,
      autoCalculate: true
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    console.log(`🔥🔥🔥 BASIC MATH NODE PROCESS CALLED FOR NODE ${this.id} 🔥🔥🔥`);
    console.log(`🔥 InputData:`, inputData);
    console.log(`🔥 Config:`, this.config);

    const a = inputData.valueA ?? this.config.valueA ?? 0;
    const b = inputData.valueB ?? this.config.valueB ?? 0;
    const operation = inputData.operation ?? this.config.operation ?? 'add';

    console.log(`🔥 Calculated values: a=${a}, b=${b}, operation=${operation}`);

    let result: number;
    let isValid = true;

    switch (operation) {
      case 'add':
        result = a + b;
        break;
      case 'subtract':
        result = a - b;
        break;
      case 'multiply':
        result = a * b;
        break;
      case 'divide':
        if (b === 0) {
          result = NaN;
          isValid = false;
        } else {
          result = a / b;
        }
        break;
      case 'power':
        result = Math.pow(a, b);
        break;
      case 'modulo':
        if (b === 0) {
          result = NaN;
          isValid = false;
        } else {
          result = a % b;
        }
        break;
      default:
        result = 0;
        isValid = false;
    }

    const outputData = {
      result,
      isValid,
      operation,
      inputA: a,
      inputB: b
    };

    console.log(`🔥🔥🔥 BASIC MATH NODE PROCESS COMPLETED FOR NODE ${this.id} 🔥🔥🔥`);
    console.log(`🔥 OutputData:`, outputData);

    return outputData;
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(BasicMathUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = { ...this.getIconProps(), isCalculating: false };
    return React.createElement(BasicMathIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(BasicMathProperties, props);
  }
}

// 导出组件供外部使用
export { BasicMathUI, BasicMathIcon, BasicMathProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-basic-math',
  name: '基本运算节点',
  description: '执行基本数学运算：加减乘除、幂运算、取模',
  category: '数学',
  icon: 'calculator',
  version: '1.0.0',
  author: 'System',
  tags: ['math', 'calculation', 'arithmetic', 'basic', 'react']
};
