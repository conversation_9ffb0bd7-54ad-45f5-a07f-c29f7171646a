'use client';

import React, { useState } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData } from '../BaseNode';
import { Shuffle } from 'lucide-react';

// 随机数类型
type RandomType = 'uniform' | 'integer' | 'normal' | 'boolean' | 'choice';

// React组件：随机数节点UI
interface RandomUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const RandomUI: React.FC<RandomUIProps> = ({ nodeId, config, status, onAction }) => {
  const [result, setResult] = useState<any>(null);
  const [history, setHistory] = useState<any[]>([]);

  const randomTypes = {
    uniform: { label: '均匀分布 (0-1)', description: '0到1之间的随机小数' },
    integer: { label: '随机整数', description: '指定范围内的随机整数' },
    normal: { label: '正态分布', description: '正态分布随机数' },
    boolean: { label: '随机布尔值', description: '真或假' },
    choice: { label: '随机选择', description: '从列表中随机选择' }
  };

  // Box-Muller变换生成正态分布随机数
  const generateNormal = (mean: number = 0, stdDev: number = 1): number => {
    const u1 = Math.random();
    const u2 = Math.random();
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
    return z0 * stdDev + mean;
  };

  const generate = () => {
    const type = config.randomType || 'uniform';
    let newResult: any;

    switch (type) {
      case 'uniform':
        const min = config.min || 0;
        const max = config.max || 1;
        newResult = Math.random() * (max - min) + min;
        break;
      
      case 'integer':
        const intMin = Math.ceil(config.min || 1);
        const intMax = Math.floor(config.max || 100);
        newResult = Math.floor(Math.random() * (intMax - intMin + 1)) + intMin;
        break;
      
      case 'normal':
        const mean = config.mean || 0;
        const stdDev = config.stdDev || 1;
        newResult = generateNormal(mean, stdDev);
        break;
      
      case 'boolean':
        const probability = config.probability || 0.5;
        newResult = Math.random() < probability;
        break;
      
      case 'choice':
        const choices = config.choices ? config.choices.split(',').map((s: string) => s.trim()) : ['A', 'B', 'C'];
        newResult = choices[Math.floor(Math.random() * choices.length)];
        break;
      
      default:
        newResult = Math.random();
    }

    setResult(newResult);
    setHistory(prev => [newResult, ...prev.slice(0, 4)]); // 保留最近5个结果
    onAction('generate', { result: newResult, type, timestamp: Date.now() });
  };

  const currentType = randomTypes[config.randomType as RandomType] || randomTypes.uniform;

  return (
    <div className="p-5 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg text-white min-w-[280px]">
      <div className="text-lg font-bold mb-3 text-center">🎲 随机数生成</div>
      
      <div className="space-y-3">
        <div className="text-center">
          <div className="text-sm font-medium">{currentType.label}</div>
          <div className="text-xs opacity-80">{currentType.description}</div>
        </div>

        <button
          onClick={generate}
          className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white py-2 rounded transition-all duration-300 hover:scale-105"
        >
          🎯 生成随机数
        </button>

        {result !== null && (
          <div className="text-center p-3 bg-white/20 rounded">
            <div className="text-sm opacity-90">当前结果</div>
            <div className="text-2xl font-bold">
              {typeof result === 'boolean' ? (result ? '✓ 真' : '✗ 假') : 
               typeof result === 'number' ? result.toFixed(6).replace(/\.?0+$/, '') : 
               result}
            </div>
          </div>
        )}

        {history.length > 0 && (
          <div className="space-y-1">
            <div className="text-sm opacity-90 text-center">历史记录</div>
            <div className="grid grid-cols-5 gap-1">
              {history.map((val, idx) => (
                <div key={idx} className="bg-white/10 p-1 rounded text-xs text-center">
                  {typeof val === 'boolean' ? (val ? '✓' : '✗') : 
                   typeof val === 'number' ? val.toFixed(2) : 
                   val.toString().slice(0, 3)}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// React组件：随机数节点图标
interface RandomIconProps {
  nodeId: string;
  status: string;
  isGenerating: boolean;
}

const RandomIcon: React.FC<RandomIconProps> = ({ nodeId, status, isGenerating }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-pink-400 to-purple-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isGenerating ? 'animate-spin' : ''}
    `}>
      <Shuffle size={16} />
    </div>
  );
};

// React组件：随机数节点属性面板
interface RandomPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const RandomProperties: React.FC<RandomPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  const randomTypes = [
    { value: 'uniform', label: '均匀分布 (0-1)' },
    { value: 'integer', label: '随机整数' },
    { value: 'normal', label: '正态分布' },
    { value: 'boolean', label: '随机布尔值' },
    { value: 'choice', label: '随机选择' }
  ];

  const currentType = config.randomType || 'uniform';

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          随机数类型
        </label>
        <select
          value={currentType}
          onChange={(e) => onConfigChange('randomType', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
        >
          {randomTypes.map(type => (
            <option key={type.value} value={type.value}>{type.label}</option>
          ))}
        </select>
      </div>

      {['uniform', 'integer'].includes(currentType) && (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              最小值
            </label>
            <input
              type="number"
              value={config.min || (currentType === 'integer' ? 1 : 0)}
              onChange={(e) => onConfigChange('min', parseFloat(e.target.value) || 0)}
              step={currentType === 'integer' ? '1' : 'any'}
              className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              最大值
            </label>
            <input
              type="number"
              value={config.max || (currentType === 'integer' ? 100 : 1)}
              onChange={(e) => onConfigChange('max', parseFloat(e.target.value) || 1)}
              step={currentType === 'integer' ? '1' : 'any'}
              className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            />
          </div>
        </>
      )}

      {currentType === 'normal' && (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              均值 (μ)
            </label>
            <input
              type="number"
              value={config.mean || 0}
              onChange={(e) => onConfigChange('mean', parseFloat(e.target.value) || 0)}
              step="any"
              className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              标准差 (σ)
            </label>
            <input
              type="number"
              value={config.stdDev || 1}
              onChange={(e) => onConfigChange('stdDev', parseFloat(e.target.value) || 1)}
              step="any"
              min="0.01"
              className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            />
          </div>
        </>
      )}

      {currentType === 'boolean' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            真值概率 (0-1)
          </label>
          <input
            type="number"
            value={config.probability || 0.5}
            onChange={(e) => onConfigChange('probability', parseFloat(e.target.value) || 0.5)}
            step="0.01"
            min="0"
            max="1"
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
          />
        </div>
      )}

      {currentType === 'choice' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            选择列表 (用逗号分隔)
          </label>
          <textarea
            value={config.choices || 'A,B,C'}
            onChange={(e) => onConfigChange('choices', e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500 resize-none"
            rows={3}
            placeholder="选项1,选项2,选项3"
          />
        </div>
      )}

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoGenerate || false}
            onChange={(e) => onConfigChange('autoGenerate', e.target.checked)}
            className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
          />
          <span className="text-sm text-gray-700">自动生成</span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          种子值 (可选，用于可重复随机)
        </label>
        <input
          type="number"
          value={config.seed || ''}
          onChange={(e) => onConfigChange('seed', e.target.value ? parseInt(e.target.value) : null)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
          placeholder="留空使用系统时间"
        />
      </div>
    </div>
  );
};

// 随机数节点类
export class ReactRandomNode extends ReactBaseNode {
  private seededRandom: (() => number) | null = null;

  constructor() {
    super('react-random', '随机数生成节点');
    this.description = '生成各种类型的随机数：均匀分布、正态分布、整数、布尔值等';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚（都是可选的）
    this.addInputPin({
      name: 'trigger',
      dataType: 'any',
      description: '触发生成',
      required: false
    });

    this.addInputPin({
      name: 'seed',
      dataType: 'number',
      description: '随机种子',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'value',
      dataType: 'any',
      description: '随机值'
    });

    this.addOutputPin({
      name: 'type',
      dataType: 'string',
      description: '随机数类型'
    });

    this.addOutputPin({
      name: 'timestamp',
      dataType: 'number',
      description: '生成时间戳'
    });

    // 默认配置
    this.config = {
      randomType: 'uniform',
      min: 0,
      max: 1,
      mean: 0,
      stdDev: 1,
      probability: 0.5,
      choices: 'A,B,C',
      autoGenerate: false,
      seed: null
    };
  }

  // 简单的线性同余生成器，用于种子随机数
  private createSeededRandom(seed: number): () => number {
    let current = seed;
    return () => {
      current = (current * 1664525 + 1013904223) % 4294967296;
      return current / 4294967296;
    };
  }

  // Box-Muller变换生成正态分布随机数
  private generateNormal(mean: number = 0, stdDev: number = 1, randomFunc: () => number = Math.random): number {
    const u1 = randomFunc();
    const u2 = randomFunc();
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
    return z0 * stdDev + mean;
  }

  async process(inputData: NodeData): Promise<NodeData> {
    const seed = inputData.seed ?? this.config.seed;
    const randomType = this.config.randomType || 'uniform';

    // 设置随机数生成器
    const randomFunc = seed ? 
      (this.seededRandom = this.createSeededRandom(seed)) : 
      Math.random;

    let value: any;

    switch (randomType) {
      case 'uniform':
        const min = this.config.min || 0;
        const max = this.config.max || 1;
        value = randomFunc() * (max - min) + min;
        break;
      
      case 'integer':
        const intMin = Math.ceil(this.config.min || 1);
        const intMax = Math.floor(this.config.max || 100);
        value = Math.floor(randomFunc() * (intMax - intMin + 1)) + intMin;
        break;
      
      case 'normal':
        const mean = this.config.mean || 0;
        const stdDev = this.config.stdDev || 1;
        value = this.generateNormal(mean, stdDev, randomFunc);
        break;
      
      case 'boolean':
        const probability = this.config.probability || 0.5;
        value = randomFunc() < probability;
        break;
      
      case 'choice':
        const choices = this.config.choices ? 
          this.config.choices.split(',').map((s: string) => s.trim()) : 
          ['A', 'B', 'C'];
        value = choices[Math.floor(randomFunc() * choices.length)];
        break;
      
      default:
        value = randomFunc();
    }

    return {
      value,
      type: randomType,
      timestamp: Date.now(),
      seed: seed || null
    };
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(RandomUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = { ...this.getIconProps(), isGenerating: false };
    return React.createElement(RandomIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(RandomProperties, props);
  }
}

// 导出组件供外部使用
export { RandomUI, RandomIcon, RandomProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-random',
  name: '随机数生成节点',
  description: '生成各种类型的随机数：均匀分布、正态分布、整数、布尔值等',
  category: '数学',
  icon: 'random',
  version: '1.0.0',
  author: 'System',
  tags: ['math', 'random', 'generator', 'probability', 'statistics', 'react']
};
