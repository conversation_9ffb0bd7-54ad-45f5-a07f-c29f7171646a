'use client';

import React, { useState } from 'react';
import { ReactBaseNode } from '../ReactBaseNode';
import { NodeData } from '../BaseNode';
import { FunctionSquare } from 'lucide-react';

// 高级数学函数类型
type MathFunction = 'sin' | 'cos' | 'tan' | 'asin' | 'acos' | 'atan' | 'log' | 'ln' | 'exp' | 'sqrt' | 'abs' | 'ceil' | 'floor' | 'round';

// React组件：高级数学节点UI
interface AdvancedMathUIProps {
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

const AdvancedMathUI: React.FC<AdvancedMathUIProps> = ({ nodeId, config, status, onAction }) => {
  const [input, setInput] = useState(config.inputValue || 0);
  const [result, setResult] = useState<number | null>(null);

  const functions = {
    sin: { label: '正弦 (sin)', description: '正弦函数' },
    cos: { label: '余弦 (cos)', description: '余弦函数' },
    tan: { label: '正切 (tan)', description: '正切函数' },
    asin: { label: '反正弦 (asin)', description: '反正弦函数' },
    acos: { label: '反余弦 (acos)', description: '反余弦函数' },
    atan: { label: '反正切 (atan)', description: '反正切函数' },
    log: { label: '对数 (log10)', description: '以10为底的对数' },
    ln: { label: '自然对数 (ln)', description: '自然对数' },
    exp: { label: '指数 (e^x)', description: '以e为底的指数函数' },
    sqrt: { label: '平方根 (√)', description: '平方根' },
    abs: { label: '绝对值 (|x|)', description: '绝对值' },
    ceil: { label: '向上取整', description: '向上取整' },
    floor: { label: '向下取整', description: '向下取整' },
    round: { label: '四舍五入', description: '四舍五入' }
  };

  const calculate = () => {
    let calcResult: number;
    const numInput = parseFloat(input.toString());
    const func = config.mathFunction || 'abs';

    try {
      switch (func) {
        case 'sin':
          calcResult = Math.sin(numInput);
          break;
        case 'cos':
          calcResult = Math.cos(numInput);
          break;
        case 'tan':
          calcResult = Math.tan(numInput);
          break;
        case 'asin':
          calcResult = Math.asin(numInput);
          break;
        case 'acos':
          calcResult = Math.acos(numInput);
          break;
        case 'atan':
          calcResult = Math.atan(numInput);
          break;
        case 'log':
          calcResult = numInput > 0 ? Math.log10(numInput) : NaN;
          break;
        case 'ln':
          calcResult = numInput > 0 ? Math.log(numInput) : NaN;
          break;
        case 'exp':
          calcResult = Math.exp(numInput);
          break;
        case 'sqrt':
          calcResult = numInput >= 0 ? Math.sqrt(numInput) : NaN;
          break;
        case 'abs':
          calcResult = Math.abs(numInput);
          break;
        case 'ceil':
          calcResult = Math.ceil(numInput);
          break;
        case 'floor':
          calcResult = Math.floor(numInput);
          break;
        case 'round':
          calcResult = Math.round(numInput);
          break;
        default:
          calcResult = numInput;
      }
    } catch (error) {
      calcResult = NaN;
    }

    setResult(calcResult);
    onAction('calculate', { input: numInput, result: calcResult, function: func });
  };

  const currentFunc = functions[config.mathFunction as MathFunction] || functions.abs;

  return (
    <div className="p-5 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg text-white min-w-[280px]">
      <div className="text-lg font-bold mb-3 text-center">📐 高级数学</div>
      
      <div className="space-y-3">
        <div className="text-center">
          <div className="text-sm font-medium">{currentFunc.label}</div>
          <div className="text-xs opacity-80">{currentFunc.description}</div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm">f(</span>
          <input
            type="number"
            value={input}
            onChange={(e) => setInput(parseFloat(e.target.value) || 0)}
            className="flex-1 p-2 rounded bg-white/20 text-white placeholder-white/70 border border-white/30"
            placeholder="输入值"
            step="any"
          />
          <span className="text-sm">)</span>
        </div>

        <button
          onClick={calculate}
          className="w-full bg-white/20 hover:bg-white/30 border border-white/30 text-white py-2 rounded transition-all duration-300"
        >
          计算
        </button>

        {result !== null && (
          <div className="text-center p-3 bg-white/20 rounded">
            <div className="text-sm opacity-90">结果</div>
            <div className="text-2xl font-bold">
              {isNaN(result) ? '未定义' : 
               isFinite(result) ? result.toFixed(8).replace(/\.?0+$/, '') : 
               result === Infinity ? '∞' : '-∞'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// React组件：高级数学节点图标
interface AdvancedMathIconProps {
  nodeId: string;
  status: string;
  isCalculating: boolean;
}

const AdvancedMathIcon: React.FC<AdvancedMathIconProps> = ({ nodeId, status, isCalculating }) => {
  return (
    <div className={`
      w-8 h-8 bg-gradient-to-br from-purple-400 to-indigo-600 rounded-lg
      flex items-center justify-center text-white shadow-md
      transition-all duration-300 cursor-pointer hover:scale-110 hover:shadow-lg
      ${isCalculating ? 'animate-pulse' : ''}
    `}>
      <FunctionSquare size={16} />
    </div>
  );
};

// React组件：高级数学节点属性面板
interface AdvancedMathPropertiesProps {
  nodeId: string;
  config: any;
  name: string;
  description: string;
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

const AdvancedMathProperties: React.FC<AdvancedMathPropertiesProps> = ({
  nodeId,
  config,
  name,
  description,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  const functionGroups = {
    '三角函数': [
      { value: 'sin', label: '正弦 (sin)' },
      { value: 'cos', label: '余弦 (cos)' },
      { value: 'tan', label: '正切 (tan)' },
      { value: 'asin', label: '反正弦 (asin)' },
      { value: 'acos', label: '反余弦 (acos)' },
      { value: 'atan', label: '反正切 (atan)' }
    ],
    '对数指数': [
      { value: 'log', label: '对数 (log10)' },
      { value: 'ln', label: '自然对数 (ln)' },
      { value: 'exp', label: '指数 (e^x)' },
      { value: 'sqrt', label: '平方根 (√)' }
    ],
    '取整函数': [
      { value: 'abs', label: '绝对值 (|x|)' },
      { value: 'ceil', label: '向上取整' },
      { value: 'floor', label: '向下取整' },
      { value: 'round', label: '四舍五入' }
    ]
  };

  return (
    <div className="p-4 bg-gray-50 rounded-lg space-y-4">
      {/* 基础属性 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          节点名称
        </label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          placeholder="自定义节点名称"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          描述
        </label>
        <textarea
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          rows={2}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          placeholder="节点描述"
        />
      </div>

      {/* 节点特定配置 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          数学函数
        </label>
        <select
          value={config.mathFunction || 'abs'}
          onChange={(e) => onConfigChange('mathFunction', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          {Object.entries(functionGroups).map(([group, functions]) => (
            <optgroup key={group} label={group}>
              {functions.map(func => (
                <option key={func.value} value={func.value}>{func.label}</option>
              ))}
            </optgroup>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          默认输入值
        </label>
        <input
          type="number"
          value={config.inputValue || 0}
          onChange={(e) => onConfigChange('inputValue', parseFloat(e.target.value) || 0)}
          step="any"
          className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        />
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.useRadians || false}
            onChange={(e) => onConfigChange('useRadians', e.target.checked)}
            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span className="text-sm text-gray-700">使用弧度制（三角函数）</span>
        </label>
      </div>

      <div>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={config.autoCalculate || false}
            onChange={(e) => onConfigChange('autoCalculate', e.target.checked)}
            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <span className="text-sm text-gray-700">自动计算</span>
        </label>
      </div>
    </div>
  );
};

// 高级数学节点类
export class ReactAdvancedMathNode extends ReactBaseNode {
  constructor() {
    super('react-advanced-math', '高级数学节点');
    this.description = '执行高级数学函数：三角函数、对数、指数、取整等';
    // 现在需要在子类构造函数中调用 initialize()
    this.initialize();
  }

  initialize(): void {
    // 输入引脚（都是可选的）
    this.addInputPin({
      name: 'input',
      dataType: 'number',
      description: '输入值',
      required: false
    });

    this.addInputPin({
      name: 'function',
      dataType: 'string',
      description: '数学函数',
      required: false
    });

    // 输出引脚
    this.addOutputPin({
      name: 'result',
      dataType: 'number',
      description: '计算结果'
    });

    this.addOutputPin({
      name: 'isValid',
      dataType: 'boolean',
      description: '结果是否有效'
    });

    // 默认配置
    this.config = {
      mathFunction: 'abs',
      inputValue: 0,
      useRadians: false,
      autoCalculate: true
    };
  }

  async process(inputData: NodeData): Promise<NodeData> {
    console.error(`🌟🌟🌟 REACT ADVANCED MATH NODE PROCESS CALLED 🌟🌟🌟`);
    const input = inputData.input ?? this.config.inputValue ?? 0;
    const mathFunction = inputData.function ?? this.config.mathFunction ?? 'abs';
    const useRadians = this.config.useRadians || false;

    console.error(`🌟🌟🌟 INPUT: ${input}, FUNCTION: ${mathFunction} 🌟🌟🌟`);
    console.error(`🌟🌟🌟 CONFIG:`, this.config);
    console.error(`🌟🌟🌟 INPUT DATA:`, inputData);

    let result: number;
    let isValid = true;

    // 对于三角函数，如果不使用弧度制，则转换为弧度
    let processedInput = input;
    if (!useRadians && ['sin', 'cos', 'tan'].includes(mathFunction)) {
      processedInput = (input * Math.PI) / 180;
    }

    try {
      switch (mathFunction) {
        case 'sin':
          result = Math.sin(processedInput);
          break;
        case 'cos':
          result = Math.cos(processedInput);
          break;
        case 'tan':
          result = Math.tan(processedInput);
          break;
        case 'asin':
          result = Math.asin(input);
          if (!useRadians) result = (result * 180) / Math.PI;
          break;
        case 'acos':
          result = Math.acos(input);
          if (!useRadians) result = (result * 180) / Math.PI;
          break;
        case 'atan':
          result = Math.atan(input);
          if (!useRadians) result = (result * 180) / Math.PI;
          break;
        case 'log':
          result = input > 0 ? Math.log10(input) : NaN;
          break;
        case 'ln':
          result = input > 0 ? Math.log(input) : NaN;
          break;
        case 'exp':
          result = Math.exp(input);
          break;
        case 'sqrt':
          result = input >= 0 ? Math.sqrt(input) : NaN;
          break;
        case 'abs':
          result = Math.abs(input);
          break;
        case 'ceil':
          result = Math.ceil(input);
          break;
        case 'floor':
          result = Math.floor(input);
          break;
        case 'round':
          result = Math.round(input);
          break;
        default:
          result = input;
          isValid = false;
      }

      if (isNaN(result) || !isFinite(result)) {
        isValid = false;
      }
    } catch (error) {
      result = NaN;
      isValid = false;
    }

    return {
      result,
      isValid,
      function: mathFunction,
      input: input
    };
  }

  renderUIComponent(): React.ReactElement {
    const props = this.getUIProps();
    return React.createElement(AdvancedMathUI, props);
  }

  renderIconComponent(): React.ReactElement {
    const props = { ...this.getIconProps(), isCalculating: false };
    return React.createElement(AdvancedMathIcon, props);
  }

  getPropertyPanelComponent(): React.ReactElement {
    const props = this.getPropertyProps();
    return React.createElement(AdvancedMathProperties, props);
  }
}

// 导出组件供外部使用
export { AdvancedMathUI, AdvancedMathIcon, AdvancedMathProperties };

// 导出节点信息
export const nodeInfo = {
  type: 'react-advanced-math',
  name: '高级数学节点',
  description: '执行高级数学函数：三角函数、对数、指数、取整等',
  category: '数学',
  icon: 'function',
  version: '1.0.0',
  author: 'System',
  tags: ['math', 'function', 'trigonometry', 'logarithm', 'advanced', 'react']
};
