/**
 * 前端工作流引擎
 * 
 * 核心设计理念：
 * 1. 工作流控制逻辑在前端执行，提供即时响应
 * 2. 节点可以自由选择前端/后端执行模式
 * 3. 前端引擎负责调度，后端负责重量级计算
 * 4. 保持与现有架构的兼容性
 */

import { EventEmitter } from 'events';
import { BaseNode, NodeStatus, ExecutionMode } from '../nodes/BaseNode';
import { ReactBaseNode } from '../nodes/ReactBaseNode';
import { WorkflowConnection, WorkflowDefinition } from './WorkflowEngine';

// 前端执行上下文
export interface FrontendExecutionContext {
  workflowId: string;
  nodeId: string;
  inputData: any;
  executionMode: ExecutionMode;
  timestamp: number;
}

// 后端调用请求
export interface BackendCallRequest {
  nodeId: string;
  method: string;
  inputData: any;
  context: FrontendExecutionContext;
}

// 后端调用响应
export interface BackendCallResponse {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
}

// 工作流执行状态
export enum FrontendWorkflowStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error'
}

/**
 * 前端工作流引擎
 * 
 * 负责在客户端管理工作流的执行流程，同时协调前后端的协同工作
 */
export class FrontendWorkflowEngine extends EventEmitter {
  private nodes: Map<string, ReactBaseNode> = new Map();
  private connections: WorkflowConnection[] = [];
  private status: FrontendWorkflowStatus = FrontendWorkflowStatus.IDLE;
  private workflowId: string = '';
  
  // 执行状态管理
  private executionQueue: string[] = [];
  private executingNodes: Set<string> = new Set();
  private completedNodes: Set<string> = new Set();
  private pausedNodes: Set<string> = new Set();
  
  // 后端通信管理
  private backendConnection: WebSocket | null = null;
  private pendingBackendCalls: Map<string, {
    resolve: (value: BackendCallResponse) => void;
    reject: (reason: any) => void;
    timeout: NodeJS.Timeout;
  }> = new Map();
  
  // 性能监控
  private executionMetrics: {
    startTime: number;
    nodeExecutionTimes: Map<string, number>;
    backendCallTimes: Map<string, number>;
  } = {
    startTime: 0,
    nodeExecutionTimes: new Map(),
    backendCallTimes: new Map()
  };

  constructor(workflowId: string) {
    super();
    this.workflowId = workflowId;
    this.initializeBackendConnection();
  }

  /**
   * 初始化后端连接
   */
  private async initializeBackendConnection(): Promise<void> {
    try {
      // 连接到后端工作流服务
      const wsUrl = `ws://localhost:3001/workflow/${this.workflowId}`;
      this.backendConnection = new WebSocket(wsUrl);

      this.backendConnection.onopen = () => {
        console.log(`🔗 Frontend engine connected to backend for workflow: ${this.workflowId}`);
        this.emit('backendConnected');
      };

      this.backendConnection.onmessage = (event) => {
        this.handleBackendMessage(JSON.parse(event.data));
      };

      this.backendConnection.onclose = () => {
        console.log(`🔌 Backend connection closed for workflow: ${this.workflowId}`);
        this.emit('backendDisconnected');
      };

      this.backendConnection.onerror = (error) => {
        console.error(`❌ Backend connection error:`, error);
        this.emit('backendError', error);
      };

    } catch (error) {
      console.error(`❌ Failed to initialize backend connection:`, error);
    }
  }

  /**
   * 加载工作流定义
   */
  async loadWorkflow(definition: WorkflowDefinition): Promise<void> {
    try {
      console.log(`📋 Loading workflow definition: ${definition.name}`);
      
      this.connections = definition.connections;
      
      // 创建节点实例
      for (const nodeData of definition.nodes) {
        await this.createNode(nodeData);
      }
      
      // 建立节点连接
      this.establishConnections();
      
      console.log(`✅ Workflow loaded: ${this.nodes.size} nodes, ${this.connections.length} connections`);
      this.emit('workflowLoaded', { nodeCount: this.nodes.size, connectionCount: this.connections.length });
      
    } catch (error) {
      console.error(`❌ Failed to load workflow:`, error);
      throw error;
    }
  }

  /**
   * 创建节点实例
   */
  private async createNode(nodeData: any): Promise<void> {
    try {
      // 使用 ClientNodeRegistry 创建节点
      const { ClientNodeRegistry } = await import('./ClientNodeRegistry');
      const registry = ClientNodeRegistry.getInstance();
      
      const node = registry.createNode(nodeData.type, nodeData.data || {});
      if (node instanceof ReactBaseNode) {
        // 设置节点ID和配置
        node.id = nodeData.id;
        node.updateConfig(nodeData.data || {});
        
        // 注册节点事件监听
        this.setupNodeEventListeners(node);
        
        this.nodes.set(node.id, node);
        console.log(`✅ Created frontend node: ${node.id} (${nodeData.type})`);
      } else {
        throw new Error(`Node ${nodeData.id} is not a ReactBaseNode`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to create node ${nodeData.id}:`, error);
      throw error;
    }
  }

  /**
   * 设置节点事件监听
   */
  private setupNodeEventListeners(node: ReactBaseNode): void {
    // 监听节点状态变化
    node.on('statusChanged', (status: NodeStatus) => {
      this.emit('nodeStatusChanged', { nodeId: node.id, status });
    });

    // 监听节点完成事件
    node.on('completed', (result: any) => {
      this.handleNodeCompleted(node.id, result);
    });

    // 监听节点错误事件
    node.on('error', (error: any) => {
      this.handleNodeError(node.id, error);
    });

    // 监听后端调用请求
    node.on('backendCallRequested', (request: BackendCallRequest) => {
      this.handleBackendCallRequest(request);
    });
  }

  /**
   * 建立节点连接
   */
  private establishConnections(): void {
    for (const connection of this.connections) {
      const sourceNode = this.nodes.get(connection.sourceNodeId);
      const targetNode = this.nodes.get(connection.targetNodeId);
      
      if (sourceNode && targetNode) {
        sourceNode.connectToNode(targetNode.id, targetNode);
        console.log(`🔗 Connected: ${sourceNode.id} -> ${targetNode.id}`);
      }
    }
  }

  /**
   * 启动工作流执行
   */
  async startWorkflow(): Promise<void> {
    if (this.status === FrontendWorkflowStatus.RUNNING) {
      console.warn('⚠️ Workflow is already running');
      return;
    }

    try {
      this.status = FrontendWorkflowStatus.RUNNING;
      this.executionMetrics.startTime = Date.now();
      
      console.log(`🚀 Starting frontend workflow: ${this.workflowId}`);
      this.emit('workflowStarted', { workflowId: this.workflowId });

      // 找到起始节点
      const startNodes = this.findStartNodes();
      if (startNodes.length === 0) {
        throw new Error('No start nodes found in workflow');
      }

      // 将起始节点加入执行队列
      startNodes.forEach(node => {
        this.executionQueue.push(node.id);
      });

      // 开始执行
      await this.processExecutionQueue();

    } catch (error) {
      this.status = FrontendWorkflowStatus.ERROR;
      this.emit('workflowError', error);
      throw error;
    }
  }

  /**
   * 查找起始节点
   */
  private findStartNodes(): ReactBaseNode[] {
    const startNodes: ReactBaseNode[] = [];
    
    for (const node of this.nodes.values()) {
      const hasInputConnections = this.connections.some(
        conn => conn.targetNodeId === node.id
      );
      
      if (!hasInputConnections) {
        startNodes.push(node);
      }
    }
    
    return startNodes;
  }

  /**
   * 处理执行队列
   */
  private async processExecutionQueue(): Promise<void> {
    while (this.executionQueue.length > 0 && this.status === FrontendWorkflowStatus.RUNNING) {
      const nodeId = this.executionQueue.shift()!;
      
      if (this.completedNodes.has(nodeId) || this.executingNodes.has(nodeId)) {
        continue;
      }

      await this.executeNode(nodeId);
    }

    // 检查工作流是否完成
    this.checkWorkflowCompletion();
  }

  /**
   * 执行单个节点
   */
  private async executeNode(nodeId: string): Promise<void> {
    const node = this.nodes.get(nodeId);
    if (!node) {
      console.error(`❌ Node not found: ${nodeId}`);
      return;
    }

    try {
      this.executingNodes.add(nodeId);
      node.setStatus(NodeStatus.RUNNING);
      
      const startTime = Date.now();
      console.log(`⚡ Executing frontend node: ${nodeId}`);

      // 获取输入数据
      const inputData = this.collectNodeInputData(nodeId);
      
      // 执行节点（前端部分）
      const result = await node.processFrontend(inputData);
      
      // 记录执行时间
      const executionTime = Date.now() - startTime;
      this.executionMetrics.nodeExecutionTimes.set(nodeId, executionTime);
      
      // 处理执行结果
      await this.handleNodeResult(nodeId, result);
      
    } catch (error) {
      console.error(`❌ Error executing node ${nodeId}:`, error);
      this.handleNodeError(nodeId, error);
    }
  }

  /**
   * 收集节点输入数据
   */
  private collectNodeInputData(nodeId: string): any {
    const inputData: any = {};
    
    // 从连接的上游节点收集数据
    const inputConnections = this.connections.filter(
      conn => conn.targetNodeId === nodeId
    );
    
    for (const connection of inputConnections) {
      const sourceNode = this.nodes.get(connection.sourceNodeId);
      if (sourceNode && this.completedNodes.has(sourceNode.id)) {
        // 获取源节点的输出数据
        const outputData = sourceNode.getOutputData(connection.sourcePinId);
        inputData[connection.targetPinId] = outputData;
      }
    }
    
    return inputData;
  }

  /**
   * 处理节点执行结果
   */
  private async handleNodeResult(nodeId: string, result: any): Promise<void> {
    const node = this.nodes.get(nodeId);
    if (!node) return;

    // 更新节点状态
    node.setStatus(NodeStatus.COMPLETED);
    this.executingNodes.delete(nodeId);
    this.completedNodes.add(nodeId);
    
    // 存储输出数据
    node.setOutputData(result);
    
    // 触发下游节点
    this.triggerDownstreamNodes(nodeId);
    
    console.log(`✅ Node completed: ${nodeId}`);
    this.emit('nodeCompleted', { nodeId, result });
  }

  /**
   * 触发下游节点
   */
  private triggerDownstreamNodes(sourceNodeId: string): void {
    const downstreamConnections = this.connections.filter(
      conn => conn.sourceNodeId === sourceNodeId
    );
    
    for (const connection of downstreamConnections) {
      const targetNodeId = connection.targetNodeId;
      
      // 检查目标节点的所有输入是否都已准备好
      if (this.areNodeInputsReady(targetNodeId)) {
        this.executionQueue.push(targetNodeId);
      }
    }
    
    // 继续处理执行队列
    this.processExecutionQueue();
  }

  /**
   * 检查节点输入是否准备好
   */
  private areNodeInputsReady(nodeId: string): boolean {
    const inputConnections = this.connections.filter(
      conn => conn.targetNodeId === nodeId
    );
    
    return inputConnections.every(connection => 
      this.completedNodes.has(connection.sourceNodeId)
    );
  }

  /**
   * 处理后端调用请求
   */
  private async handleBackendCallRequest(request: BackendCallRequest): Promise<void> {
    if (!this.backendConnection || this.backendConnection.readyState !== WebSocket.OPEN) {
      console.error(`❌ Backend connection not available for node: ${request.nodeId}`);
      return;
    }

    const callId = `${request.nodeId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      // 发送后端调用请求
      const message = {
        type: 'backend_call',
        callId,
        request
      };
      
      this.backendConnection.send(JSON.stringify(message));
      
      // 等待后端响应
      const response = await this.waitForBackendResponse(callId);
      
      // 记录调用时间
      const callTime = Date.now() - startTime;
      this.executionMetrics.backendCallTimes.set(callId, callTime);
      
      // 处理响应
      const node = this.nodes.get(request.nodeId);
      if (node) {
        node.emit('backendCallCompleted', { request, response });
      }
      
      console.log(`🔄 Backend call completed: ${request.nodeId}.${request.method} (${callTime}ms)`);
      
    } catch (error) {
      console.error(`❌ Backend call failed: ${request.nodeId}.${request.method}`, error);
      
      const node = this.nodes.get(request.nodeId);
      if (node) {
        node.emit('backendCallError', { request, error });
      }
    }
  }

  /**
   * 等待后端响应
   */
  private waitForBackendResponse(callId: string): Promise<BackendCallResponse> {
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingBackendCalls.delete(callId);
        reject(new Error(`Backend call timeout: ${callId}`));
      }, 30000); // 30秒超时

      this.pendingBackendCalls.set(callId, { resolve, reject, timeout });
    });
  }

  /**
   * 处理后端消息
   */
  private handleBackendMessage(message: any): void {
    switch (message.type) {
      case 'backend_response':
        this.handleBackendResponse(message);
        break;
      case 'backend_error':
        this.handleBackendError(message);
        break;
      default:
        console.warn(`⚠️ Unknown backend message type: ${message.type}`);
    }
  }

  /**
   * 处理后端响应
   */
  private handleBackendResponse(message: any): void {
    const { callId, response } = message;
    const pendingCall = this.pendingBackendCalls.get(callId);
    
    if (pendingCall) {
      clearTimeout(pendingCall.timeout);
      this.pendingBackendCalls.delete(callId);
      pendingCall.resolve(response);
    }
  }

  /**
   * 处理后端错误
   */
  private handleBackendError(message: any): void {
    const { callId, error } = message;
    const pendingCall = this.pendingBackendCalls.get(callId);
    
    if (pendingCall) {
      clearTimeout(pendingCall.timeout);
      this.pendingBackendCalls.delete(callId);
      pendingCall.reject(new Error(error));
    }
  }

  /**
   * 处理节点完成事件
   */
  private handleNodeCompleted(nodeId: string, result: any): void {
    this.handleNodeResult(nodeId, result);
  }

  /**
   * 处理节点错误事件
   */
  private handleNodeError(nodeId: string, error: any): void {
    const node = this.nodes.get(nodeId);
    if (node) {
      node.setStatus(NodeStatus.ERROR);
      this.executingNodes.delete(nodeId);
    }
    
    console.error(`❌ Node error: ${nodeId}`, error);
    this.emit('nodeError', { nodeId, error });
    
    // 根据错误处理策略决定是否继续执行
    // 这里可以实现不同的错误处理策略
  }

  /**
   * 检查工作流完成状态
   */
  private checkWorkflowCompletion(): void {
    const totalNodes = this.nodes.size;
    const completedCount = this.completedNodes.size;
    const executingCount = this.executingNodes.size;
    const queuedCount = this.executionQueue.length;
    
    if (executingCount === 0 && queuedCount === 0) {
      if (completedCount === totalNodes) {
        // 工作流成功完成
        this.status = FrontendWorkflowStatus.COMPLETED;
        const totalTime = Date.now() - this.executionMetrics.startTime;
        
        console.log(`🎉 Frontend workflow completed: ${this.workflowId} (${totalTime}ms)`);
        this.emit('workflowCompleted', { 
          workflowId: this.workflowId, 
          executionTime: totalTime,
          metrics: this.executionMetrics
        });
      } else {
        // 工作流异常结束
        this.status = FrontendWorkflowStatus.ERROR;
        console.error(`❌ Workflow ended abnormally: ${completedCount}/${totalNodes} nodes completed`);
        this.emit('workflowError', new Error('Workflow ended abnormally'));
      }
    }
  }

  /**
   * 暂停工作流
   */
  pauseWorkflow(): void {
    if (this.status === FrontendWorkflowStatus.RUNNING) {
      this.status = FrontendWorkflowStatus.PAUSED;
      console.log(`⏸️ Workflow paused: ${this.workflowId}`);
      this.emit('workflowPaused', { workflowId: this.workflowId });
    }
  }

  /**
   * 恢复工作流
   */
  resumeWorkflow(): void {
    if (this.status === FrontendWorkflowStatus.PAUSED) {
      this.status = FrontendWorkflowStatus.RUNNING;
      console.log(`▶️ Workflow resumed: ${this.workflowId}`);
      this.emit('workflowResumed', { workflowId: this.workflowId });
      this.processExecutionQueue();
    }
  }

  /**
   * 停止工作流
   */
  stopWorkflow(): void {
    this.status = FrontendWorkflowStatus.IDLE;
    this.executionQueue = [];
    this.executingNodes.clear();
    this.completedNodes.clear();
    
    console.log(`⏹️ Workflow stopped: ${this.workflowId}`);
    this.emit('workflowStopped', { workflowId: this.workflowId });
  }

  /**
   * 获取工作流状态
   */
  getStatus(): FrontendWorkflowStatus {
    return this.status;
  }

  /**
   * 获取执行指标
   */
  getExecutionMetrics() {
    return {
      ...this.executionMetrics,
      totalNodes: this.nodes.size,
      completedNodes: this.completedNodes.size,
      executingNodes: this.executingNodes.size,
      queuedNodes: this.executionQueue.length
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 关闭后端连接
    if (this.backendConnection) {
      this.backendConnection.close();
      this.backendConnection = null;
    }
    
    // 清理待处理的后端调用
    for (const [callId, pendingCall] of this.pendingBackendCalls) {
      clearTimeout(pendingCall.timeout);
      pendingCall.reject(new Error('Engine disposed'));
    }
    this.pendingBackendCalls.clear();
    
    // 清理节点
    for (const node of this.nodes.values()) {
      node.removeAllListeners();
    }
    this.nodes.clear();
    
    // 清理自身事件监听器
    this.removeAllListeners();
    
    console.log(`🧹 Frontend workflow engine disposed: ${this.workflowId}`);
  }
}
