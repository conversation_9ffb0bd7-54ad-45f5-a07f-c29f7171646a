import { EventEmitter } from 'events';
import { BaseNode, NodeStatus, ExecutionMode } from '../nodes/BaseNode';
import { MessageBus } from './MessageBus';
import { MessageQueue, WorkflowEvent } from './MessageQueue';
import { NodeFactory } from './NodeFactory';
// ReactNodeProxy已被移除，使用新的自包含节点架构
import { WebSocketManager } from './WebSocketManager';
import { v4 as uuidv4 } from 'uuid';

// 工作流连接定义
export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  sourcePinId: string;
  targetNodeId: string;
  targetPinId: string;
}

// 工作流定义
export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  nodes: any[]; // 序列化的节点数据
  connections: WorkflowConnection[];
  version: string;
  createdAt: Date;
  updatedAt: Date;
}

// 工作流执行状态
export enum WorkflowStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  ERROR = 'error',
  STOPPED = 'stopped'
}

// 工作流执行引擎
export class WorkflowEngine extends EventEmitter {
  private static instance: WorkflowEngine;
  private nodes: Map<string, BaseNode> = new Map();
  private connections: WorkflowConnection[] = [];
  private messageBus: MessageBus;
  private messageQueue: MessageQueue;
  private nodeFactory: NodeFactory;
  private webSocketManager: WebSocketManager;
  private status: WorkflowStatus = WorkflowStatus.IDLE;
  private executionQueue: string[] = []; // 待执行节点队列
  private executingNodes: Set<string> = new Set(); // 正在执行的节点
  private completedNodes: Set<string> = new Set(); // 已完成的节点
  private pausedNodes: Set<string> = new Set(); // 暂停的节点（等待用户交互）
  private threadedNodes: Set<string> = new Set(); // 线程化节点
  private workflowId: string = '';

  // 前后端分离支持
  private isThreadedMode: boolean = false;
  private wsPort: number = 8080;

  constructor(workflowId?: string, nodeFactory?: NodeFactory, messageQueue?: MessageQueue) {
    super();
    this.workflowId = workflowId || '';
    this.messageBus = new MessageBus();
    this.messageQueue = messageQueue || MessageQueue.getInstance();
    this.nodeFactory = nodeFactory || NodeFactory.getInstance();
    this.webSocketManager = WebSocketManager.getInstance();

    this.setupEventHandlers();
    this.setupWebSocketListeners();
  }

  // 获取单例实例（向后兼容）
  static getInstance(): WorkflowEngine {
    if (!WorkflowEngine.instance) {
      WorkflowEngine.instance = new WorkflowEngine();
    }
    return WorkflowEngine.instance;
  }

  // isReactNodeType方法已移除，不再需要区分React节点类型

  // 设置事件处理器
  private setupEventHandlers(): void {
    // 监听消息总线事件
    this.messageBus.on('messageDelivered', (message) => {
      this.emit('messageDelivered', message);
    });

    this.messageBus.on('messageError', (error) => {
      this.emit('messageError', error);
    });
  }

  // 设置WebSocket监听器
  private setupWebSocketListeners(): void {
    this.webSocketManager.on('clientConnected', ({ clientId }) => {
      console.log(`🔗 WebSocket client connected: ${clientId}`);
    });

    this.webSocketManager.on('clientDisconnected', ({ clientId, nodeId }) => {
      console.log(`🔌 WebSocket client disconnected: ${clientId}, node: ${nodeId}`);

      // 如果是线程化节点断开连接，标记为错误状态
      if (nodeId && this.threadedNodes.has(nodeId)) {
        const node = this.nodes.get(nodeId);
        if (node) {
          node.status = NodeStatus.ERROR;
          this.emit('nodeStatusChanged', { nodeId, status: NodeStatus.ERROR });
        }
      }
    });

    this.webSocketManager.on('messageRouted', ({ message, sourceClientId }) => {
      console.log(`📨 Message routed from ${sourceClientId}:`, message.type);
    });

    this.webSocketManager.on('serverError', (error) => {
      console.error('❌ WebSocket server error:', error);
      this.emit('webSocketError', error);
    });
  }

  // 发送工作流事件到消息队列
  private async broadcastEvent(eventType: string, data: any = {}): Promise<void> {
    const event: WorkflowEvent = {
      id: uuidv4(),
      type: eventType,
      workflowId: this.workflowId,
      timestamp: new Date().toISOString(),
      data
    };

    try {
      // 发送到消息队列
      await this.messageQueue.publishEvent(event);

      // 同时发送到本地EventEmitter（向后兼容）
      this.emit(eventType, data);

      console.log(`Broadcasting event ${eventType}:`, data);
    } catch (error) {
      console.error(`Failed to broadcast event ${eventType}:`, error);
      // 降级到本地EventEmitter
      this.emit(eventType, data);
    }
  }

  // 设置工作流ID（如果构造时未设置）
  setWorkflowId(workflowId: string): void {
    this.workflowId = workflowId;
  }

  // 加载工作流定义
  async loadWorkflow(definition: WorkflowDefinition): Promise<void> {
    try {
      this.clear();
      
      // 创建节点实例
      for (const nodeData of definition.nodes) {
        let node: BaseNode;

        try {
          node = this.nodeFactory.createNode(nodeData.type, nodeData.config);
          console.log(`✅✅✅ SUCCESSFULLY CREATED NODE WITH NODEFACTORY: ${nodeData.type} ✅✅✅`);
        } catch (factoryError) {
          console.log(`❌❌❌ NODEFACTORY FAILED FOR ${nodeData.type} ❌❌❌`, factoryError);
          throw factoryError; // 所有节点都应该通过NodeFactory创建，不再使用ReactNodeProxy
        }

        // 恢复节点状态
        console.log(`🔧 Setting node ID: ${nodeData.id} for node type: ${nodeData.type}`);
        node.id = nodeData.id; // 设置正确的节点ID
        node.name = nodeData.name;
        node.description = nodeData.description;
        node.position = nodeData.position;
        console.log(`🔧 Node after ID setting: id=${node.id}, type=${nodeData.type}`);

        this.addNode(node);
        console.log(`🔧 Node added to engine: ${node.id}`);
        console.log(`🔧 Total nodes in engine: ${this.nodes.size}`);
      }

      // 设置连接
      this.connections = [...definition.connections];
      this.setupConnections();

      this.emit('workflowLoaded', definition);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  // 添加节点
  addNode(node: BaseNode): void {
    this.nodes.set(node.id, node);
    
    // 监听节点事件
    node.on('statusChanged', async (status) => {
      await this.handleNodeStatusChange(node.id, status);
    });

    node.on('completed', async (outputData) => {
      await this.handleNodeCompleted(node.id, outputData);
    });

    node.on('error', (error) => {
      this.handleNodeError(node.id, error);
    });

    node.on('outputDataChanged', async ({ pinId, data }) => {
      await this.broadcastEvent('nodeOutputChanged', {
        workflowId: this.workflowId,
        nodeId: node.id,
        pinId,
        data
      });
    });

    node.on('showForm', (formConfig) => {
      this.emit('showForm', formConfig);
    });

    this.emit('nodeAdded', node);
  }

  // 移除节点
  removeNode(nodeId: string): boolean {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return false;
    }

    // 清理连接
    this.connections = this.connections.filter(
      conn => conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
    );

    // 清理节点
    node.destroy();
    this.nodes.delete(nodeId);

    this.emit('nodeRemoved', nodeId);
    return true;
  }

  // 添加连接
  addConnection(connection: WorkflowConnection): void {
    this.connections.push(connection);
    this.setupConnection(connection);
    this.emit('connectionAdded', connection);
  }

  // 移除连接
  removeConnection(connectionId: string): boolean {
    const index = this.connections.findIndex(conn => conn.id === connectionId);
    if (index === -1) {
      return false;
    }

    this.connections.splice(index, 1);
    this.emit('connectionRemoved', connectionId);
    return true;
  }

  // 设置所有连接
  private setupConnections(): void {
    this.connections.forEach(connection => {
      this.setupConnection(connection);
    });
  }

  // 设置单个连接
  private setupConnection(connection: WorkflowConnection): void {
    console.log(`🔗 Setting up connection: ${connection.id}`);
    console.log(`🔗 Source node ID: ${connection.sourceNodeId}, Target node ID: ${connection.targetNodeId}`);
    console.log(`🔗 Available nodes in engine: ${Array.from(this.nodes.keys()).join(', ')}`);

    const sourceNode = this.nodes.get(connection.sourceNodeId);
    const targetNode = this.nodes.get(connection.targetNodeId);

    console.log(`🔗 Source node found: ${!!sourceNode}, Target node found: ${!!targetNode}`);

    if (!sourceNode || !targetNode) {
      console.warn(`Invalid connection: ${connection.id}`);
      console.warn(`🔗 Missing nodes - Source: ${!!sourceNode}, Target: ${!!targetNode}`);
      return;
    }

    // 监听源节点的输出数据变化
    sourceNode.on('outputDataChanged', ({ pinId, data }) => {
      // 发射节点输出变化事件
      this.emit('nodeOutputChanged', {
        nodeId: connection.sourceNodeId,
        pinId,
        data
      });

      if (pinId === connection.sourcePinId) {
        // 将数据传递给目标节点
        targetNode.setInputData(connection.targetPinId, data);

        // 发送消息
        this.messageBus.publishNodeData(
          connection.sourceNodeId,
          connection.targetNodeId,
          connection.sourcePinId,
          connection.targetPinId,
          data
        );
      }
    });
  }

  // ==================== 线程化支持方法 ====================

  /**
   * 启用线程化模式
   */
  async enableThreadedMode(wsPort: number = 8080): Promise<void> {
    if (this.isThreadedMode) {
      console.warn('⚠️ Threaded mode is already enabled');
      return;
    }

    try {
      this.wsPort = wsPort;

      // 启动WebSocket服务器
      await this.webSocketManager.startServer(wsPort);

      this.isThreadedMode = true;
      console.log(`🧵 Threaded mode enabled on port ${wsPort}`);

      this.emit('threadedModeEnabled', { port: wsPort });

    } catch (error) {
      console.error('❌ Failed to enable threaded mode:', error);
      throw error;
    }
  }

  /**
   * 禁用线程化模式
   */
  async disableThreadedMode(): Promise<void> {
    if (!this.isThreadedMode) {
      return;
    }

    try {
      // 停止所有线程化节点
      await this.stopAllThreadedNodes();

      // 停止WebSocket服务器
      await this.webSocketManager.stopServer();

      this.isThreadedMode = false;
      this.threadedNodes.clear();

      console.log('🛑 Threaded mode disabled');
      this.emit('threadedModeDisabled');

    } catch (error) {
      console.error('❌ Failed to disable threaded mode:', error);
      throw error;
    }
  }

  /**
   * 启动指定节点的线程
   */
  async startNodeThread(nodeId: string): Promise<void> {
    const node = this.nodes.get(nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }

    if (this.threadedNodes.has(nodeId)) {
      console.warn(`⚠️ Node ${nodeId} is already threaded`);
      return;
    }

    try {
      // 设置WebSocket URL
      if (this.isThreadedMode) {
        node.wsUrl = `ws://localhost:${this.wsPort}`;
      }

      // 启动节点线程
      await node.startThread();

      this.threadedNodes.add(nodeId);
      console.log(`🧵 Started thread for node ${nodeId}`);

      this.emit('nodeThreadStarted', { nodeId });

    } catch (error) {
      console.error(`❌ Failed to start thread for node ${nodeId}:`, error);
      throw error;
    }
  }

  /**
   * 停止指定节点的线程
   */
  async stopNodeThread(nodeId: string): Promise<void> {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }

    if (!this.threadedNodes.has(nodeId)) {
      return;
    }

    try {
      await node.stopThread();
      this.threadedNodes.delete(nodeId);

      console.log(`🛑 Stopped thread for node ${nodeId}`);
      this.emit('nodeThreadStopped', { nodeId });

    } catch (error) {
      console.error(`❌ Failed to stop thread for node ${nodeId}:`, error);
      throw error;
    }
  }

  /**
   * 启动所有节点的线程
   */
  async startAllThreadedNodes(): Promise<void> {
    if (!this.isThreadedMode) {
      await this.enableThreadedMode();
    }

    const startPromises = Array.from(this.nodes.keys()).map(nodeId =>
      this.startNodeThread(nodeId).catch(error => {
        console.error(`❌ Failed to start thread for node ${nodeId}:`, error);
      })
    );

    await Promise.all(startPromises);
    console.log(`🧵 Started threads for ${this.threadedNodes.size} nodes`);
  }

  /**
   * 停止所有线程化节点
   */
  async stopAllThreadedNodes(): Promise<void> {
    const stopPromises = Array.from(this.threadedNodes).map(nodeId =>
      this.stopNodeThread(nodeId).catch(error => {
        console.error(`❌ Failed to stop thread for node ${nodeId}:`, error);
      })
    );

    await Promise.all(stopPromises);
    console.log('🛑 Stopped all threaded nodes');
  }

  /**
   * 设置节点执行模式
   */
  setNodeExecutionMode(nodeId: string, mode: ExecutionMode): void {
    const node = this.nodes.get(nodeId);
    if (node) {
      node.setExecutionMode(mode);
      console.log(`🔧 Set execution mode for node ${nodeId}: ${mode}`);
    }
  }

  /**
   * 批量设置节点执行模式
   */
  setAllNodesExecutionMode(mode: ExecutionMode): void {
    this.nodes.forEach((node, nodeId) => {
      node.setExecutionMode(mode);
    });
    console.log(`🔧 Set execution mode for all nodes: ${mode}`);
  }

  // 启动工作流引擎（别名方法）
  async start(): Promise<void> {
    return this.startWorkflow();
  }

  // 开始执行工作流
  async startWorkflow(): Promise<void> {
    if (this.status === WorkflowStatus.RUNNING) {
      console.warn('Workflow is already running');
      return;
    }

    try {
      this.status = WorkflowStatus.RUNNING;
      await this.broadcastEvent('statusChanged', { status: this.status });
      await this.broadcastEvent('workflowStarted', { workflowId: this.workflowId });

      // 找到起始节点（没有输入连接的节点）
      const startNodes = this.findStartNodes();
      
      if (startNodes.length === 0) {
        throw new Error('No start nodes found in workflow');
      }

      // 将起始节点加入执行队列
      startNodes.forEach(node => {
        this.executionQueue.push(node.id);
      });

      // 开始执行
      await this.processExecutionQueue();

    } catch (error) {
      this.status = WorkflowStatus.ERROR;
      this.emit('statusChanged', this.status);
      this.emit('error', error);
      throw error;
    }
  }

  // 暂停工作流
  pauseWorkflow(): void {
    if (this.status === WorkflowStatus.RUNNING) {
      this.status = WorkflowStatus.PAUSED;
      this.emit('statusChanged', this.status);
      this.emit('workflowPaused');
    }
  }

  // 恢复工作流
  async resumeWorkflow(): Promise<void> {
    if (this.status === WorkflowStatus.PAUSED) {
      this.status = WorkflowStatus.RUNNING;
      this.emit('statusChanged', this.status);
      this.emit('workflowResumed');
      
      await this.processExecutionQueue();
    }
  }

  // 停止工作流引擎（别名方法）
  async stop(): Promise<void> {
    this.stopWorkflow();
  }

  // 停止工作流
  stopWorkflow(): void {
    this.status = WorkflowStatus.STOPPED;
    this.executionQueue = [];
    this.executingNodes.clear();
    this.pausedNodes.clear();
    
    // 重置所有节点状态
    this.nodes.forEach(node => {
      if (node.status === NodeStatus.RUNNING || node.status === NodeStatus.WAITING) {
        node.status = NodeStatus.IDLE;
        node.emit('statusChanged', node.status);
      }
    });

    this.emit('statusChanged', this.status);
    this.emit('workflowStopped');
  }

  // 处理执行队列
  private async processExecutionQueue(): Promise<void> {
    while (this.executionQueue.length > 0 && this.status === WorkflowStatus.RUNNING) {
      const nodeId = this.executionQueue.shift()!;
      
      if (this.completedNodes.has(nodeId) || this.executingNodes.has(nodeId)) {
        continue;
      }

      await this.executeNode(nodeId);
    }

    // 检查是否所有节点都已完成
    if (this.executionQueue.length === 0 && this.executingNodes.size === 0 && this.pausedNodes.size === 0) {
      this.status = WorkflowStatus.COMPLETED;
      await this.broadcastEvent('statusChanged', { status: this.status });
      await this.broadcastEvent('workflowCompleted', { workflowId: this.workflowId });
    }
  }

  // 执行单个节点
  private async executeNode(nodeId: string): Promise<void> {
    console.log(`🚀🚀🚀 WORKFLOW ENGINE EXECUTE NODE CALLED: ${nodeId} 🚀🚀🚀`);

    const node = this.nodes.get(nodeId);
    if (!node) {
      console.log(`❌❌❌ NODE ${nodeId} NOT FOUND IN NODES MAP ❌❌❌`);
      console.log(`❌ Available nodes:`, Array.from(this.nodes.keys()));
      return;
    }

    console.log(`⚡⚡⚡ ABOUT TO EXECUTE NODE ${nodeId}, TYPE: ${node.type} ⚡⚡⚡`);
    console.log(`⚡ Node instance:`, node);
    console.log(`⚡ Node constructor:`, node.constructor.name);

    try {
      this.executingNodes.add(nodeId);
      console.log(`⚡⚡⚡ CALLING NODE.EXECUTE() FOR ${nodeId} ⚡⚡⚡`);
      await node.execute();
      console.log(`⚡⚡⚡ NODE.EXECUTE() COMPLETED FOR ${nodeId} ⚡⚡⚡`);
    } catch (error) {
      console.log(`❌❌❌ ERROR EXECUTING NODE ${nodeId} ❌❌❌`, error);
      this.handleNodeError(nodeId, error);
    }
  }

  // 处理节点状态变化
  private async handleNodeStatusChange(nodeId: string, status: NodeStatus): Promise<void> {
    await this.broadcastEvent('nodeStatusChanged', {
      workflowId: this.workflowId,
      nodeId,
      status
    });

    if (status === NodeStatus.WAITING) {
      // 节点等待用户交互
      this.pausedNodes.add(nodeId);
      this.executingNodes.delete(nodeId);
    }
  }

  // 处理节点完成
  private async handleNodeCompleted(nodeId: string, outputData: any): Promise<void> {
    this.completedNodes.add(nodeId);
    this.executingNodes.delete(nodeId);
    this.pausedNodes.delete(nodeId);

    // 找到下游节点并加入执行队列
    const downstreamNodes = this.findDownstreamNodes(nodeId);
    downstreamNodes.forEach(downstreamNodeId => {
      if (!this.completedNodes.has(downstreamNodeId) && 
          !this.executingNodes.has(downstreamNodeId) &&
          !this.executionQueue.includes(downstreamNodeId)) {
        
        // 检查下游节点的所有输入是否都已准备好
        if (this.areNodeInputsReady(downstreamNodeId)) {
          this.executionQueue.push(downstreamNodeId);
        }
      }
    });

    await this.broadcastEvent('nodeCompleted', {
      workflowId: this.workflowId,
      nodeId,
      outputData
    });

    // 继续处理队列
    if (this.status === WorkflowStatus.RUNNING) {
      await this.processExecutionQueue();
    }
  }

  // 处理节点错误
  private handleNodeError(nodeId: string, error: any): void {
    this.executingNodes.delete(nodeId);
    this.status = WorkflowStatus.ERROR;
    
    this.emit('statusChanged', this.status);
    this.emit('nodeError', { nodeId, error });
    this.emit('workflowError', error);
  }

  // 找到起始节点
  private findStartNodes(): BaseNode[] {
    const nodesWithInputs = new Set(
      this.connections.map(conn => conn.targetNodeId)
    );

    return Array.from(this.nodes.values()).filter(
      node => !nodesWithInputs.has(node.id)
    );
  }

  // 找到下游节点
  private findDownstreamNodes(nodeId: string): string[] {
    return this.connections
      .filter(conn => conn.sourceNodeId === nodeId)
      .map(conn => conn.targetNodeId);
  }

  // 检查节点输入是否准备好
  private areNodeInputsReady(nodeId: string): boolean {
    const inputConnections = this.connections.filter(
      conn => conn.targetNodeId === nodeId
    );

    return inputConnections.every(conn =>
      this.completedNodes.has(conn.sourceNodeId)
    );
  }

  // 更新节点输出数据
  private updateNodeOutput(nodeId: string, pinId: string, data: any): void {
    console.log(`Updating node output: ${nodeId} ${pinId}`, data);

    this.emit('nodeOutputChanged', {
      nodeId: nodeId,
      pinId: pinId,
      data: data
    });
  }

  // 处理用户交互完成
  async handleUserInteraction(nodeId: string, data: any): Promise<void> {
    const node = this.nodes.get(nodeId);
    if (!node) {
      console.warn(`Node ${nodeId} not found for user interaction`);
      return;
    }

    console.log(`Handling user interaction for node ${nodeId}:`, data);

    // 提取实际的交互数据（处理嵌套的数据结构）
    const interactionData = data.interactionData || data;
    console.log(`Extracted interaction data:`, interactionData);

    // 检查是否是表单提交
    if (interactionData.action === 'formSubmit') {
      console.log(`Form submitted for node ${nodeId} with data:`, interactionData.data);

      // 触发节点的formSubmitted事件
      if (typeof (node as any).emit === 'function') {
        (node as any).emit('formSubmitted', interactionData.data);
        console.log(`Emitted formSubmitted event for node ${nodeId}`);
      }

      // 如果节点有submitForm方法，也调用它
      if (typeof (node as any).submitForm === 'function') {
        (node as any).submitForm(interactionData.data);
        console.log(`Called submitForm method for node ${nodeId}`);
      }

      // 对于表单节点，手动更新状态并完成节点
      if ((node as any).nodeType === 'react-form-input') {
        console.log(`Manually completing form node ${nodeId}`);

        // 更新节点状态为运行中
        (node as any).status = NodeStatus.RUNNING;
        this.emit('nodeStatusChanged', {
          nodeId: nodeId,
          status: NodeStatus.RUNNING
        });

        // 设置输出数据
        const outputData = {
          formData: interactionData.data,
          completed: true
        };

        // 更新节点输出
        this.updateNodeOutput(nodeId, 'formData', outputData.formData);
        this.updateNodeOutput(nodeId, 'completed', outputData.completed);

        // 标记节点为已完成
        this.completedNodes.add(nodeId);
        (node as any).status = NodeStatus.COMPLETED;
        this.emit('nodeStatusChanged', {
          nodeId: nodeId,
          status: NodeStatus.COMPLETED
        });

        this.emit('nodeCompleted', {
          nodeId: nodeId,
          outputData: outputData
        });

        console.log(`Form node ${nodeId} completed with output:`, outputData);

        // 继续执行后续节点
        this.processExecutionQueue();
      }

      // 恢复节点执行
      if (this.pausedNodes.has(nodeId)) {
        this.pausedNodes.delete(nodeId);
        console.log(`Removed node ${nodeId} from paused nodes`);
      }
    } else if (interactionData.action === 'formCancel') {
      // 处理表单取消
      console.log(`Form cancelled for node ${nodeId}`);

      // 触发节点的formCancelled事件
      if (typeof (node as any).emit === 'function') {
        (node as any).emit('formCancelled');
      }

      if (this.pausedNodes.has(nodeId)) {
        this.pausedNodes.delete(nodeId);
        // 表单取消时，可以选择跳过这个节点或者重新等待
      }
    } else if (interactionData.action === 'calculate') {
      // 处理计算动作 - 触发节点执行
      console.log(`🎯🎯🎯 CALCULATE ACTION RECEIVED FOR NODE ${nodeId} 🎯🎯🎯`);
      console.log(`🎯 Calculate data:`, interactionData.data);

      try {
        // 如果节点有计算数据，先更新节点的输出数据
        if (interactionData.data && typeof interactionData.data === 'object') {
          Object.keys(interactionData.data).forEach(key => {
            // 查找对应的输出引脚
            const outputPin = (node as any).outputs?.find((pin: any) => pin.name === key);
            if (outputPin) {
              console.log(`🎯 Setting output data for pin ${key}:`, interactionData.data[key]);
              this.updateNodeOutput(nodeId, outputPin.id, interactionData.data[key]);
            }
          });
        }

        // 触发节点执行（如果节点支持）
        if (typeof (node as any).execute === 'function') {
          console.log(`🎯 Triggering node execution for ${nodeId}`);
          await (node as any).execute();
        } else {
          console.log(`🎯 Node ${nodeId} does not support execution, using UI calculation result`);

          // 如果节点不支持执行，直接标记为完成
          (node as any).status = NodeStatus.COMPLETED;
          this.completedNodes.add(nodeId);
          this.emit('nodeStatusChanged', {
            nodeId: nodeId,
            status: NodeStatus.COMPLETED
          });

          this.emit('nodeCompleted', {
            nodeId: nodeId,
            outputData: interactionData.data || {}
          });
        }

      } catch (error) {
        console.error(`🎯 Error handling calculate action for node ${nodeId}:`, error);
      }
    }
  }

  // 获取工作流状态
  getStatus(): WorkflowStatus {
    return this.status;
  }

  // 获取所有节点
  getNodes(): BaseNode[] {
    return Array.from(this.nodes.values());
  }

  // 获取节点状态统计
  getNodeStatistics(): {
    total: number;
    idle: number;
    running: number;
    completed: number;
    error: number;
    waiting: number;
  } {
    const stats = {
      total: this.nodes.size,
      idle: 0,
      running: 0,
      completed: 0,
      error: 0,
      waiting: 0
    };

    this.nodes.forEach(node => {
      switch (node.status) {
        case NodeStatus.IDLE:
          stats.idle++;
          break;
        case NodeStatus.RUNNING:
          stats.running++;
          break;
        case NodeStatus.COMPLETED:
          stats.completed++;
          break;
        case NodeStatus.ERROR:
          stats.error++;
          break;
        case NodeStatus.WAITING:
          stats.waiting++;
          break;
      }
    });

    return stats;
  }

  // 清理工作流
  clear(): void {
    this.stopWorkflow();
    
    this.nodes.forEach(node => node.destroy());
    this.nodes.clear();
    this.connections = [];
    this.completedNodes.clear();
    
    this.status = WorkflowStatus.IDLE;
  }

  // 销毁引擎
  destroy(): void {
    this.clear();
    this.messageBus.destroy();
    this.removeAllListeners();
  }
}
