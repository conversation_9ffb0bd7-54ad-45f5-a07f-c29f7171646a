import { WorkflowEngine, WorkflowDefinition } from './WorkflowEngine';
import { NodeFactory } from './NodeFactory';
import { NodeRegistry } from './NodeRegistry';
import { MessageQueue } from './MessageQueue';
import { WorkflowDatabase } from '../database/Database';
import { BaseNode } from '../nodes/BaseNode';
// ReactNodeProxy已被移除，使用新的自包含节点架构

// 全局单例实例存储
declare global {
  var __workflowManagerInstance: WorkflowManager | undefined;
}

// 工作流管理器 - 连接前端和后端的桥梁
export class WorkflowManager {
  private static instance: WorkflowManager;
  private engines: Map<string, WorkflowEngine> = new Map();
  private nodeFactory: NodeFactory;
  private database: WorkflowDatabase;
  private messageQueue: MessageQueue;

  private constructor() {
    this.nodeFactory = NodeFactory.getInstance();
    this.database = WorkflowDatabase.getInstance();
    this.messageQueue = MessageQueue.getInstance();
    console.log('WorkflowManager constructor called. Creating new instance.');
  }

  static getInstance(): WorkflowManager {
    // 在开发环境中使用全局变量来确保真正的单例
    if (process.env.NODE_ENV === 'development') {
      if (!global.__workflowManagerInstance) {
        console.log('Creating new WorkflowManager instance (development mode)');
        global.__workflowManagerInstance = new WorkflowManager();
      } else {
        console.log('Reusing existing WorkflowManager instance (development mode)');
      }
      return global.__workflowManagerInstance;
    }

    // 生产环境使用标准单例模式
    if (!WorkflowManager.instance) {
      console.log('Creating new WorkflowManager instance (production mode)');
      WorkflowManager.instance = new WorkflowManager();
    } else {
      console.log('Reusing existing WorkflowManager instance (production mode)');
    }
    return WorkflowManager.instance;
  }

  // 初始化管理器
  async initialize(): Promise<void> {
    try {
      console.log('Initializing WorkflowManager...');
      
      // 数据库已经在构造函数中初始化了，不需要额外的 initialize 调用
      console.log('Database already initialized in constructor');
      
      // 初始化节点注册表
      await NodeRegistry.initialize();

      // 初始化消息队列（非阻塞）
      this.messageQueue.connect().catch(error => {
        console.warn('MessageQueue initialization failed, using memory mode:', error.code || error.message);
      });

      // 所有节点现在都通过ClientNodeRegistry在客户端动态注册
      // 服务器端不再需要硬编码任何节点信息
      console.log('All nodes are dynamically registered through ClientNodeRegistry on client-side');
      console.log('WorkflowManager initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize WorkflowManager:', error);
    }
  }

  // 从React Flow数据创建工作流
  async createWorkflowFromReactFlow(reactFlowData: any): Promise<string> {
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // 创建工作流引擎实例
      const engine = new WorkflowEngine(workflowId, this.nodeFactory, this.messageQueue);

      // 转换React Flow数据为工作流定义
      const definition: WorkflowDefinition = {
        id: workflowId,
        name: `工作流_${new Date().toLocaleString()}`,
        description: '通过React Flow创建的工作流',
        version: '1.0.0',
        createdAt: new Date(),
        updatedAt: new Date(),
        nodes: reactFlowData.nodes.map((node: any) => {
          console.log(`🔍🔍🔍 CONVERTING REACT FLOW NODE:`, node);
          console.log(`🔍 Original type: ${node.type}, data.nodeType: ${node.data.nodeType}`);
          const convertedNode = {
            id: node.id,
            type: node.data.nodeType || node.type, // 使用 data.nodeType 而不是 type
            name: node.data.name || node.data.label || node.data.nodeType || node.type,
            description: node.data.description || '',
            position: node.position,
            config: node.data
          };
          console.log(`🔍 Converted node:`, convertedNode);
          return convertedNode;
        }),
        connections: reactFlowData.edges.map((edge: any) => ({
          id: edge.id,
          sourceNodeId: edge.source,
          targetNodeId: edge.target,
          sourcePinId: edge.sourceHandle,
          targetPinId: edge.targetHandle
        }))
      };

      // 加载工作流定义
      await engine.loadWorkflow(definition);

      // 存储引擎实例
      this.engines.set(workflowId, engine);

      console.log(`Workflow created from React Flow: ${workflowId}`);
      return workflowId;
    } catch (error) {
      console.error('Failed to create workflow from React Flow:', error);
      throw error;
    }
  }

  // 创建工作流
  async createWorkflow(definition: WorkflowDefinition): Promise<string> {
    const workflowId = `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // 暂时跳过数据库保存，专注于让工作流运行
      console.log(`Workflow created in memory: ${workflowId}`);
      return workflowId;
    } catch (error) {
      console.error('Failed to create workflow:', error);
      throw error;
    }
  }

  // 运行工作流（别名方法）
  async runWorkflow(workflowId: string): Promise<{ success: boolean; message?: string }> {
    try {
      console.log(`Running workflow: ${workflowId}`);

      // 获取已创建的工作流引擎
      const engine = this.engines.get(workflowId);
      if (!engine) {
        throw new Error(`Workflow engine not found: ${workflowId}`);
      }

      // 启动工作流
      await engine.start();

      console.log(`Workflow started successfully: ${workflowId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to run workflow ${workflowId}:`, error);
      return { success: false, message: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // 启动工作流
  async startWorkflow(workflowId: string, workflowDefinition?: WorkflowDefinition): Promise<{ success: boolean; message?: string }> {
    try {
      console.log(`Starting workflow: ${workflowId}`);

      // 如果没有提供工作流定义，说明这是一个已存在的工作流
      if (!workflowDefinition) {
        throw new Error(`Workflow definition not provided for: ${workflowId}`);
      }

      // 创建工作流引擎实例
      const engine = new WorkflowEngine(workflowId, this.nodeFactory, this.messageQueue);

      // 加载工作流定义
      await engine.loadWorkflow(workflowDefinition);

      // 存储引擎实例
      this.engines.set(workflowId, engine);

      // 启动工作流
      await engine.start();

      console.log(`Workflow started successfully: ${workflowId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to start workflow ${workflowId}:`, error);
      return { success: false, message: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // 停止工作流
  async stopWorkflow(workflowId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const engine = this.engines.get(workflowId);
      if (!engine) {
        throw new Error(`Workflow engine not found: ${workflowId}`);
      }

      await engine.stop();

      console.log(`Workflow stopped: ${workflowId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to stop workflow ${workflowId}:`, error);
      return { success: false, message: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // 获取工作流状态
  async getWorkflowStatus(workflowId: string): Promise<any> {
    const engine = this.engines.get(workflowId);
    if (!engine) {
      return { status: 'not_found' };
    }
    return engine.getStatus();
  }

  // 获取工作流结果
  async getWorkflowResults(workflowId: string): Promise<any> {
    const engine = this.engines.get(workflowId);
    if (!engine) {
      return null;
    }
    // 暂时返回引擎状态，因为 getResults 方法可能不存在
    return engine.getStatus();
  }

  // 创建节点实例 - 完全动态，无硬编码
  async createNodeInstance(nodeData: any): Promise<BaseNode | null> {
    try {
      console.log(`🚀🚀🚀 ATTEMPTING TO CREATE NODE WITH NODEFACTORY: ${nodeData.data.nodeType} 🚀🚀🚀`);

      // 优先使用NodeFactory创建真正的节点实例
      let node: BaseNode | null = null;

      try {
        node = this.nodeFactory.createNode(nodeData.data.nodeType, nodeData.data);
        console.log(`✅✅✅ SUCCESSFULLY CREATED NODE WITH NODEFACTORY: ${nodeData.data.nodeType} ✅✅✅`);
      } catch (factoryError) {
        console.log(`❌❌❌ NODEFACTORY FAILED FOR ${nodeData.data.nodeType} ❌❌❌`, factoryError);
        // 所有节点都应该通过NodeFactory创建，不再使用ReactNodeProxy
      }

      if (!node) {
        console.log(`❌❌❌ FAILED TO CREATE NODE: ${nodeData.data.nodeType} ❌❌❌`);
        return null;
      }

      // 设置节点ID
      if (nodeData.id) {
        (node as any).id = nodeData.id;
      }

      return node;
    } catch (error) {
      console.log(`❌❌❌ CRITICAL ERROR CREATING NODE ${nodeData.data.nodeType} ❌❌❌`, error);
      return null;
    }
  }

  // isReactNodeType方法已移除，不再需要区分React节点类型

  // 获取所有工作流
  async getAllWorkflows(): Promise<any[]> {
    // 暂时返回空数组，专注于让工作流运行
    return [];
  }

  // 处理用户交互
  async handleUserInteraction(workflowId: string, nodeId: string, interactionData: any): Promise<void> {
    const engine = this.engines.get(workflowId);
    if (!engine) {
      throw new Error(`Workflow engine not found: ${workflowId}`);
    }

    await engine.handleUserInteraction(nodeId, interactionData);
  }

  // 删除工作流
  async deleteWorkflow(workflowId: string): Promise<{ success: boolean; message?: string }> {
    try {
      // 先停止工作流（如果正在运行）
      if (this.engines.has(workflowId)) {
        await this.stopWorkflow(workflowId);
        this.engines.delete(workflowId);
      }

      // 暂时跳过数据库删除，专注于让工作流运行
      console.log(`Skipping database deletion for: ${workflowId}`);
      
      console.log(`Workflow deleted: ${workflowId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to delete workflow ${workflowId}:`, error);
      return { success: false, message: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // 清理工作流引擎
  cleanupWorkflow(workflowId: string): void {
    const engine = this.engines.get(workflowId);
    if (engine) {
      engine.destroy();
      this.engines.clear();
    }
  }

  // 清理所有资源
  destroy(): void {
    this.engines.forEach(engine => engine.destroy());
    this.engines.clear();
  }
}
