import { EventEmitter } from 'events';
import { BaseNode, WSMessage, WSMessageType } from '../nodes/BaseNode';
import { WebSocketManager } from './WebSocketManager';

/**
 * 节点间通信管理器 - 处理前端节点间的直接通信
 */
export class NodeCommunicationManager extends EventEmitter {
  private static instance: NodeCommunicationManager;
  private nodes: Map<string, BaseNode> = new Map();
  private connections: Map<string, string[]> = new Map(); // nodeId -> [connectedNodeIds]
  private messageBuffer: Map<string, any[]> = new Map(); // nodeId -> [bufferedMessages]
  private webSocketManager: WebSocketManager;
  private isEnabled: boolean = false;

  private constructor() {
    super();
    this.webSocketManager = WebSocketManager.getInstance();
    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): NodeCommunicationManager {
    if (!NodeCommunicationManager.instance) {
      NodeCommunicationManager.instance = new NodeCommunicationManager();
    }
    return NodeCommunicationManager.instance;
  }

  /**
   * 启用节点间通信
   */
  enable(): void {
    if (this.isEnabled) {
      return;
    }

    this.isEnabled = true;
    console.log('🔗 Node communication manager enabled');
    this.emit('communicationEnabled');
  }

  /**
   * 禁用节点间通信
   */
  disable(): void {
    if (!this.isEnabled) {
      return;
    }

    this.isEnabled = false;
    this.clearAllBuffers();
    console.log('🔌 Node communication manager disabled');
    this.emit('communicationDisabled');
  }

  /**
   * 注册节点
   */
  registerNode(node: BaseNode): void {
    this.nodes.set(node.id, node);
    this.connections.set(node.id, []);
    this.messageBuffer.set(node.id, []);

    // 监听节点的输出数据变化
    node.on('outputDataChanged', ({ pinId, data }) => {
      this.handleNodeOutput(node.id, pinId, data);
    });

    // 监听节点完成事件
    node.on('completed', (outputData) => {
      this.handleNodeCompleted(node.id, outputData);
    });

    console.log(`🔗 Registered node ${node.id} for communication`);
  }

  /**
   * 注销节点
   */
  unregisterNode(nodeId: string): void {
    this.nodes.delete(nodeId);
    this.connections.delete(nodeId);
    this.messageBuffer.delete(nodeId);

    // 从其他节点的连接中移除
    this.connections.forEach((connectedNodes, id) => {
      const index = connectedNodes.indexOf(nodeId);
      if (index !== -1) {
        connectedNodes.splice(index, 1);
      }
    });

    console.log(`🔌 Unregistered node ${nodeId} from communication`);
  }

  /**
   * 建立节点间连接
   */
  connectNodes(sourceNodeId: string, targetNodeId: string): void {
    const sourceConnections = this.connections.get(sourceNodeId);
    if (sourceConnections && !sourceConnections.includes(targetNodeId)) {
      sourceConnections.push(targetNodeId);
    }

    // 在节点对象中也建立连接
    const sourceNode = this.nodes.get(sourceNodeId);
    const targetNode = this.nodes.get(targetNodeId);
    
    if (sourceNode && targetNode) {
      sourceNode.connectToNode(targetNodeId, targetNode);
    }

    console.log(`🔗 Connected node ${sourceNodeId} to ${targetNodeId}`);
    this.emit('nodesConnected', { sourceNodeId, targetNodeId });
  }

  /**
   * 断开节点间连接
   */
  disconnectNodes(sourceNodeId: string, targetNodeId: string): void {
    const sourceConnections = this.connections.get(sourceNodeId);
    if (sourceConnections) {
      const index = sourceConnections.indexOf(targetNodeId);
      if (index !== -1) {
        sourceConnections.splice(index, 1);
      }
    }

    // 在节点对象中也断开连接
    const sourceNode = this.nodes.get(sourceNodeId);
    if (sourceNode) {
      sourceNode.disconnectFromNode(targetNodeId);
    }

    console.log(`🔌 Disconnected node ${sourceNodeId} from ${targetNodeId}`);
    this.emit('nodesDisconnected', { sourceNodeId, targetNodeId });
  }

  /**
   * 发送消息到指定节点
   */
  sendMessage(sourceNodeId: string, targetNodeId: string, data: any): void {
    if (!this.isEnabled) {
      console.warn('⚠️ Node communication is disabled');
      return;
    }

    const targetNode = this.nodes.get(targetNodeId);
    if (!targetNode) {
      console.warn(`⚠️ Target node ${targetNodeId} not found`);
      return;
    }

    // 直接发送到目标节点
    targetNode.receiveFromNode(sourceNodeId, data);

    // 同时通过WebSocket发送（如果启用）
    if (this.webSocketManager.isNodeConnected(targetNodeId)) {
      const message: WSMessage = {
        type: WSMessageType.NODE_MESSAGE,
        nodeId: targetNodeId,
        data: {
          sourceNodeId,
          targetNodeId,
          messageData: data
        },
        timestamp: Date.now(),
        messageId: this.generateMessageId()
      };

      this.webSocketManager.sendToNode(targetNodeId, message);
    }

    console.log(`📨 Sent message from ${sourceNodeId} to ${targetNodeId}:`, data);
    this.emit('messageSent', { sourceNodeId, targetNodeId, data });
  }

  /**
   * 广播消息到所有连接的节点
   */
  broadcastMessage(sourceNodeId: string, data: any): void {
    if (!this.isEnabled) {
      return;
    }

    const connectedNodes = this.connections.get(sourceNodeId) || [];
    
    connectedNodes.forEach(targetNodeId => {
      this.sendMessage(sourceNodeId, targetNodeId, data);
    });

    console.log(`📡 Broadcasted message from ${sourceNodeId} to ${connectedNodes.length} nodes`);
  }

  /**
   * 缓存消息到节点缓冲区
   */
  bufferMessage(nodeId: string, data: any): void {
    const buffer = this.messageBuffer.get(nodeId);
    if (buffer) {
      buffer.push({
        data,
        timestamp: Date.now()
      });

      // 限制缓冲区大小
      if (buffer.length > 100) {
        buffer.shift(); // 移除最旧的消息
      }
    }
  }

  /**
   * 获取节点的缓冲消息
   */
  getBufferedMessages(nodeId: string): any[] {
    return this.messageBuffer.get(nodeId) || [];
  }

  /**
   * 清空节点的缓冲消息
   */
  clearNodeBuffer(nodeId: string): void {
    const buffer = this.messageBuffer.get(nodeId);
    if (buffer) {
      buffer.length = 0;
    }
  }

  /**
   * 清空所有缓冲区
   */
  clearAllBuffers(): void {
    this.messageBuffer.forEach((buffer) => {
      buffer.length = 0;
    });
  }

  /**
   * 处理节点输出
   */
  private handleNodeOutput(nodeId: string, pinId: string, data: any): void {
    if (!this.isEnabled) {
      return;
    }

    // 将输出数据发送到连接的节点
    this.broadcastMessage(nodeId, {
      type: 'output',
      pinId,
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 处理节点完成事件
   */
  private handleNodeCompleted(nodeId: string, outputData: any): void {
    if (!this.isEnabled) {
      return;
    }

    // 将完成事件发送到连接的节点
    this.broadcastMessage(nodeId, {
      type: 'completed',
      outputData,
      timestamp: Date.now()
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听WebSocket管理器的事件
    this.webSocketManager.on('clientDisconnected', ({ nodeId }) => {
      if (nodeId) {
        this.handleNodeDisconnected(nodeId);
      }
    });
  }

  /**
   * 处理节点断开连接
   */
  private handleNodeDisconnected(nodeId: string): void {
    console.log(`🔌 Node ${nodeId} disconnected from communication`);
    
    // 缓存未发送的消息
    const connectedNodes = this.connections.get(nodeId) || [];
    connectedNodes.forEach(targetNodeId => {
      this.bufferMessage(targetNodeId, {
        type: 'node_disconnected',
        sourceNodeId: nodeId,
        timestamp: Date.now()
      });
    });
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `comm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取通信状态
   */
  getStatus(): any {
    return {
      isEnabled: this.isEnabled,
      nodeCount: this.nodes.size,
      connectionCount: Array.from(this.connections.values()).reduce((sum, conns) => sum + conns.length, 0),
      bufferedMessageCount: Array.from(this.messageBuffer.values()).reduce((sum, buffer) => sum + buffer.length, 0)
    };
  }

  /**
   * 获取节点连接信息
   */
  getNodeConnections(nodeId: string): string[] {
    return this.connections.get(nodeId) || [];
  }

  /**
   * 获取所有连接信息
   */
  getAllConnections(): Map<string, string[]> {
    return new Map(this.connections);
  }
}

// 导出单例实例
export const nodeCommunicationManager = NodeCommunicationManager.getInstance();
