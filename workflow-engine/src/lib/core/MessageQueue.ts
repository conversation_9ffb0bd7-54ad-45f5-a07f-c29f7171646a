import { createClient, RedisClientType } from 'redis';

export interface WorkflowEvent {
  id: string;
  type: string;
  workflowId: string;
  timestamp: string;
  data: any;
}

// 使用全局变量存储实例，避免在Next.js开发模式下被重置
declare global {
  var __messageQueueInstance: MessageQueue | undefined;
}

export class MessageQueue {
  private client: RedisClientType | null = null;
  private isConnected = false;
  private subscribers = new Map<string, Set<(event: WorkflowEvent) => void>>();
  private memoryHistory = new Map<string, WorkflowEvent[]>(); // 内存模式的历史事件缓存

  private constructor() {}

  static getInstance(): MessageQueue {
    if (!global.__messageQueueInstance) {
      global.__messageQueueInstance = new MessageQueue();
      console.log('Created new MessageQueue instance');
    }
    return global.__messageQueueInstance;
  }

  async connect(): Promise<void> {
    if (this.isConnected && this.client) {
      return;
    }

    // 检查是否禁用Redis
    if (process.env.DISABLE_REDIS === 'true') {
      console.log('Redis disabled, using memory mode');
      this.client = null;
      this.isConnected = false;
      return;
    }

    try {
      // 创建Redis客户端
      this.client = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          reconnectStrategy: (retries) => {
            // 限制重连次数，避免无限重连
            if (retries > 3) {
              console.log('Redis connection failed after 3 retries, switching to memory mode');
              this.client = null;
              this.isConnected = false;
              return false; // 停止重连
            }
            return Math.min(retries * 50, 500);
          }
        }
      });

      // 连接错误处理
      this.client.on('error', (err) => {
        console.warn('Redis Client Error, falling back to memory mode:', err.code || err.message);
        this.isConnected = false;
        // 清理客户端，避免继续尝试连接
        if (this.client) {
          this.client.disconnect().catch(() => {});
          this.client = null;
        }
      });

      this.client.on('connect', () => {
        console.log('Redis Client Connected');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('Redis Client Disconnected');
        this.isConnected = false;
      });

      // 设置连接超时
      const connectTimeout = setTimeout(() => {
        console.log('Redis connection timeout, switching to memory mode');
        if (this.client) {
          this.client.disconnect().catch(() => {});
          this.client = null;
        }
        this.isConnected = false;
      }, 2000); // 2秒超时

      await this.client.connect();
      clearTimeout(connectTimeout);
      console.log('MessageQueue connected to Redis');
    } catch (error) {
      console.warn('Failed to connect to Redis, using memory mode:', error.code || error.message);
      // 如果Redis连接失败，使用内存模式
      this.client = null;
      this.isConnected = false;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.disconnect();
      this.client = null;
      this.isConnected = false;
    }
  }

  /**
   * 发布工作流事件到消息队列
   */
  async publishEvent(event: WorkflowEvent): Promise<void> {
    try {
      if (this.client && this.isConnected) {
        // 使用Redis发布事件
        const channel = `workflow:${event.workflowId}`;
        await this.client.publish(channel, JSON.stringify(event));
        
        // 同时存储到历史队列（用于新连接获取历史事件）
        const historyKey = `workflow:${event.workflowId}:history`;
        await this.client.lPush(historyKey, JSON.stringify(event));
        
        // 限制历史记录数量（保留最近100条）
        await this.client.lTrim(historyKey, 0, 99);
        
        // 设置过期时间（1小时）
        await this.client.expire(historyKey, 3600);
        
        console.log(`Published event to Redis: ${event.type} for workflow ${event.workflowId}`);
      } else {
        // 降级到内存模式
        this.publishEventInMemory(event);
      }
    } catch (error) {
      console.error('Failed to publish event to Redis:', error);
      // 降级到内存模式
      this.publishEventInMemory(event);
    }
  }

  /**
   * 内存模式发布事件（Redis不可用时的降级方案）
   */
  private publishEventInMemory(event: WorkflowEvent): void {
    // 缓存历史事件
    if (!this.memoryHistory.has(event.workflowId)) {
      this.memoryHistory.set(event.workflowId, []);
      console.log(`Created new memory history for workflow ${event.workflowId}`);
    }
    const history = this.memoryHistory.get(event.workflowId)!;
    history.push(event);

    // 限制历史记录数量（保留最近100条）
    if (history.length > 100) {
      history.shift();
    }

    console.log(`Stored event in memory history: ${event.type} for workflow ${event.workflowId} (total: ${history.length} events)`);

    // 发送给当前订阅者
    const workflowSubscribers = this.subscribers.get(event.workflowId);
    if (workflowSubscribers) {
      workflowSubscribers.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error('Error in event callback:', error);
        }
      });
    }
    console.log(`Published event in memory: ${event.type} for workflow ${event.workflowId}`);
  }

  /**
   * 订阅工作流事件
   */
  async subscribeToWorkflow(
    workflowId: string, 
    callback: (event: WorkflowEvent) => void,
    includeHistory = true
  ): Promise<() => void> {
    try {
      if (this.client && this.isConnected) {
        // 使用Redis订阅
        return await this.subscribeToWorkflowRedis(workflowId, callback, includeHistory);
      } else {
        // 降级到内存模式
        return this.subscribeToWorkflowInMemory(workflowId, callback, includeHistory);
      }
    } catch (error) {
      console.error('Failed to subscribe to workflow events:', error);
      // 降级到内存模式
      return this.subscribeToWorkflowInMemory(workflowId, callback, includeHistory);
    }
  }

  /**
   * Redis模式订阅
   */
  private async subscribeToWorkflowRedis(
    workflowId: string,
    callback: (event: WorkflowEvent) => void,
    includeHistory: boolean
  ): Promise<() => void> {
    if (!this.client) {
      throw new Error('Redis client not available');
    }

    // 创建订阅客户端
    const subscriber = this.client.duplicate();
    await subscriber.connect();

    const channel = `workflow:${workflowId}`;
    
    // 订阅实时事件
    await subscriber.subscribe(channel, (message) => {
      try {
        const event: WorkflowEvent = JSON.parse(message);
        callback(event);
      } catch (error) {
        console.error('Error parsing event message:', error);
      }
    });

    // 如果需要历史事件，先发送历史事件
    if (includeHistory) {
      try {
        const historyKey = `workflow:${workflowId}:history`;
        const historyEvents = await this.client.lRange(historyKey, 0, -1);
        
        // 按时间顺序发送历史事件（Redis lRange返回的是逆序）
        for (let i = historyEvents.length - 1; i >= 0; i--) {
          try {
            const event: WorkflowEvent = JSON.parse(historyEvents[i]);
            callback(event);
          } catch (error) {
            console.error('Error parsing history event:', error);
          }
        }
      } catch (error) {
        console.error('Error fetching history events:', error);
      }
    }

    console.log(`Subscribed to Redis channel: ${channel}`);

    // 返回取消订阅函数
    return async () => {
      try {
        await subscriber.unsubscribe(channel);
        await subscriber.disconnect();
        console.log(`Unsubscribed from Redis channel: ${channel}`);
      } catch (error) {
        console.error('Error unsubscribing:', error);
      }
    };
  }

  /**
   * 内存模式订阅
   */
  private subscribeToWorkflowInMemory(
    workflowId: string,
    callback: (event: WorkflowEvent) => void,
    includeHistory = true
  ): () => void {
    if (!this.subscribers.has(workflowId)) {
      this.subscribers.set(workflowId, new Set());
    }

    const workflowSubscribers = this.subscribers.get(workflowId)!;
    workflowSubscribers.add(callback);

    // 如果需要历史事件，先发送历史事件
    if (includeHistory) {
      console.log(`Checking memory history for workflow ${workflowId}. Available workflows: [${Array.from(this.memoryHistory.keys()).join(', ')}]`);

      if (this.memoryHistory.has(workflowId)) {
        const history = this.memoryHistory.get(workflowId)!;
        console.log(`Sending ${history.length} historical events for workflow ${workflowId}`);

        // 按时间顺序发送历史事件
        history.forEach((event, index) => {
          try {
            console.log(`Sending historical event ${index + 1}/${history.length}: ${event.type}`);
            callback(event);
          } catch (error) {
            console.error('Error sending historical event:', error);
          }
        });
      } else {
        console.log(`No historical events found for workflow ${workflowId}`);
      }
    }

    console.log(`Subscribed to workflow in memory: ${workflowId}`);

    // 返回取消订阅函数
    return () => {
      workflowSubscribers.delete(callback);
      if (workflowSubscribers.size === 0) {
        this.subscribers.delete(workflowId);
      }
      console.log(`Unsubscribed from workflow in memory: ${workflowId}`);
    };
  }

  /**
   * 获取工作流历史事件
   */
  async getWorkflowHistory(workflowId: string, limit = 100): Promise<WorkflowEvent[]> {
    try {
      if (this.client && this.isConnected) {
        const historyKey = `workflow:${workflowId}:history`;
        const historyEvents = await this.client.lRange(historyKey, 0, limit - 1);
        
        return historyEvents.reverse().map(eventStr => {
          try {
            return JSON.parse(eventStr) as WorkflowEvent;
          } catch (error) {
            console.error('Error parsing history event:', error);
            return null;
          }
        }).filter(event => event !== null) as WorkflowEvent[];
      }
    } catch (error) {
      console.error('Error fetching workflow history:', error);
    }
    
    return [];
  }

  /**
   * 清理工作流数据
   */
  async cleanupWorkflow(workflowId: string): Promise<void> {
    try {
      if (this.client && this.isConnected) {
        const historyKey = `workflow:${workflowId}:history`;
        await this.client.del(historyKey);
        console.log(`Cleaned up workflow data: ${workflowId}`);
      }

      // 清理内存订阅和历史
      this.subscribers.delete(workflowId);
      this.memoryHistory.delete(workflowId);
    } catch (error) {
      console.error('Error cleaning up workflow:', error);
    }
  }

  /**
   * 检查连接状态
   */
  isRedisConnected(): boolean {
    return this.isConnected && this.client !== null;
  }
}
