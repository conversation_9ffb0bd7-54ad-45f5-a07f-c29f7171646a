import { BaseNode } from '../nodes/BaseNode';

// 节点类构造函数类型
export type NodeConstructor = new (...args: any[]) => BaseNode;

// 节点注册信息
export interface NodeRegistration {
  type: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  constructor: NodeConstructor;
  version: string;
  author?: string;
  tags?: string[];
}

// 动态加载的节点模块接口
export interface NodeModule {
  default: NodeConstructor;
  nodeInfo?: Partial<NodeRegistration>;
}

// 节点工厂类
export class NodeFactory {
  private static instance: NodeFactory;
  private registeredNodes: Map<string, NodeRegistration> = new Map();
  private loadedModules: Map<string, NodeModule> = new Map();

  private constructor() {
    // 注册内置节点类型
    this.registerBuiltinNodes();
  }

  // 单例模式
  static getInstance(): NodeFactory {
    if (!NodeFactory.instance) {
      NodeFactory.instance = new NodeFactory();
    }
    return NodeFactory.instance;
  }

  // 注册节点类型
  registerNode(registration: NodeRegistration): void {
    if (this.registeredNodes.has(registration.type)) {
      console.warn(`Node type '${registration.type}' is already registered. Overwriting...`);
    }

    this.registeredNodes.set(registration.type, registration);
    console.log(`Registered node type: ${registration.type}`);
  }

  // 创建节点实例
  createNode(type: string, config?: any): BaseNode {
    console.log(`🚀🚀🚀 NODEFACTORY CREATING NODE: ${type} 🚀🚀🚀`);
    console.log(`🚀 Config:`, config);

    // 如果类型是 'custom' 但配置中有 nodeType，使用配置中的 nodeType
    let actualType = type;
    console.log(`🔍 Checking type: ${type}, config.nodeType: ${config?.nodeType}`);
    console.log(`🔍 Type === 'custom': ${type === 'custom'}`);
    console.log(`🔍 config?.nodeType exists: ${!!config?.nodeType}`);

    if (type === 'custom' && config?.nodeType) {
      console.log(`🔧🔧🔧 FIXING NODE TYPE: ${type} -> ${config.nodeType} 🔧🔧🔧`);
      actualType = config.nodeType;
    } else {
      console.log(`🔍 No type fix needed. Using original type: ${type}`);
    }

    const registration = this.registeredNodes.get(actualType);
    if (!registration) {
      console.log(`❌❌❌ UNKNOWN NODE TYPE: ${actualType} (original: ${type}) ❌❌❌`);
      throw new Error(`Unknown node type: ${actualType}`);
    }

    try {
      console.log(`🔧 Creating node instance with constructor:`, registration.constructor.name);
      const node = new registration.constructor(config);

      // 设置基本信息
      if (config) {
        node.updateConfig(config);
      }

      console.log(`✅✅✅ SUCCESSFULLY CREATED NODE: ${type} ✅✅✅`);
      console.log(`✅ Node instance:`, node);
      return node;
    } catch (error) {
      console.log(`❌❌❌ FAILED TO CREATE NODE: ${type} ❌❌❌`, error);
      throw new Error(`Failed to create node of type '${type}': ${error}`);
    }
  }

  // 动态加载节点类
  async loadNodeFromFile(filePath: string): Promise<string[]> {
    // 这个方法在当前架构中不被使用，所有节点都通过ClientNodeRegistry静态注册
    // 为了避免Next.js webpack的动态导入警告，我们不实际执行动态导入
    console.warn(`loadNodeFromFile is deprecated and not supported in the current architecture. Use ClientNodeRegistry instead.`);
    throw new Error(`Dynamic node loading from file is not supported. All nodes should be registered through ClientNodeRegistry.`);
  }

  // 批量加载节点目录
  async loadNodesFromDirectory(directoryPath: string): Promise<string[]> {
    const loadedTypes: string[] = [];
    
    try {
      // 在实际实现中，这里需要根据环境来处理文件系统访问
      // 浏览器环境中可能需要预定义的文件列表或API端点
      console.warn('loadNodesFromDirectory not fully implemented for browser environment');
      
      return loadedTypes;
    } catch (error) {
      console.error(`Failed to load nodes from directory ${directoryPath}:`, error);
      throw error;
    }
  }

  // 卸载节点类型
  unregisterNode(type: string): boolean {
    const removed = this.registeredNodes.delete(type);
    if (removed) {
      console.log(`Unregistered node type: ${type}`);
    }
    return removed;
  }

  // 获取所有注册的节点类型
  getRegisteredNodes(): NodeRegistration[] {
    return Array.from(this.registeredNodes.values()).filter(node =>
      node &&
      typeof node === 'object' &&
      node.type &&
      node.name &&
      node.constructor
    );
  }

  // 按类别获取节点
  getNodesByCategory(category: string): NodeRegistration[] {
    return this.getRegisteredNodes().filter(node => node.category === category);
  }

  // 搜索节点
  searchNodes(query: string): NodeRegistration[] {
    const lowerQuery = query.toLowerCase();
    return this.getRegisteredNodes().filter(node => 
      node.name.toLowerCase().includes(lowerQuery) ||
      node.description.toLowerCase().includes(lowerQuery) ||
      node.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  // 检查节点类型是否存在
  hasNodeType(type: string): boolean {
    return this.registeredNodes.has(type);
  }

  // 获取节点信息
  getNodeInfo(type: string): NodeRegistration | undefined {
    return this.registeredNodes.get(type);
  }

  // 验证节点配置
  validateNodeConfig(type: string, config: any): { valid: boolean; errors: string[] } {
    const registration = this.registeredNodes.get(type);
    if (!registration) {
      return { valid: false, errors: [`Unknown node type: ${type}`] };
    }

    // 这里可以添加更复杂的配置验证逻辑
    // 例如检查必需字段、数据类型等
    return { valid: true, errors: [] };
  }

  // 注册内置节点类型
  private registerBuiltinNodes(): void {
    // 在服务器端，通过ServerNodeRegistry动态注册节点
    // 在客户端，通过ClientNodeRegistry动态注册节点
    console.log('NodeFactory initialized - nodes will be registered dynamically');
  }

  // 加载内置节点 - 现在所有节点都是React节点，在客户端动态加载
  private async loadBuiltinNode(nodeType: string): Promise<void> {
    console.warn(`Builtin node loading not supported: ${nodeType}. All nodes are now React nodes loaded on client side.`);
  }

  // 热重载节点
  async reloadNode(type: string): Promise<void> {
    const registration = this.registeredNodes.get(type);
    if (!registration) {
      throw new Error(`Node type '${type}' not found`);
    }

    // 在当前架构中，所有节点都是通过ClientNodeRegistry静态注册的
    // 热重载功能在这种架构下不适用
    console.warn(`Hot reloading is not supported in the current architecture. Node type '${type}' cannot be reloaded.`);
    console.warn(`All nodes are statically registered through ClientNodeRegistry and require application restart to update.`);
  }

  // 获取统计信息
  getStatistics(): {
    totalNodes: number;
    categoryCounts: Map<string, number>;
    loadedModules: number;
  } {
    const categoryCounts = new Map<string, number>();
    
    this.registeredNodes.forEach(node => {
      const count = categoryCounts.get(node.category) || 0;
      categoryCounts.set(node.category, count + 1);
    });

    return {
      totalNodes: this.registeredNodes.size,
      categoryCounts,
      loadedModules: this.loadedModules.size
    };
  }

  // 清理资源
  destroy(): void {
    this.registeredNodes.clear();
    this.loadedModules.clear();
  }
}
