import { EventEmitter } from 'events';
import { WSMessage, WSMessageType } from '../nodes/BaseNode';

/**
 * WebSocket管理器 - 处理前后端通信
 */
export class WebSocketManager extends EventEmitter {
  private static instance: WebSocketManager;
  private server: any = null; // WebSocket服务器实例
  private clients: Map<string, WebSocket> = new Map(); // 客户端连接
  private nodeConnections: Map<string, string> = new Map(); // 节点ID到客户端ID的映射
  private messageQueue: Map<string, WSMessage[]> = new Map(); // 消息队列
  private port: number = 8080;
  private isServerRunning: boolean = false;

  private constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * 启动WebSocket服务器
   */
  async startServer(port: number = 8080): Promise<void> {
    if (this.isServerRunning) {
      console.warn('⚠️ WebSocket server is already running');
      return;
    }

    try {
      this.port = port;
      
      // 在浏览器环境中，我们不需要启动服务器
      if (typeof window !== 'undefined') {
        console.log('🌐 Running in browser environment, WebSocket server not needed');
        this.isServerRunning = true;
        return;
      }

      // 在Node.js环境中启动WebSocket服务器
      const { WebSocketServer } = await import('ws');
      this.server = new WebSocketServer({ port });

      this.server.on('connection', (ws: WebSocket, request: any) => {
        this.handleNewConnection(ws, request);
      });

      this.server.on('error', (error: Error) => {
        console.error('❌ WebSocket server error:', error);
        this.emit('serverError', error);
      });

      this.isServerRunning = true;
      console.log(`🚀 WebSocket server started on port ${port}`);
      this.emit('serverStarted', { port });

    } catch (error) {
      console.error('❌ Failed to start WebSocket server:', error);
      throw error;
    }
  }

  /**
   * 停止WebSocket服务器
   */
  async stopServer(): Promise<void> {
    if (!this.isServerRunning) {
      return;
    }

    try {
      // 关闭所有客户端连接
      this.clients.forEach((client, clientId) => {
        client.close();
      });
      this.clients.clear();
      this.nodeConnections.clear();

      // 关闭服务器
      if (this.server) {
        this.server.close();
        this.server = null;
      }

      this.isServerRunning = false;
      console.log('🛑 WebSocket server stopped');
      this.emit('serverStopped');

    } catch (error) {
      console.error('❌ Failed to stop WebSocket server:', error);
      throw error;
    }
  }

  /**
   * 处理新的WebSocket连接
   */
  private handleNewConnection(ws: WebSocket, request: any): void {
    const clientId = this.generateClientId();
    this.clients.set(clientId, ws);

    console.log(`🔗 New WebSocket connection: ${clientId}`);

    ws.on('message', (data: string) => {
      this.handleMessage(clientId, data);
    });

    ws.on('close', () => {
      this.handleDisconnection(clientId);
    });

    ws.on('error', (error: Error) => {
      console.error(`❌ WebSocket client error (${clientId}):`, error);
    });

    // 发送连接确认
    this.sendToClient(clientId, {
      type: WSMessageType.BACKEND_TO_FRONTEND,
      nodeId: '',
      data: { action: 'connection_established', clientId },
      timestamp: Date.now(),
      messageId: this.generateMessageId()
    });

    this.emit('clientConnected', { clientId });
  }

  /**
   * 处理客户端断开连接
   */
  private handleDisconnection(clientId: string): void {
    console.log(`🔌 WebSocket client disconnected: ${clientId}`);
    
    this.clients.delete(clientId);
    
    // 清理节点连接映射
    const nodeId = Array.from(this.nodeConnections.entries())
      .find(([_, cId]) => cId === clientId)?.[0];
    
    if (nodeId) {
      this.nodeConnections.delete(nodeId);
    }

    this.emit('clientDisconnected', { clientId, nodeId });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(clientId: string, data: string): void {
    try {
      const message: WSMessage = JSON.parse(data);
      
      console.log(`📨 Received message from ${clientId}:`, message);

      // 如果是节点注册消息
      if (message.type === WSMessageType.FRONTEND_TO_BACKEND && 
          message.data.action === 'register_node') {
        this.registerNode(message.nodeId, clientId);
        return;
      }

      // 转发消息到目标节点或广播
      this.routeMessage(message, clientId);

    } catch (error) {
      console.error('❌ Error parsing WebSocket message:', error);
    }
  }

  /**
   * 注册节点到客户端的映射
   */
  private registerNode(nodeId: string, clientId: string): void {
    this.nodeConnections.set(nodeId, clientId);
    console.log(`🔗 Node ${nodeId} registered to client ${clientId}`);

    // 发送注册确认
    this.sendToClient(clientId, {
      type: WSMessageType.BACKEND_TO_FRONTEND,
      nodeId,
      data: { action: 'node_registered' },
      timestamp: Date.now(),
      messageId: this.generateMessageId()
    });

    // 发送队列中的消息
    this.flushMessageQueue(nodeId);
  }

  /**
   * 路由消息到目标
   */
  private routeMessage(message: WSMessage, sourceClientId: string): void {
    if (message.type === WSMessageType.BROADCAST) {
      // 广播消息到所有客户端
      this.broadcast(message);
    } else if (message.type === WSMessageType.NODE_MESSAGE) {
      // 节点间消息
      this.forwardNodeMessage(message);
    } else {
      // 直接转发到目标节点
      this.sendToNode(message.nodeId, message);
    }

    this.emit('messageRouted', { message, sourceClientId });
  }

  /**
   * 发送消息到指定节点
   */
  sendToNode(nodeId: string, message: WSMessage): void {
    const clientId = this.nodeConnections.get(nodeId);
    
    if (clientId) {
      this.sendToClient(clientId, message);
    } else {
      // 节点未连接，加入消息队列
      this.queueMessage(nodeId, message);
      console.warn(`⚠️ Node ${nodeId} not connected, message queued`);
    }
  }

  /**
   * 发送消息到指定客户端
   */
  private sendToClient(clientId: string, message: WSMessage): void {
    const client = this.clients.get(clientId);
    
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(message));
    } else {
      console.warn(`⚠️ Client ${clientId} not available for message`);
    }
  }

  /**
   * 广播消息到所有客户端
   */
  broadcast(message: WSMessage): void {
    this.clients.forEach((client, clientId) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
    
    console.log(`📡 Broadcasted message to ${this.clients.size} clients`);
  }

  /**
   * 转发节点间消息
   */
  private forwardNodeMessage(message: WSMessage): void {
    const { targetNodeId } = message.data;
    if (targetNodeId) {
      this.sendToNode(targetNodeId, message);
    }
  }

  /**
   * 将消息加入队列
   */
  private queueMessage(nodeId: string, message: WSMessage): void {
    if (!this.messageQueue.has(nodeId)) {
      this.messageQueue.set(nodeId, []);
    }
    this.messageQueue.get(nodeId)!.push(message);
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(nodeId: string): void {
    const messages = this.messageQueue.get(nodeId);
    if (messages && messages.length > 0) {
      console.log(`📤 Flushing ${messages.length} queued messages for node ${nodeId}`);
      
      messages.forEach(message => {
        this.sendToNode(nodeId, message);
      });
      
      this.messageQueue.delete(nodeId);
    }
  }

  /**
   * 生成客户端ID
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取服务器状态
   */
  getServerStatus(): any {
    return {
      isRunning: this.isServerRunning,
      port: this.port,
      clientCount: this.clients.size,
      nodeCount: this.nodeConnections.size,
      queuedMessages: Array.from(this.messageQueue.values()).reduce((sum, msgs) => sum + msgs.length, 0)
    };
  }

  /**
   * 获取连接的节点列表
   */
  getConnectedNodes(): string[] {
    return Array.from(this.nodeConnections.keys());
  }

  /**
   * 检查节点是否已连接
   */
  isNodeConnected(nodeId: string): boolean {
    return this.nodeConnections.has(nodeId);
  }
}

// 导出单例实例
export const webSocketManager = WebSocketManager.getInstance();
