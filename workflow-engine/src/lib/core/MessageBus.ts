import { EventEmitter } from 'events';

// 消息接口定义
export interface Message {
  id: string;
  type: string;
  sourceNodeId: string;
  targetNodeId?: string;
  sourcePinId: string;
  targetPinId?: string;
  data: any;
  timestamp: Date;
  priority?: number; // 消息优先级，数字越小优先级越高
}

// 消息队列项
interface QueueItem {
  message: Message;
  priority: number;
}

// 消息总线类
export class MessageBus extends EventEmitter {
  private messageQueue: QueueItem[] = [];
  private subscribers: Map<string, Set<Function>> = new Map();
  private isProcessing: boolean = false;
  private processingDelay: number = 0; // 处理延迟（毫秒）

  constructor() {
    super();
    this.setMaxListeners(1000); // 增加最大监听器数量
  }

  // 发布消息
  publish(message: Omit<Message, 'id' | 'timestamp'>): string {
    const fullMessage: Message = {
      id: this.generateMessageId(),
      timestamp: new Date(),
      priority: 5, // 默认优先级
      ...message
    };

    // 添加到队列
    this.enqueue(fullMessage);

    // 触发消息发布事件
    this.emit('messagePublished', fullMessage);

    // 开始处理队列
    this.processQueue();

    return fullMessage.id;
  }

  // 订阅消息
  subscribe(eventType: string, callback: Function): () => void {
    if (!this.subscribers.has(eventType)) {
      this.subscribers.set(eventType, new Set());
    }
    
    this.subscribers.get(eventType)!.add(callback);

    // 返回取消订阅函数
    return () => {
      const subscribers = this.subscribers.get(eventType);
      if (subscribers) {
        subscribers.delete(callback);
        if (subscribers.size === 0) {
          this.subscribers.delete(eventType);
        }
      }
    };
  }

  // 订阅节点间数据传递
  subscribeNodeData(sourceNodeId: string, targetNodeId: string, callback: Function): () => void {
    const eventType = `nodeData:${sourceNodeId}:${targetNodeId}`;
    return this.subscribe(eventType, callback);
  }

  // 发布节点间数据
  publishNodeData(sourceNodeId: string, targetNodeId: string, sourcePinId: string, targetPinId: string, data: any): string {
    return this.publish({
      type: 'nodeData',
      sourceNodeId,
      targetNodeId,
      sourcePinId,
      targetPinId,
      data
    });
  }

  // 广播消息（发送给所有订阅者）
  broadcast(type: string, data: any): void {
    this.publish({
      type,
      sourceNodeId: 'system',
      sourcePinId: 'broadcast',
      data
    });
  }

  // 将消息添加到优先级队列
  private enqueue(message: Message): void {
    const priority = message.priority || 5;
    const queueItem: QueueItem = { message, priority };

    // 按优先级插入队列（优先级数字越小越靠前）
    let insertIndex = 0;
    while (insertIndex < this.messageQueue.length && 
           this.messageQueue[insertIndex].priority <= priority) {
      insertIndex++;
    }

    this.messageQueue.splice(insertIndex, 0, queueItem);
  }

  // 处理消息队列
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.messageQueue.length > 0) {
      const queueItem = this.messageQueue.shift()!;
      const message = queueItem.message;

      try {
        await this.deliverMessage(message);
        
        // 处理延迟
        if (this.processingDelay > 0) {
          await new Promise(resolve => setTimeout(resolve, this.processingDelay));
        }
      } catch (error) {
        this.emit('messageError', { message, error });
        console.error('Message delivery error:', error);
      }
    }

    this.isProcessing = false;
  }

  // 投递消息给订阅者
  private async deliverMessage(message: Message): Promise<void> {
    // 通用消息类型订阅者
    const generalSubscribers = this.subscribers.get(message.type) || new Set();
    
    // 特定节点间消息订阅者
    const nodeSpecificKey = `${message.type}:${message.sourceNodeId}:${message.targetNodeId}`;
    const nodeSpecificSubscribers = this.subscribers.get(nodeSpecificKey) || new Set();

    // 合并所有订阅者
    const allSubscribers = new Set([...generalSubscribers, ...nodeSpecificSubscribers]);

    // 并行调用所有订阅者
    const promises = Array.from(allSubscribers).map(async (callback) => {
      try {
        await callback(message);
      } catch (error) {
        console.error('Subscriber callback error:', error);
        this.emit('subscriberError', { message, callback, error });
      }
    });

    await Promise.all(promises);

    // 触发消息已投递事件
    this.emit('messageDelivered', message);
  }

  // 设置处理延迟
  setProcessingDelay(delay: number): void {
    this.processingDelay = Math.max(0, delay);
  }

  // 获取队列状态
  getQueueStatus(): { length: number; isProcessing: boolean } {
    return {
      length: this.messageQueue.length,
      isProcessing: this.isProcessing
    };
  }

  // 清空队列
  clearQueue(): void {
    this.messageQueue = [];
    this.emit('queueCleared');
  }

  // 获取所有订阅者信息
  getSubscribers(): Map<string, number> {
    const result = new Map<string, number>();
    this.subscribers.forEach((subscribers, eventType) => {
      result.set(eventType, subscribers.size);
    });
    return result;
  }

  // 生成消息ID
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 清理资源
  destroy(): void {
    this.clearQueue();
    this.subscribers.clear();
    this.removeAllListeners();
  }
}
