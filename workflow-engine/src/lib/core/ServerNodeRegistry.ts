import { NodeFactory } from './NodeFactory';
import { BaseNode } from '../nodes/BaseNode';

/**
 * 服务器端节点注册表 - 支持动态发现和注册节点
 * 实现真正的节点独立运行架构
 */
export class ServerNodeRegistry {
  private static instance: ServerNodeRegistry;
  private initialized = false;
  private nodeModules: Map<string, any> = new Map();
  private nodeInfos: Map<string, any> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): ServerNodeRegistry {
    if (!ServerNodeRegistry.instance) {
      ServerNodeRegistry.instance = new ServerNodeRegistry();
    }
    return ServerNodeRegistry.instance;
  }

  /**
   * 初始化服务器端节点注册表
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.log('🔍 Initializing server node registry...');
      
      const factory = NodeFactory.getInstance();
      
      // 动态注册节点
      await this.registerDynamicNodes();
      
      // 注册发现的节点
      for (const [nodeType, nodeModule] of this.nodeModules) {
        try {
          const registration = this.createNodeRegistration(nodeType, nodeModule);
          factory.registerNode(registration);
          console.log(`✅ Registered server node: ${nodeType}`);
        } catch (error) {
          console.error(`❌ Failed to register server node ${nodeType}:`, error);
        }
      }

      this.initialized = true;
      console.log(`🎉 Server node registry initialized with ${this.nodeModules.size} nodes`);
      
    } catch (error) {
      console.error('❌ Failed to initialize server node registry:', error);
      throw error;
    }
  }

  /**
   * 动态注册所有节点 - 使用节点注册表
   *
   * 这个方法从节点注册表自动加载所有服务器端节点，
   * 添加新节点时只需要修改 /nodes/server/index.ts 文件
   */
  private async registerDynamicNodes(): Promise<void> {
    try {
      // 导入节点注册表
      const { getAllServerNodeInfos, validateServerNodes } = await import('../nodes/server/index');

      // 获取所有节点信息并注册
      const allNodeInfos = await getAllServerNodeInfos();

      // 验证所有节点
      const validation = await validateServerNodes();
      if (!validation.valid) {
        console.warn('⚠️ 服务器端节点验证发现问题:', validation.errors);
      }

      console.log(`✅ 验证通过的节点: ${validation.validNodes.join(', ')}`);

      for (const nodeConfig of allNodeInfos) {
        try {
          const { nodeInfo, class: NodeClass, name } = nodeConfig;

          // 注册节点信息和类
          this.nodeInfos.set(nodeInfo.type, nodeInfo);
          this.nodeModules.set(nodeInfo.type, { default: NodeClass });

          console.log(`📦 Registered server node: ${nodeInfo.type} (${name})`);

        } catch (error) {
          console.error(`❌ Failed to register ${nodeConfig.name}:`, error);
        }
      }

      console.log(`🎉 Server node registry initialized with ${this.nodeInfos.size} nodes`);

    } catch (error) {
      console.error('❌ Failed to load server node registry:', error);
    }
  }

  // 动态扫描方法已移除，使用静态注册

  // 旧的动态提取方法已移除，使用静态注册

  /**
   * 创建节点注册信息
   */
  private createNodeRegistration(nodeType: string, nodeModule: any): any {
    const nodeInfo = this.nodeInfos.get(nodeType);
    
    return {
      type: nodeType,
      name: nodeInfo.name,
      description: nodeInfo.description,
      category: nodeInfo.category,
      icon: nodeInfo.icon,
      version: nodeInfo.version,
      author: nodeInfo.author,
      tags: nodeInfo.tags,
      constructor: nodeModule.default
    };
  }

  /**
   * 获取节点信息
   */
  getNodeInfo(nodeType: string): any {
    return this.nodeInfos.get(nodeType);
  }

  /**
   * 获取所有节点信息
   */
  getAllNodeInfos(): any[] {
    return Array.from(this.nodeInfos.values());
  }

  /**
   * 检查节点是否已注册
   */
  hasNode(nodeType: string): boolean {
    return this.nodeModules.has(nodeType);
  }

  /**
   * 获取节点模块
   */
  getNodeModule(nodeType: string): any {
    return this.nodeModules.get(nodeType);
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 获取已发现的节点数量
   */
  getDiscoveredNodeCount(): number {
    return this.nodeModules.size;
  }

  /**
   * 按类别获取节点
   */
  getNodesByCategory(category: string): any[] {
    return Array.from(this.nodeInfos.values()).filter(info => info.category === category);
  }

  /**
   * 搜索节点
   */
  searchNodes(query: string): any[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.nodeInfos.values()).filter(info =>
      info.name.toLowerCase().includes(lowerQuery) ||
      info.description.toLowerCase().includes(lowerQuery) ||
      info.tags?.some((tag: string) => tag.toLowerCase().includes(lowerQuery))
    );
  }
}

// 导出单例实例
export const serverNodeRegistry = ServerNodeRegistry.getInstance();
