// 客户端节点注册表 - 仅在客户端使用，支持React组件
'use client';

import { NodeFactory } from './NodeFactory';

// 节点模块接口
interface NodeModule {
  default?: any; // 默认导出的节点类构造函数
  [key: string]: any; // 命名导出，包括节点类和nodeInfo
}

// 动态发现的节点信息存储
let discoveredNodeInfos: any[] = [];

// 客户端节点注册表
export class ClientNodeRegistry {
  private static initialized = false;

  // 初始化客户端节点注册表
  static async initialize(): Promise<void> {
    if (ClientNodeRegistry.initialized) {
      return;
    }

    try {
      console.log('Initializing client node registry...');

      const factory = NodeFactory.getInstance();

      // 使用webpack的require.context自动扫描所有React节点文件
      // 第二个参数设为true表示递归扫描子目录
      // 匹配模式：React开头，Node结尾的.tsx文件
      const nodeContext = require.context('../nodes/', true, /React.*Node\.tsx$/);

      console.log('Discovered node files:', nodeContext.keys());

      // 清空之前发现的节点信息
      discoveredNodeInfos = [];

      // 遍历所有发现的节点文件
      for (const modulePath of nodeContext.keys()) {
        try {
          console.log(`Loading node module: ${modulePath}`);

          // 动态导入节点模块
          const nodeModule: NodeModule = nodeContext(modulePath);

          // 查找节点类构造函数，优先使用默认导出，否则查找命名导出
          let NodeClass = nodeModule.default;

          if (!NodeClass) {
            // 从文件路径提取文件名（去除目录和扩展名）
            const fullPath = modulePath.replace('./', '').replace('.tsx', '');
            const fileName = fullPath.split('/').pop() || fullPath; // 获取最后一部分作为文件名
            NodeClass = nodeModule[fileName];

            if (!NodeClass) {
              console.warn(`Node module ${modulePath} does not export a class named ${fileName} or default export`);
              continue;
            }
          }

          // 获取节点信息，优先使用模块导出的nodeInfo，否则使用默认值
          const nodeInfo = nodeModule.nodeInfo || {};

          // 从文件路径提取文件名和目录信息
          const fullPath = modulePath.replace('./', '').replace('.tsx', '');
          const pathParts = fullPath.split('/');
          const fileName = pathParts.pop() || fullPath; // 文件名
          const subDirectory = pathParts.length > 0 ? pathParts[0] : null; // 子目录名

          // 从文件名推断节点类型（如果没有明确指定）
          const defaultType = fileName.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, '');

          // 如果在子目录中，可以从目录名推断分类
          const defaultCategory = subDirectory ?
            subDirectory.charAt(0).toUpperCase() + subDirectory.slice(1) :
            '自定义';

          const registration = {
            type: nodeInfo.type || defaultType,
            name: nodeInfo.name || fileName.replace(/([A-Z])/g, ' $1').trim(),
            description: nodeInfo.description || `${fileName} 节点`,
            category: nodeInfo.category || defaultCategory,
            icon: nodeInfo.icon || 'settings',
            version: nodeInfo.version || '1.0.0',
            author: nodeInfo.author || 'User',
            tags: nodeInfo.tags || ['custom', 'react', ...(subDirectory ? [subDirectory] : [])],
            constructor: NodeClass
          };

          // 注册节点
          factory.registerNode(registration);

          // 保存节点信息供UI使用
          discoveredNodeInfos.push({
            type: registration.type,
            name: registration.name,
            description: registration.description,
            category: registration.category,
            icon: registration.icon,
            version: registration.version,
            author: registration.author,
            tags: registration.tags
          });

          console.log(`Registered node type: ${registration.type}`);
        } catch (error) {
          console.error(`Failed to load node module ${modulePath}:`, error);
        }
      }

      console.log(`Client node registry initialized successfully with ${discoveredNodeInfos.length} nodes`);
      ClientNodeRegistry.initialized = true;
    } catch (error) {
      console.error('Failed to initialize client node registry:', error);
    }
  }

  // 获取所有React节点信息
  static getReactNodeInfos() {
    return discoveredNodeInfos;
  }

  // 检查是否已初始化
  static isInitialized(): boolean {
    return ClientNodeRegistry.initialized;
  }

  // 获取已发现的节点数量
  static getDiscoveredNodeCount(): number {
    return discoveredNodeInfos.length;
  }

  // 按类别获取节点
  static getNodesByCategory(category: string) {
    return discoveredNodeInfos.filter(node => node.category === category);
  }

  // 搜索节点
  static searchNodes(query: string) {
    const lowerQuery = query.toLowerCase();
    return discoveredNodeInfos.filter(node =>
      node.name.toLowerCase().includes(lowerQuery) ||
      node.description.toLowerCase().includes(lowerQuery) ||
      node.tags?.some((tag: string) => tag.toLowerCase().includes(lowerQuery))
    );
  }
}
