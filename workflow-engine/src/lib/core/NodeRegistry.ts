import { NodeFactory } from './NodeFactory';
import { ServerNodeRegistry } from './ServerNodeRegistry';

// 节点注册表 - 在应用启动时注册所有内置节点
export class NodeRegistry {
  private static initialized = false;

  static async initialize(): Promise<void> {
    if (NodeRegistry.initialized) {
      return;
    }

    const factory = NodeFactory.getInstance();

    try {
      // 在服务器端，使用ServerNodeRegistry注册节点
      if (typeof window === 'undefined') {
        // 服务器端环境
        const serverRegistry = ServerNodeRegistry.getInstance();
        await serverRegistry.initialize();
        console.log('Node registry initialized successfully (server-side)');
      } else {
        // 客户端环境 - 节点通过ClientNodeRegistry注册
        console.log('Node registry initialized successfully (client-side)');
      }

      NodeRegistry.initialized = true;
    } catch (error) {
      console.error('Failed to initialize node registry:', error);
      throw error;
    }
  }

  static isInitialized(): boolean {
    return NodeRegistry.initialized;
  }
}
