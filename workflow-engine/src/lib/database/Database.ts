import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

export interface WorkflowRecord {
  id: number;
  name: string;
  description: string;
  data: string; // JSON格式的工作流数据
  version: string;
  created_at: string;
  updated_at: string;
}

export interface NodeConfigRecord {
  id: number;
  workflow_id: number;
  node_id: string;
  config: string; // JSON格式的节点配置
  created_at: string;
  updated_at: string;
}

export interface ExecutionLogRecord {
  id: number;
  workflow_id: number;
  node_id: string;
  event_type: string;
  data: string; // JSON格式的事件数据
  timestamp: string;
}

export class WorkflowDatabase {
  private db: Database.Database;
  private static instance: WorkflowDatabase;

  private constructor() {
    // 确保数据库目录存在
    const dbDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    const dbPath = path.join(dbDir, 'workflow.db');
    this.db = new Database(dbPath);
    
    // 启用外键约束
    this.db.pragma('foreign_keys = ON');
    
    this.initializeTables();
  }

  static getInstance(): WorkflowDatabase {
    if (!WorkflowDatabase.instance) {
      WorkflowDatabase.instance = new WorkflowDatabase();
    }
    return WorkflowDatabase.instance;
  }

  private initializeTables(): void {
    // 创建工作流表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS workflows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        data TEXT NOT NULL,
        version TEXT DEFAULT '1.0.0',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建节点配置表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS node_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workflow_id INTEGER NOT NULL,
        node_id TEXT NOT NULL,
        config TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE,
        UNIQUE(workflow_id, node_id)
      )
    `);

    // 创建执行日志表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS execution_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workflow_id INTEGER,
        node_id TEXT,
        event_type TEXT NOT NULL,
        data TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE SET NULL
      )
    `);

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_workflows_name ON workflows(name);
      CREATE INDEX IF NOT EXISTS idx_node_configs_workflow_id ON node_configs(workflow_id);
      CREATE INDEX IF NOT EXISTS idx_execution_logs_workflow_id ON execution_logs(workflow_id);
      CREATE INDEX IF NOT EXISTS idx_execution_logs_timestamp ON execution_logs(timestamp);
    `);

    // 创建更新时间触发器
    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_workflows_timestamp 
      AFTER UPDATE ON workflows
      BEGIN
        UPDATE workflows SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);

    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS update_node_configs_timestamp 
      AFTER UPDATE ON node_configs
      BEGIN
        UPDATE node_configs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
      END;
    `);
  }

  // 工作流操作
  createWorkflow(name: string, description: string, data: any): number {
    const stmt = this.db.prepare(`
      INSERT INTO workflows (name, description, data)
      VALUES (?, ?, ?)
    `);
    
    const result = stmt.run(name, description, JSON.stringify(data));
    return result.lastInsertRowid as number;
  }

  getWorkflow(id: number): WorkflowRecord | undefined {
    const stmt = this.db.prepare('SELECT * FROM workflows WHERE id = ?');
    return stmt.get(id) as WorkflowRecord | undefined;
  }

  getAllWorkflows(): WorkflowRecord[] {
    const stmt = this.db.prepare('SELECT * FROM workflows ORDER BY updated_at DESC');
    return stmt.all() as WorkflowRecord[];
  }

  updateWorkflow(id: number, name?: string, description?: string, data?: any): boolean {
    const updates: string[] = [];
    const params: any[] = [];

    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name);
    }
    if (description !== undefined) {
      updates.push('description = ?');
      params.push(description);
    }
    if (data !== undefined) {
      updates.push('data = ?');
      params.push(JSON.stringify(data));
    }

    if (updates.length === 0) {
      return false;
    }

    params.push(id);
    const stmt = this.db.prepare(`
      UPDATE workflows SET ${updates.join(', ')} WHERE id = ?
    `);
    
    const result = stmt.run(...params);
    return result.changes > 0;
  }

  deleteWorkflow(id: number): boolean {
    const stmt = this.db.prepare('DELETE FROM workflows WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // 节点配置操作
  saveNodeConfig(workflowId: number, nodeId: string, config: any): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO node_configs (workflow_id, node_id, config)
      VALUES (?, ?, ?)
    `);
    
    stmt.run(workflowId, nodeId, JSON.stringify(config));
  }

  getNodeConfig(workflowId: number, nodeId: string): any | undefined {
    const stmt = this.db.prepare(`
      SELECT config FROM node_configs 
      WHERE workflow_id = ? AND node_id = ?
    `);
    
    const result = stmt.get(workflowId, nodeId) as { config: string } | undefined;
    return result ? JSON.parse(result.config) : undefined;
  }

  getWorkflowNodeConfigs(workflowId: number): { [nodeId: string]: any } {
    const stmt = this.db.prepare(`
      SELECT node_id, config FROM node_configs WHERE workflow_id = ?
    `);
    
    const results = stmt.all(workflowId) as { node_id: string; config: string }[];
    const configs: { [nodeId: string]: any } = {};
    
    results.forEach(row => {
      configs[row.node_id] = JSON.parse(row.config);
    });
    
    return configs;
  }

  deleteNodeConfig(workflowId: number, nodeId: string): boolean {
    const stmt = this.db.prepare(`
      DELETE FROM node_configs WHERE workflow_id = ? AND node_id = ?
    `);
    
    const result = stmt.run(workflowId, nodeId);
    return result.changes > 0;
  }

  // 执行日志操作
  logExecution(workflowId: number | null, nodeId: string | null, eventType: string, data?: any): number {
    const stmt = this.db.prepare(`
      INSERT INTO execution_logs (workflow_id, node_id, event_type, data)
      VALUES (?, ?, ?, ?)
    `);
    
    const result = stmt.run(
      workflowId,
      nodeId,
      eventType,
      data ? JSON.stringify(data) : null
    );
    
    return result.lastInsertRowid as number;
  }

  getExecutionLogs(workflowId?: number, limit: number = 100): ExecutionLogRecord[] {
    let query = 'SELECT * FROM execution_logs';
    const params: any[] = [];

    if (workflowId !== undefined) {
      query += ' WHERE workflow_id = ?';
      params.push(workflowId);
    }

    query += ' ORDER BY timestamp DESC LIMIT ?';
    params.push(limit);

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as ExecutionLogRecord[];
  }

  clearExecutionLogs(workflowId?: number, olderThan?: Date): number {
    let query = 'DELETE FROM execution_logs';
    const params: any[] = [];

    const conditions: string[] = [];
    
    if (workflowId !== undefined) {
      conditions.push('workflow_id = ?');
      params.push(workflowId);
    }
    
    if (olderThan) {
      conditions.push('timestamp < ?');
      params.push(olderThan.toISOString());
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    const stmt = this.db.prepare(query);
    const result = stmt.run(...params);
    return result.changes;
  }

  // 统计信息
  getStatistics(): {
    totalWorkflows: number;
    totalNodeConfigs: number;
    totalExecutionLogs: number;
    recentExecutions: number;
  } {
    const totalWorkflows = this.db.prepare('SELECT COUNT(*) as count FROM workflows').get() as { count: number };
    const totalNodeConfigs = this.db.prepare('SELECT COUNT(*) as count FROM node_configs').get() as { count: number };
    const totalExecutionLogs = this.db.prepare('SELECT COUNT(*) as count FROM execution_logs').get() as { count: number };
    
    // 最近24小时的执行记录
    const recentExecutions = this.db.prepare(`
      SELECT COUNT(*) as count FROM execution_logs 
      WHERE timestamp > datetime('now', '-1 day')
    `).get() as { count: number };

    return {
      totalWorkflows: totalWorkflows.count,
      totalNodeConfigs: totalNodeConfigs.count,
      totalExecutionLogs: totalExecutionLogs.count,
      recentExecutions: recentExecutions.count
    };
  }

  // 数据库维护
  vacuum(): void {
    this.db.exec('VACUUM');
  }

  backup(backupPath: string): void {
    this.db.backup(backupPath);
  }

  close(): void {
    this.db.close();
  }
}
