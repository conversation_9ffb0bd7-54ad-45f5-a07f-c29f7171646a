/**
 * 节点后端服务
 * 
 * 负责处理前端节点的后端方法调用：
 * 1. 接收前端的后端调用请求
 * 2. 执行节点的服务器端代码
 * 3. 返回执行结果给前端
 * 4. 支持缓存、优先级、超时等高级功能
 */

import { WebSocket } from 'ws';
import { EventEmitter } from 'events';
import { BackendCallRequest, BackendCallResponse } from '../core/FrontendWorkflowEngine';

// 后端节点接口
interface BackendNodeInterface {
  executeComplexMath(inputData: any): Promise<any>;
  processLargeDataset(inputData: any): Promise<any>;
  performDatabaseOperation(inputData: any): Promise<any>;
  runMLInference(inputData: any): Promise<any>;
  // 可以添加更多后端方法
}

// 执行上下文
interface BackendExecutionContext {
  nodeId: string;
  workflowId: string;
  userId?: string;
  sessionId?: string;
  timestamp: number;
}

// 执行统计
interface ExecutionStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageExecutionTime: number;
  cacheHitRate: number;
}

/**
 * 节点后端服务类
 */
export class NodeBackendService extends EventEmitter {
  private connections: Map<string, WebSocket> = new Map(); // workflowId -> WebSocket
  private nodeImplementations: Map<string, BackendNodeInterface> = new Map(); // nodeType -> implementation
  private executionCache: Map<string, { result: any; timestamp: number; ttl: number }> = new Map();
  private executionStats: Map<string, ExecutionStats> = new Map(); // nodeType -> stats
  private executionQueue: Array<{ request: BackendCallRequest; ws: WebSocket; priority: number }> = [];
  private isProcessingQueue = false;

  constructor() {
    super();
    this.initializeNodeImplementations();
    this.startQueueProcessor();
  }

  /**
   * 初始化节点实现
   */
  private initializeNodeImplementations(): void {
    // 注册混合数学节点的后端实现
    this.registerNodeImplementation('react-hybrid-math', new HybridMathBackendImplementation());
    
    // 可以注册更多节点的后端实现
    // this.registerNodeImplementation('react-data-processor', new DataProcessorBackendImplementation());
    // this.registerNodeImplementation('react-ml-inference', new MLInferenceBackendImplementation());
    
    console.log(`🔧 Initialized ${this.nodeImplementations.size} backend node implementations`);
  }

  /**
   * 注册节点后端实现
   */
  registerNodeImplementation(nodeType: string, implementation: BackendNodeInterface): void {
    this.nodeImplementations.set(nodeType, implementation);
    this.executionStats.set(nodeType, {
      totalCalls: 0,
      successfulCalls: 0,
      failedCalls: 0,
      averageExecutionTime: 0,
      cacheHitRate: 0
    });
    console.log(`✅ Registered backend implementation for: ${nodeType}`);
  }

  /**
   * 处理WebSocket连接
   */
  handleConnection(workflowId: string, ws: WebSocket): void {
    this.connections.set(workflowId, ws);
    
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(workflowId, message, ws);
      } catch (error) {
        console.error(`❌ Error parsing message from ${workflowId}:`, error);
      }
    });

    ws.on('close', () => {
      this.connections.delete(workflowId);
      console.log(`🔌 Backend connection closed for workflow: ${workflowId}`);
    });

    ws.on('error', (error) => {
      console.error(`❌ Backend connection error for ${workflowId}:`, error);
      this.connections.delete(workflowId);
    });

    console.log(`🔗 Backend connection established for workflow: ${workflowId}`);
  }

  /**
   * 处理消息
   */
  private async handleMessage(workflowId: string, message: any, ws: WebSocket): Promise<void> {
    switch (message.type) {
      case 'backend_call':
        await this.handleBackendCall(workflowId, message.callId, message.request, ws);
        break;
      default:
        console.warn(`⚠️ Unknown message type: ${message.type}`);
    }
  }

  /**
   * 处理后端调用
   */
  private async handleBackendCall(
    workflowId: string, 
    callId: string, 
    request: BackendCallRequest, 
    ws: WebSocket
  ): Promise<void> {
    try {
      console.log(`🔄 Received backend call: ${request.nodeId}.${request.method}`);
      
      // 获取优先级
      const priority = this.getPriority(request);
      
      // 添加到执行队列
      this.executionQueue.push({ request, ws, priority });
      
      // 按优先级排序
      this.executionQueue.sort((a, b) => b.priority - a.priority);
      
      // 开始处理队列
      this.processQueue();
      
    } catch (error) {
      console.error(`❌ Error handling backend call:`, error);
      this.sendErrorResponse(ws, callId, error);
    }
  }

  /**
   * 获取请求优先级
   */
  private getPriority(request: BackendCallRequest): number {
    // 可以根据节点类型、方法名、用户等级等因素确定优先级
    const basePriority = 50;
    
    // 根据方法名调整优先级
    if (request.method.includes('urgent') || request.method.includes('realtime')) {
      return basePriority + 30;
    }
    
    if (request.method.includes('batch') || request.method.includes('background')) {
      return basePriority - 20;
    }
    
    return basePriority;
  }

  /**
   * 启动队列处理器
   */
  private startQueueProcessor(): void {
    setInterval(() => {
      if (!this.isProcessingQueue && this.executionQueue.length > 0) {
        this.processQueue();
      }
    }, 100); // 每100ms检查一次队列
  }

  /**
   * 处理执行队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.executionQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      // 并发处理多个请求（最多5个）
      const concurrentLimit = 5;
      const batch = this.executionQueue.splice(0, concurrentLimit);
      
      const promises = batch.map(({ request, ws }) => 
        this.executeBackendCall(request, ws)
      );
      
      await Promise.allSettled(promises);
      
    } finally {
      this.isProcessingQueue = false;
      
      // 如果还有待处理的请求，继续处理
      if (this.executionQueue.length > 0) {
        setTimeout(() => this.processQueue(), 10);
      }
    }
  }

  /**
   * 执行后端调用
   */
  private async executeBackendCall(request: BackendCallRequest, ws: WebSocket): Promise<void> {
    const startTime = Date.now();
    const callId = `${request.nodeId}_${startTime}`;
    
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(request);
      const cached = this.getFromCache(cacheKey);
      
      if (cached) {
        const response: BackendCallResponse = {
          success: true,
          result: cached.result,
          executionTime: Date.now() - startTime
        };
        
        this.sendSuccessResponse(ws, callId, response);
        this.updateStats(request.nodeId, true, Date.now() - startTime, true);
        return;
      }

      // 获取节点实现
      const nodeType = this.getNodeTypeFromId(request.nodeId);
      const implementation = this.nodeImplementations.get(nodeType);
      
      if (!implementation) {
        throw new Error(`No backend implementation found for node type: ${nodeType}`);
      }

      // 执行方法
      const result = await this.executeMethod(implementation, request.method, request.inputData);
      
      // 缓存结果
      this.cacheResult(cacheKey, result, 300000); // 5分钟缓存
      
      const executionTime = Date.now() - startTime;
      const response: BackendCallResponse = {
        success: true,
        result,
        executionTime
      };
      
      this.sendSuccessResponse(ws, callId, response);
      this.updateStats(request.nodeId, true, executionTime, false);
      
      console.log(`✅ Backend call executed: ${request.nodeId}.${request.method} (${executionTime}ms)`);
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ Backend call failed: ${request.nodeId}.${request.method}`, error);
      
      this.sendErrorResponse(ws, callId, error);
      this.updateStats(request.nodeId, false, executionTime, false);
    }
  }

  /**
   * 执行方法
   */
  private async executeMethod(implementation: any, methodName: string, inputData: any): Promise<any> {
    if (typeof implementation[methodName] !== 'function') {
      throw new Error(`Method ${methodName} not found in backend implementation`);
    }
    
    return await implementation[methodName](inputData);
  }

  /**
   * 从节点ID获取节点类型
   */
  private getNodeTypeFromId(nodeId: string): string {
    // 这里需要根据实际的节点ID格式来解析
    // 假设节点ID格式为: nodeType_uniqueId
    const parts = nodeId.split('_');
    return parts[0] || 'unknown';
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: BackendCallRequest): string {
    const dataHash = JSON.stringify(request.inputData);
    return `${request.nodeId}_${request.method}_${btoa(dataHash).slice(0, 16)}`;
  }

  /**
   * 获取缓存结果
   */
  private getFromCache(cacheKey: string): any | null {
    const cached = this.executionCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached;
    }
    
    // 清理过期缓存
    if (cached) {
      this.executionCache.delete(cacheKey);
    }
    
    return null;
  }

  /**
   * 缓存结果
   */
  private cacheResult(cacheKey: string, result: any, ttl: number): void {
    this.executionCache.set(cacheKey, {
      result,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 发送成功响应
   */
  private sendSuccessResponse(ws: WebSocket, callId: string, response: BackendCallResponse): void {
    const message = {
      type: 'backend_response',
      callId,
      response
    };
    
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * 发送错误响应
   */
  private sendErrorResponse(ws: WebSocket, callId: string, error: any): void {
    const message = {
      type: 'backend_error',
      callId,
      error: error.message || String(error)
    };
    
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(nodeId: string, success: boolean, executionTime: number, cached: boolean): void {
    const nodeType = this.getNodeTypeFromId(nodeId);
    const stats = this.executionStats.get(nodeType);
    
    if (stats) {
      stats.totalCalls++;
      if (success) {
        stats.successfulCalls++;
      } else {
        stats.failedCalls++;
      }
      
      // 更新平均执行时间
      stats.averageExecutionTime = (stats.averageExecutionTime * (stats.totalCalls - 1) + executionTime) / stats.totalCalls;
      
      // 更新缓存命中率
      if (cached) {
        stats.cacheHitRate = (stats.cacheHitRate * (stats.totalCalls - 1) + 1) / stats.totalCalls;
      } else {
        stats.cacheHitRate = (stats.cacheHitRate * (stats.totalCalls - 1)) / stats.totalCalls;
      }
    }
  }

  /**
   * 获取统计信息
   */
  getStats(): Map<string, ExecutionStats> {
    return new Map(this.executionStats);
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.executionCache.clear();
    console.log(`🧹 Backend cache cleared`);
  }

  /**
   * 清理资源
   */
  dispose(): void {
    // 关闭所有连接
    for (const ws of this.connections.values()) {
      ws.close();
    }
    this.connections.clear();
    
    // 清理缓存
    this.clearCache();
    
    // 清理队列
    this.executionQueue = [];
    
    console.log(`🧹 NodeBackendService disposed`);
  }
}

/**
 * 混合数学节点的后端实现
 */
class HybridMathBackendImplementation implements BackendNodeInterface {
  
  /**
   * 执行复杂数学计算
   */
  async executeComplexMath(inputData: any): Promise<any> {
    const startTime = Date.now();
    
    try {
      const { operation, a, b, matrix_a, matrix_b, precision = 10 } = inputData;
      let result: any;
      
      switch (operation) {
        case 'add':
          result = this.precisionAdd(a, b, precision);
          break;
        case 'multiply':
          result = this.precisionMultiply(a, b, precision);
          break;
        case 'power':
          result = await this.complexPower(a, b);
          break;
        case 'factorial':
          result = await this.largeFactorial(a);
          break;
        case 'prime_check':
          result = await this.advancedPrimeCheck(a);
          break;
        case 'matrix_multiply':
          result = await this.optimizedMatrixMultiply(matrix_a, matrix_b);
          break;
        default:
          throw new Error(`Unsupported operation: ${operation}`);
      }
      
      const executionTime = Date.now() - startTime;
      
      return {
        result,
        executionTime,
        executedOn: 'backend',
        cached: false
      };
      
    } catch (error) {
      console.error(`❌ Error in backend math calculation:`, error);
      throw error;
    }
  }

  /**
   * 精确加法（高精度）
   */
  private precisionAdd(a: number, b: number, precision: number): number {
    // 使用高精度计算库或自定义实现
    return parseFloat((a + b).toFixed(precision));
  }

  /**
   * 精确乘法（高精度）
   */
  private precisionMultiply(a: number, b: number, precision: number): number {
    // 使用高精度计算库或自定义实现
    return parseFloat((a * b).toFixed(precision));
  }

  /**
   * 复杂幂运算
   */
  private async complexPower(base: number, exponent: number): Promise<number> {
    // 对于大数值使用更高效的算法
    if (Math.abs(exponent) > 1000) {
      // 使用快速幂算法
      return this.fastPower(base, exponent);
    }
    return Math.pow(base, exponent);
  }

  /**
   * 快速幂算法
   */
  private fastPower(base: number, exponent: number): number {
    if (exponent === 0) return 1;
    if (exponent < 0) return 1 / this.fastPower(base, -exponent);
    
    let result = 1;
    let currentPower = base;
    let exp = exponent;
    
    while (exp > 0) {
      if (exp % 2 === 1) {
        result *= currentPower;
      }
      currentPower *= currentPower;
      exp = Math.floor(exp / 2);
    }
    
    return result;
  }

  /**
   * 大数阶乘
   */
  private async largeFactorial(n: number): Promise<number> {
    if (n < 0) throw new Error('Factorial of negative number is undefined');
    if (n === 0 || n === 1) return 1;
    
    // 对于大数使用Stirling近似或其他高效算法
    if (n > 170) {
      throw new Error('Factorial too large for JavaScript number type');
    }
    
    let result = 1;
    for (let i = 2; i <= n; i++) {
      result *= i;
    }
    return result;
  }

  /**
   * 高级质数检查
   */
  private async advancedPrimeCheck(n: number): Promise<boolean> {
    if (n < 2) return false;
    if (n === 2) return true;
    if (n % 2 === 0) return false;
    
    // 使用Miller-Rabin质数测试或其他高效算法
    return this.millerRabinTest(n, 10); // 10轮测试
  }

  /**
   * Miller-Rabin质数测试
   */
  private millerRabinTest(n: number, k: number): boolean {
    // 简化的Miller-Rabin实现
    // 实际应用中应该使用更完整的实现
    
    if (n < 4) return n === 2 || n === 3;
    if (n % 2 === 0) return false;
    
    // 写成 n-1 = d * 2^r 的形式
    let d = n - 1;
    let r = 0;
    while (d % 2 === 0) {
      d /= 2;
      r++;
    }
    
    // 进行k轮测试
    for (let i = 0; i < k; i++) {
      const a = 2 + Math.floor(Math.random() * (n - 4));
      let x = this.modPow(a, d, n);
      
      if (x === 1 || x === n - 1) continue;
      
      let composite = true;
      for (let j = 0; j < r - 1; j++) {
        x = (x * x) % n;
        if (x === n - 1) {
          composite = false;
          break;
        }
      }
      
      if (composite) return false;
    }
    
    return true;
  }

  /**
   * 模幂运算
   */
  private modPow(base: number, exponent: number, modulus: number): number {
    let result = 1;
    base = base % modulus;
    
    while (exponent > 0) {
      if (exponent % 2 === 1) {
        result = (result * base) % modulus;
      }
      exponent = Math.floor(exponent / 2);
      base = (base * base) % modulus;
    }
    
    return result;
  }

  /**
   * 优化的矩阵乘法
   */
  private async optimizedMatrixMultiply(a: number[][], b: number[][]): Promise<number[][]> {
    if (!a || !b || a.length === 0 || b.length === 0) {
      throw new Error('Invalid matrices for multiplication');
    }
    
    if (a[0].length !== b.length) {
      throw new Error('Matrix dimensions do not match for multiplication');
    }
    
    const rows = a.length;
    const cols = b[0].length;
    const inner = b.length;
    
    // 对于大矩阵使用分块矩阵乘法或Strassen算法
    if (rows > 100 || cols > 100 || inner > 100) {
      return this.strassenMatrixMultiply(a, b);
    }
    
    // 标准矩阵乘法
    const result: number[][] = Array(rows).fill(null).map(() => Array(cols).fill(0));
    
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        for (let k = 0; k < inner; k++) {
          result[i][j] += a[i][k] * b[k][j];
        }
      }
    }
    
    return result;
  }

  /**
   * Strassen矩阵乘法算法（简化版）
   */
  private strassenMatrixMultiply(a: number[][], b: number[][]): number[][] {
    // 这里应该实现Strassen算法
    // 为了简化，这里使用标准算法
    const rows = a.length;
    const cols = b[0].length;
    const inner = b.length;
    
    const result: number[][] = Array(rows).fill(null).map(() => Array(cols).fill(0));
    
    for (let i = 0; i < rows; i++) {
      for (let j = 0; j < cols; j++) {
        for (let k = 0; k < inner; k++) {
          result[i][j] += a[i][k] * b[k][j];
        }
      }
    }
    
    return result;
  }

  /**
   * 处理大数据集
   */
  async processLargeDataset(inputData: any): Promise<any> {
    // 实现大数据集处理逻辑
    throw new Error('Method not implemented');
  }

  /**
   * 执行数据库操作
   */
  async performDatabaseOperation(inputData: any): Promise<any> {
    // 实现数据库操作逻辑
    throw new Error('Method not implemented');
  }

  /**
   * 运行机器学习推理
   */
  async runMLInference(inputData: any): Promise<any> {
    // 实现ML推理逻辑
    throw new Error('Method not implemented');
  }
}

export default NodeBackendService;
