'use client';

import React, { useState, useEffect, useRef } from 'react';

interface LogEntry {
  id: string;
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  data?: any;
}

export default function Console() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [filter, setFilter] = useState<string>('all');
  const consoleRef = useRef<HTMLDivElement>(null);

  // 初始化日志和设置定时器
  useEffect(() => {
    const initialLogs: LogEntry[] = [
      {
        id: '1',
        timestamp: new Date(),
        level: 'info',
        message: '工作流编辑器已启动',
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 1000),
        level: 'debug',
        message: '加载节点类型: 开始节点, 表单录入节点',
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 2000),
        level: 'info',
        message: '连接数据库成功',
      },
    ];
    setLogs(initialLogs);

    // 模拟实时日志
    const interval = setInterval(() => {
      const messages = [
        { level: 'debug' as const, message: '节点状态检查完成' },
        { level: 'info' as const, message: '工作流保存成功' },
        { level: 'debug' as const, message: '内存使用情况: 45MB' },
        { level: 'info' as const, message: '节点连接验证通过' },
      ];

      const randomMessage = messages[Math.floor(Math.random() * messages.length)];
      addLog(randomMessage.level, randomMessage.message);
    }, 10000); // 每10秒添加一条日志

    return () => clearInterval(interval);
  }, []);

  // 自动滚动到底部
  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [logs]);

  const addLog = (level: LogEntry['level'], message: string, data?: any) => {
    const newLog: LogEntry = {
      id: Date.now().toString(),
      timestamp: new Date(),
      level,
      message,
      data,
    };
    setLogs(prev => [...prev, newLog]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).catch(err => {
      console.error('Failed to copy to clipboard:', err);
    });
  };

  const filteredLogs = logs.filter(log => {
    if (filter === 'all') return true;
    return log.level === filter;
  });

  const getLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error':
        return 'text-red-600';
      case 'warn':
        return 'text-yellow-600';
      case 'debug':
        return 'text-gray-500';
      default:
        return 'text-blue-600';
    }
  };

  const getLevelBadge = (level: LogEntry['level']) => {
    const baseClasses = 'px-2 py-0.5 text-xs rounded font-medium';
    switch (level) {
      case 'error':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'warn':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'debug':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-blue-100 text-blue-800`;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900 text-white">
      {/* 控制台头部 */}
      <div className="h-10 bg-gray-800 border-b border-gray-700 flex items-center px-4 gap-4">
        <span className="text-sm font-medium">控制台</span>

        {/* 过滤器 */}
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="text-xs bg-gray-700 text-white border border-gray-600 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">全部 ({logs.length})</option>
          <option value="info">信息 ({logs.filter(l => l.level === 'info').length})</option>
          <option value="warn">警告 ({logs.filter(l => l.level === 'warn').length})</option>
          <option value="error">错误 ({logs.filter(l => l.level === 'error').length})</option>
          <option value="debug">调试 ({logs.filter(l => l.level === 'debug').length})</option>
        </select>

        {/* 控制按钮 */}
        <div className="flex gap-2">
          <button
            onClick={clearLogs}
            className="text-xs bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded transition-colors"
          >
            清空
          </button>
          <button
            onClick={() => {
              if (consoleRef.current) {
                consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
              }
            }}
            className="text-xs bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded transition-colors"
          >
            滚动到底部
          </button>
        </div>

        {/* 日志计数和状态 */}
        <div className="ml-auto flex items-center gap-3">
          <span className="text-xs text-gray-400">
            显示 {filteredLogs.length} / {logs.length} 条
          </span>
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" title="实时更新中"></div>
        </div>
      </div>

      {/* 日志内容 */}
      <div
        ref={consoleRef}
        className="flex-1 overflow-auto p-2 space-y-1 font-mono text-sm"
      >
        {filteredLogs.length > 0 ? (
          filteredLogs.map((log) => (
            <div
              key={log.id}
              className="flex items-start gap-2 hover:bg-gray-800 px-2 py-1 rounded transition-colors group"
            >
              <span className="text-gray-400 text-xs whitespace-nowrap font-mono">
                {log.timestamp.toLocaleTimeString()}
              </span>
              <span className={getLevelBadge(log.level)}>
                {log.level.toUpperCase()}
              </span>
              <span className="flex-1 break-words">{log.message}</span>
              {log.data && (
                <details className="text-xs text-gray-400 group-hover:text-gray-300">
                  <summary className="cursor-pointer hover:text-white">数据</summary>
                  <pre className="mt-1 p-2 bg-gray-800 rounded overflow-auto max-w-md">
                    {JSON.stringify(log.data, null, 2)}
                  </pre>
                </details>
              )}
              <button
                onClick={() => copyToClipboard(log.message)}
                className="opacity-0 group-hover:opacity-100 text-gray-500 hover:text-gray-300 transition-opacity"
                title="复制日志"
              >
                📋
              </button>
            </div>
          ))
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <div className="text-lg mb-2">📝</div>
              <div className="text-sm">暂无日志</div>
              <div className="text-xs mt-1">
                {filter === 'all' ? '等待系统生成日志...' : `当前过滤器: ${filter}`}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
