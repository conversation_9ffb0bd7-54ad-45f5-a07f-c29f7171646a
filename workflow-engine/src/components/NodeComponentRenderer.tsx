'use client';

import React from 'react';
import { NodeFactory } from '@/lib/core/NodeFactory';

// 动态组件映射 - 通过 ClientNodeRegistry 获取
// 不再使用硬编码的组件映射表

// 节点UI渲染器
interface NodeUIRendererProps {
  nodeType: string;
  nodeId: string;
  config: any;
  status: string;
  onAction: (action: string, data?: any) => void;
}

export const NodeUIRenderer: React.FC<NodeUIRendererProps> = ({
  nodeType,
  nodeId,
  config,
  status,
  onAction
}) => {
  // 动态组件加载已被移除 - 使用默认UI
  // TODO: 实现通过 ClientNodeRegistry 的动态组件加载

  return (
    <div className="p-4 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg text-center">
      <h3 className="font-medium text-gray-800">节点 UI</h3>
      <p className="text-sm text-gray-600">节点类型: {nodeType}</p>
      <p className="text-sm text-gray-600">状态: {status}</p>
      <div className="mt-2">
        <button
          onClick={() => onAction('test')}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          测试操作
        </button>
      </div>
    </div>
  );
};

// 节点图标渲染器
interface NodeIconRendererProps {
  nodeType: string;
  nodeId: string;
  status: string;
  isRunning?: boolean;
  isCompleted?: boolean;
  isError?: boolean;
  isWaiting?: boolean;
}

export const NodeIconRenderer: React.FC<NodeIconRendererProps> = ({
  nodeType,
  nodeId,
  status,
  isRunning = false,
  isCompleted = false,
  isError = false,
  isWaiting = false
}) => {
  // 动态组件加载已被移除 - 使用默认图标
  // TODO: 实现通过 ClientNodeRegistry 的动态组件加载

  // 如果没有硬编码的组件，尝试从NodeFactory获取节点实例并调用其图标方法
  try {
    const { NodeFactory } = require('@/lib/core/NodeFactory');
    const factory = NodeFactory.getInstance();

    if (factory.hasNodeType(nodeType)) {
      // 创建临时节点实例来获取图标组件
      const tempNode = factory.createNode(nodeType, {});
      if (tempNode && typeof tempNode.renderIconComponent === 'function') {
        const iconComponent = tempNode.renderIconComponent();

        // 克隆组件并传入正确的props
        return React.cloneElement(iconComponent, {
          nodeId,
          status,
          isRunning,
          isCompleted,
          isError,
          isWaiting
        });
      }
    }
  } catch (error) {
    console.warn('Failed to get dynamic icon for node type:', nodeType, error);
  }

  // 如果都失败了，显示默认图标
  return (
    <div className="w-8 h-8 bg-gray-400 rounded-lg flex items-center justify-center text-white">
      <span className="text-xs">?</span>
    </div>
  );
};

// 节点属性面板渲染器
interface NodePropertiesRendererProps {
  nodeType: string;
  nodeId: string;
  config: any;
  name: string;
  description: string;
  type: string;
  inputs: any[];
  outputs: any[];
  position: { x: number; y: number };
  onConfigChange: (key: string, value: any) => void;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

export const NodePropertiesRenderer: React.FC<NodePropertiesRendererProps> = ({
  nodeType,
  nodeId,
  config,
  name,
  description,
  type,
  inputs,
  outputs,
  position,
  onConfigChange,
  onNameChange,
  onDescriptionChange
}) => {
  // 动态组件加载已被移除 - 使用默认属性面板
  // TODO: 实现通过 ClientNodeRegistry 的动态组件加载

  // 如果没有硬编码的组件，尝试从NodeFactory获取节点实例并调用其属性面板方法
  try {
    const { NodeFactory } = require('@/lib/core/NodeFactory');
    const factory = NodeFactory.getInstance();

    if (factory.hasNodeType(nodeType)) {
      // 创建临时节点实例来获取属性面板组件，使用传入的配置
      const nodeConfig = config || {};
      const tempNode = factory.createNode(nodeType, nodeConfig);
      if (tempNode && typeof tempNode.getPropertyPanelComponent === 'function') {
        const PropertyComponent = tempNode.getPropertyPanelComponent();

        // 克隆组件并传入正确的props
        return React.cloneElement(PropertyComponent, {
          nodeId,
          config,
          name,
          description,
          onConfigChange,
          onNameChange,
          onDescriptionChange
        });
      }
    }
  } catch (error) {
    console.warn('Failed to get dynamic property panel for node type:', nodeType, error);
  }

  // 如果都失败了，显示默认的基础属性面板
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <h3 className="font-medium text-sm mb-3">基础属性</h3>

      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            节点名称
          </label>
          <input
            type="text"
            value={name}
            onChange={(e) => onNameChange(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            描述
          </label>
          <textarea
            value={description}
            onChange={(e) => onDescriptionChange(e.target.value)}
            rows={3}
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="text-xs text-gray-500">
          <div>节点类型: {type}</div>
          <div>位置: ({Math.round(position.x)}, {Math.round(position.y)})</div>
          <div>输入引脚: {inputs.length}</div>
          <div>输出引脚: {outputs.length}</div>
        </div>
      </div>
    </div>
  );
};

// 弹出式节点交互对话框
interface NodeInteractionDialogProps {
  isOpen: boolean;
  nodeType: string;
  nodeId: string;
  config: any;
  status: string;
  onClose: () => void;
  onAction: (action: string, data?: any) => void;
}

export const NodeInteractionDialog: React.FC<NodeInteractionDialogProps> = ({
  isOpen,
  nodeType,
  nodeId,
  config,
  status,
  onClose,
  onAction
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl max-h-[90vh] overflow-auto">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">节点交互</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-4">
          <NodeUIRenderer
            nodeType={nodeType}
            nodeId={nodeId}
            config={config}
            status={status}
            onAction={(action, data) => {
              onAction(action, data);
              if (action === 'formSubmit' || action === 'formCancel') {
                onClose();
              }
            }}
          />
        </div>
      </div>
    </div>
  );
};
