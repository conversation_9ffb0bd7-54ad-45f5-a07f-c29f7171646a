'use client';

import React, { useState, useEffect } from 'react';
import {
  Play,
  Square,
  FileText,
  Video,
  Lightbulb,
  Database,
  Calculator,
  Settings,
  GitBranch,
  Timer,
  Search,
  Clock,
  FunctionSquare,
  BarChart3,
  Shuffle,
  GitCompare
} from 'lucide-react';
import { NodeIconRenderer } from './NodeComponentRenderer';
import { ClientNodeRegistry } from '@/lib/core/ClientNodeRegistry';

interface NodeType {
  type: string;
  name: string;
  category: string;
  icon: string;
  description: string;
  version: string;
  author?: string;
  tags?: string[];
}

interface NodePaletteProps {
  availableNodes: NodeType[];
}

// 图标映射（作为回退）
const iconMap: { [key: string]: React.ReactNode } = {
  'play': <Play size={16} />,
  'square': <Square size={16} />,
  'file-text': <FileText size={16} />,
  'video': <Video size={16} />,
  'lightbulb': <Lightbulb size={16} />,
  'database': <Database size={16} />,
  'calculator': <Calculator size={16} />,
  'settings': <Settings size={16} />,
  'git-branch': <GitBranch size={16} />,
  'timer': <Timer size={16} />,
  'clock': <Clock size={16} />,
  'function': <FunctionSquare size={16} />,
  'chart': <BarChart3 size={16} />,
  'random': <Shuffle size={16} />,
  'compare': <GitCompare size={16} />,
};

// 节点面板图标渲染器
interface NodePaletteIconProps {
  nodeType: string;
  icon: string;
  size?: number;
}

const NodePaletteIcon: React.FC<NodePaletteIconProps> = ({ nodeType, icon, size = 16 }) => {
  // 如果是React节点，尝试使用动态图标渲染
  if (nodeType.startsWith('react-')) {
    try {
      return (
        <div
          style={{
            transform: `scale(${size / 32})`, // 基于32px进行缩放，因为NodeIconRenderer默认是32px
            transformOrigin: 'center',
            width: `${size}px`,
            height: `${size}px`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <NodeIconRenderer
            nodeType={nodeType}
            nodeId="palette-preview"
            status="idle"
            isRunning={false}
            isCompleted={false}
            isError={false}
            isWaiting={false}
          />
        </div>
      );
    } catch (error) {
      console.warn('Failed to render dynamic icon for palette:', nodeType, error);
    }
  }

  // 回退到静态图标
  return <div>{iconMap[icon] || <Settings size={size} />}</div>;
};

// 颜色映射
const colorMap: { [key: string]: string } = {
  '控制': 'bg-green-500',
  '交互': 'bg-blue-500',
  '显示': 'bg-indigo-500',
  '处理': 'bg-teal-500',
  '扩展': 'bg-pink-500',
  '数学': 'bg-purple-500',
  '自定义': 'bg-orange-500',
};

// 不再使用硬编码的默认节点类型
// 所有节点都通过 ClientNodeRegistry 动态获取

export default function NodePalette({ availableNodes }: NodePaletteProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [clientNodesLoaded, setClientNodesLoaded] = useState(false);
  // 默认所有分类都收缩
  const [collapsedCategories, setCollapsedCategories] = useState<Set<string>>(
    new Set(['控制', '交互', '显示', '处理'])
  );

  // 切换分类折叠状态
  const toggleCategoryCollapse = (category: string) => {
    setCollapsedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(category)) {
        newSet.delete(category);
      } else {
        newSet.add(category);
      }
      return newSet;
    });
  };

  // 初始化客户端节点注册表
  useEffect(() => {
    const initClientNodes = async () => {
      if (!ClientNodeRegistry.isInitialized()) {
        await ClientNodeRegistry.initialize();
        setClientNodesLoaded(true);
      } else {
        setClientNodesLoaded(true);
      }
    };

    initClientNodes();
  }, []);

  // 确保availableNodes是数组
  const safeAvailableNodes = Array.isArray(availableNodes) ? availableNodes : [];

  // 只使用React节点，不使用服务器端的HTML节点
  const allNodes = clientNodesLoaded ? ClientNodeRegistry.getReactNodeInfos() : [];

  // 只使用动态获取的节点数据
  const nodeTypes = allNodes;

  // 过滤节点
  const filteredNodes = nodeTypes.filter(node => {
    // 确保节点对象有必要的属性
    if (!node || typeof node !== 'object') return false;

    const nodeName = node.name || '';
    const nodeDescription = node.description || '';
    const nodeCategory = node.category || '';

    const matchesSearch = nodeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         nodeDescription.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === '全部' || nodeCategory === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // 获取所有类别
  const categories = ['全部', ...Array.from(new Set(nodeTypes.map(node => node.category)))];

  const onDragStart = (event: React.DragEvent, nodeType: NodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType.type);
    event.dataTransfer.effectAllowed = 'move';
  };

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">节点面板</h2>
        <p className="text-sm text-gray-600">拖拽节点到画布创建工作流</p>

        {/* 搜索框 */}
        <div className="mt-3 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="搜索节点..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* 类别选择 */}
        <div className="mt-3">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>
      </div>

      {/* 节点列表 */}
      <div className="flex-1 overflow-auto p-4">
        {selectedCategory === '全部' ? (
          // 显示所有类别
          categories.slice(1).map((category) => {
            const categoryNodes = filteredNodes.filter(node => node.category === category);
            if (categoryNodes.length === 0) return null;

            const isCollapsed = collapsedCategories.has(category);

            return (
              <div key={category} className="mb-6">
                <button
                  onClick={() => toggleCategoryCollapse(category)}
                  className="w-full flex items-center justify-between text-sm font-medium text-gray-700 mb-3 uppercase tracking-wide hover:text-gray-900 transition-colors"
                >
                  <span>{category} ({categoryNodes.length})</span>
                  <span className="text-gray-400">
                    {isCollapsed ? '▶' : '▼'}
                  </span>
                </button>
                {!isCollapsed && (
                  <div className="space-y-2">
                    {categoryNodes.map((nodeType) => (
                    <div
                      key={nodeType.type}
                      className="p-3 bg-white border border-gray-200 rounded-lg cursor-move hover:shadow-md transition-shadow"
                      draggable
                      onDragStart={(event) => onDragStart(event, nodeType)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center" style={{ width: '48px', height: '48px' }}>
                          <NodePaletteIcon nodeType={nodeType.type} icon={nodeType.icon} size={48} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900">
                            {nodeType.name}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {nodeType.description}
                          </div>
                          <div className="text-xs text-gray-400">
                            v{nodeType.version}
                          </div>
                        </div>
                      </div>
                    </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })
        ) : (
          // 显示选中类别的节点
          <div className="space-y-2">
            {filteredNodes.map((nodeType) => (
              <div
                key={nodeType.type}
                className="p-3 bg-white border border-gray-200 rounded-lg cursor-move hover:shadow-md transition-shadow"
                draggable
                onDragStart={(event) => onDragStart(event, nodeType)}
              >
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center" style={{ width: '48px', height: '48px' }}>
                    <NodePaletteIcon nodeType={nodeType.type} icon={nodeType.icon} size={48} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900">
                      {nodeType.name}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {nodeType.description}
                    </div>
                    <div className="text-xs text-gray-400">
                      v{nodeType.version}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 无结果提示 */}
        {filteredNodes.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <div className="text-sm">未找到匹配的节点</div>
            <div className="text-xs mt-1">尝试调整搜索条件</div>
          </div>
        )}
      </div>

      {/* 底部操作 */}
      <div className="p-4 border-t bg-gray-50">
        <button className="w-full py-2 px-4 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
          导入自定义节点
        </button>
        <div className="mt-2 text-xs text-gray-500 text-center">
          总计: {nodeTypes.length} 个节点
        </div>
      </div>
    </div>
  );
}
