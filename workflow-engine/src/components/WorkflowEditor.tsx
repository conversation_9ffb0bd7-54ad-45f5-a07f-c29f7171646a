'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  ReactFlowProvider,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';

import DebugPanel from './DebugPanel';
import Console from './Console';
import NodePalette from './NodePalette';
import CustomNode from './CustomNode';
import { NodeInteractionDialog } from './NodeComponentRenderer';
import { ClientNodeRegistry } from '../lib/core/ClientNodeRegistry';
import { v4 as uuidv4 } from 'uuid';

// 定义节点类型
const nodeTypes = {
  custom: CustomNode,
};

const initialNodes: Node[] = [];
const initialEdges: Edge[] = [];

// 内部编辑器组件
function WorkflowEditorInner() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [availableNodes, setAvailableNodes] = useState<any[]>([]);
  const [currentWorkflow, setCurrentWorkflow] = useState<any>(null);
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string | null>(null);
  const [workflowStatus, setWorkflowStatus] = useState<any>(null);
  const [interactionDialog, setInteractionDialog] = useState<{
    isOpen: boolean;
    nodeType: string;
    nodeId: string;
    config: any;
    status: string;
  }>({
    isOpen: false,
    nodeType: '',
    nodeId: '',
    config: {},
    status: 'idle'
  });
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { project } = useReactFlow();

  // 加载可用节点类型
  useEffect(() => {
    const initializeNodes = async () => {
      // 首先初始化客户端节点注册表
      await ClientNodeRegistry.initialize();
      console.log('ClientNodeRegistry initialized');

      // 然后加载可用节点类型
      fetchAvailableNodes();
    };

    initializeNodes();
  }, []);

  // 监听工作流事件
  useEffect(() => {
    if (!currentWorkflowId || currentWorkflowId.startsWith('temp_')) return;

    let eventSource: EventSource | null = null;
    let reconnectTimeout: NodeJS.Timeout | null = null;
    let isWorkflowCompleted = false;

    const connectEventSource = () => {
      if (isWorkflowCompleted) return;

      console.log('Connecting to event source for workflow:', currentWorkflowId);
      eventSource = new EventSource(`/api/workflows/events?workflowId=${currentWorkflowId}`);

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Event received for workflow', currentWorkflowId, ':', data);
          handleWorkflowEvent(data);

          // 如果工作流完成，标记为完成，不再重连
          if (data.type === 'workflowCompleted') {
            isWorkflowCompleted = true;
            eventSource?.close();
          }
        } catch (error) {
          console.error('Failed to parse workflow event:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Workflow event source error:', error);
        eventSource?.close();

        // 如果工作流未完成，3秒后重连
        if (!isWorkflowCompleted) {
          reconnectTimeout = setTimeout(() => {
            connectEventSource();
          }, 3000);
        }
      };
    };

    connectEventSource();

    return () => {
      isWorkflowCompleted = true;
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      eventSource?.close();
    };
  }, [currentWorkflowId]);

  const handleWorkflowEvent = (event: any) => {
    console.log('Workflow event received:', event);

    try {
      switch (event.type) {
        case 'nodeOutputChanged':
          console.log('Processing nodeOutputChanged event:', {
            nodeId: event.data?.nodeId,
            pinId: event.data?.pinId,
            data: event.data?.data,
            fullEvent: event
          });
          if (event.data?.nodeId && event.data?.pinId !== undefined && event.data?.data !== undefined) {
            console.log('Updating node output:', event.data.nodeId, event.data.pinId, event.data.data);
            updateNodeOutput(event.data.nodeId, event.data.pinId, event.data.data);
          } else {
            console.warn('Missing required fields in nodeOutputChanged event:', {
              hasNodeId: !!event.data?.nodeId,
              hasPinId: event.data?.pinId !== undefined,
              hasData: event.data?.data !== undefined,
              eventData: event.data
            });
          }
          break;
        case 'nodeCompleted':
          if (event.data?.nodeId && event.data?.outputData) {
            updateNodeOutputData(event.data.nodeId, event.data.outputData);
          }
          break;
        case 'nodeStatusChanged':
          if (event.data?.nodeId && event.data?.status) {
            updateNodeStatus(event.data.nodeId, event.data.status);
          }
          break;
        case 'statusChanged':
          // 处理工作流状态变化
          console.log('Workflow status changed:', event.data?.status);
          break;
        case 'workflowCompleted':
          setIsRunning(false);
          console.log('Workflow completed');
          // 工作流完成后，主动获取所有节点的输出数据
          fetchWorkflowResults(event.workflowId);
          break;
        case 'workflowStarted':
          setIsRunning(true);
          console.log('Workflow started');
          break;
        case 'connected':
          console.log('Event source connected');
          break;
        case 'showForm':
          // 处理表单显示事件
          console.log('Processing showForm event:', event);
          const nodeId = event.data?.nodeId;
          const formConfig = event.data?.formConfig;

          if (nodeId) {
            console.log('Showing form for node:', nodeId);
            console.log('Form config:', formConfig);

            // 查找对应的节点以获取节点类型
            const targetNode = nodes.find(node => node.id === nodeId);
            const nodeType = targetNode?.data?.nodeType || 'react-form-input';

            setInteractionDialog({
              isOpen: true,
              nodeType: nodeType,
              nodeId: nodeId,
              config: formConfig || {},
              status: targetNode?.data?.status || 'waiting'
            });
          } else {
            console.warn('showForm event missing nodeId:', event);
          }
          break;
        default:
          console.log('Unknown event type:', event.type);
      }
    } catch (error) {
      console.error('Error handling workflow event:', error, event);
    }
  };

  const updateNodeOutput = (nodeId: string, pinId: string, data: any) => {
    console.log('updateNodeOutput called with:', { nodeId, pinId, data });

    setNodes((nodes) => {
      console.log('Current nodes:', nodes.map(n => ({ id: n.id, type: n.type })));
      console.log('Looking for nodeId:', nodeId);

      const updatedNodes = nodes.map((node) => {
        console.log('Checking node:', node.id, 'matches:', node.id === nodeId);
        if (node.id === nodeId) {
          console.log('Found matching node:', node.id, 'updating output data');
          const outputData = { ...node.data.outputData };
          outputData[pinId] = data;
          const updatedNode = {
            ...node,
            data: {
              ...node.data,
              outputData
            }
          };
          console.log('Updated node output data:', updatedNode.data.outputData);

          // 如果更新的是当前选中的节点，也更新selectedNode状态
          if (selectedNode && selectedNode.id === nodeId) {
            setSelectedNode(updatedNode);
          }

          return updatedNode;
        }
        return node;
      });

      console.log('Nodes after update:', updatedNodes.map(n => ({ id: n.id, outputData: n.data.outputData })));
      return updatedNodes;
    });
  };

  const updateNodeOutputData = (nodeId: string, outputData: any) => {
    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === nodeId) {
          const updatedNode = {
            ...node,
            data: {
              ...node.data,
              outputData
            }
          };

          // 如果更新的是当前选中的节点，也更新selectedNode状态
          if (selectedNode && selectedNode.id === nodeId) {
            setSelectedNode(updatedNode);
          }

          return updatedNode;
        }
        return node;
      })
    );
  };

  const updateNodeStatus = useCallback((nodeId: string, status: string) => {
    console.log(`Updating node ${nodeId} status to:`, status);

    setNodes((nodes) =>
      nodes.map((node) => {
        if (node.id === nodeId) {
          // 避免不必要的状态更新
          if (node.data?.status === status) {
            return node;
          }

          const updatedNode = {
            ...node,
            data: {
              ...node.data,
              status
            }
          };

          // 如果更新的是当前选中的节点，也更新selectedNode状态
          if (selectedNode && selectedNode.id === nodeId) {
            setSelectedNode(updatedNode);
          }

          return updatedNode;
        }
        return node;
      })
    );
  }, [selectedNode]);

  // 获取工作流结果
  const fetchWorkflowResults = async (workflowId: string) => {
    try {
      console.log('Fetching workflow results for:', workflowId);
      const response = await fetch(`/api/workflows/results?workflowId=${workflowId}`);

      if (response.ok) {
        const results = await response.json();
        console.log('Workflow results:', results);

        if (results.success && results.data) {
          // 更新所有节点的输出数据
          results.data.forEach((nodeResult: any) => {
            if (nodeResult.nodeId && nodeResult.outputData) {
              console.log('Updating node output data:', nodeResult.nodeId, nodeResult.outputData);
              updateNodeOutputData(nodeResult.nodeId, nodeResult.outputData);
            }
          });
        }
      } else {
        console.error('Failed to fetch workflow results:', response.status);
      }
    } catch (error) {
      console.error('Error fetching workflow results:', error);
    }
  };

  const fetchAvailableNodes = async () => {
    try {
      // 使用客户端注册的节点而不是服务器端的节点
      const clientNodes = ClientNodeRegistry.getReactNodeInfos();
      setAvailableNodes(clientNodes);
    } catch (error) {
      console.error('Failed to fetch available nodes:', error);
    }
  };

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);

    // 如果工作流正在运行且节点支持交互，打开交互对话框
    if (isRunning && node.data.nodeType && node.data.nodeType.startsWith('react-')) {
      setInteractionDialog({
        isOpen: true,
        nodeType: node.data.nodeType,
        nodeId: node.id,
        config: node.data.config || {},
        status: node.data.status || 'idle'
      });
    }
  }, [isRunning]);

  // 处理拖拽放置
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type || !reactFlowBounds) {
        return;
      }

      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const nodeInfo = availableNodes.find(n => n.type === type);

      // 获取节点的默认配置
      let defaultConfig = {};
      try {
        // 尝试创建临时节点实例来获取默认配置
        const { NodeFactory } = require('@/lib/core/NodeFactory');
        const factory = NodeFactory.getInstance();
        if (factory.hasNodeType(type)) {
          const tempNode = factory.createNode(type, {});
          defaultConfig = tempNode.config || {};
        }
      } catch (error) {
        console.warn('Failed to get default config for node type:', type, error);
      }

      const newNode: Node = {
        id: uuidv4(),
        type: 'custom',
        position,
        data: {
          label: nodeInfo?.name || type,
          nodeType: type,
          icon: nodeInfo?.icon || 'settings',
          status: 'idle',
          description: nodeInfo?.description,
          config: defaultConfig
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [project, availableNodes, setNodes]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const runWorkflow = useCallback(async () => {
    if (nodes.length === 0) {
      alert('请先添加节点到工作流中');
      return;
    }

    setIsRunning(true);
    try {
      console.log('开始运行工作流...');

      // 准备工作流数据
      const workflowData = {
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.type,
          position: node.position,
          data: node.data
        })),
        edges: edges.map(edge => ({
          id: edge.id,
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        }))
      };

      // 调用运行API
      const response = await fetch('/api/workflows/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowData,
          name: `工作流_${new Date().toLocaleString()}`
        })
      });

      const result = await response.json();

      if (result.success) {
        // 立即设置真实的工作流ID以开始事件监听
        setCurrentWorkflowId(result.data.workflowId);
        setCurrentWorkflow(result.data);
        console.log('工作流启动成功:', result.data);
      } else {
        throw new Error(result.error);
      }

    } catch (error) {
      console.error('工作流执行失败:', error);
      alert(`工作流执行失败: ${error}`);
      setIsRunning(false);
    }
  }, [nodes, edges]);

  const stopWorkflow = useCallback(async () => {
    if (!currentWorkflowId) {
      return;
    }

    try {
      const response = await fetch('/api/workflows/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: currentWorkflowId,
          action: 'stop'
        })
      });

      const result = await response.json();

      if (result.success) {
        setIsRunning(false);
        setCurrentWorkflowId(null);
        setWorkflowStatus(null);

        // 重置所有节点状态为空闲
        setNodes((nodes) =>
          nodes.map((node) => ({
            ...node,
            data: {
              ...node.data,
              status: 'idle'
            }
          }))
        );

        // 关闭交互对话框
        setInteractionDialog(prev => ({ ...prev, isOpen: false }));

        console.log('工作流已停止');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('停止工作流失败:', error);
    }
  }, [currentWorkflowId]);

  // 状态轮询
  const startStatusPolling = useCallback((workflowId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/workflows/control?workflowId=${workflowId}`);
        const result = await response.json();

        if (result.success) {
          setWorkflowStatus(result.data);

          // 如果工作流完成或出错，停止轮询
          if (result.data.status === 'completed' || result.data.status === 'error' || result.data.status === 'stopped') {
            clearInterval(pollInterval);
            setIsRunning(false);
          }
        }
      } catch (error) {
        console.error('获取工作流状态失败:', error);
        clearInterval(pollInterval);
      }
    }, 1000); // 每秒轮询一次

    // 5分钟后自动停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 300000);
  }, []);

  // 处理节点交互
  const handleNodeAction = useCallback(async (action: string, data?: any) => {
    if (!currentWorkflowId || !interactionDialog.nodeId) {
      return;
    }

    try {
      const response = await fetch('/api/workflows/control', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: currentWorkflowId,
          action: 'userInteraction',
          data: {
            nodeId: interactionDialog.nodeId,
            interactionData: { action, data }
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('节点交互成功:', { action, data });

        // 某些操作后关闭对话框
        if (action === 'formSubmit' || action === 'formCancel') {
          setInteractionDialog(prev => ({ ...prev, isOpen: false }));
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('节点交互失败:', error);
    }
  }, [currentWorkflowId, interactionDialog.nodeId]);

  // 关闭交互对话框
  const closeInteractionDialog = useCallback(() => {
    setInteractionDialog(prev => ({ ...prev, isOpen: false }));
  }, []);

  // 节点更新函数
  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<Node>) => {
    console.log('Updating node:', nodeId, updates);
    setNodes((nodes) =>
      nodes.map((node) =>
        node.id === nodeId
          ? { ...node, ...updates }
          : node
      )
    );

    // 如果更新的是当前选中的节点，也更新selectedNode状态
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(prev => prev ? { ...prev, ...updates } : null);
    }
  }, [selectedNode]);

  const saveWorkflow = useCallback(async () => {
    try {
      const workflowData = {
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.data.nodeType || 'default',
          name: node.data.label,
          position: node.position,
          data: node.data
        })),
        connections: edges.map(edge => ({
          id: edge.id,
          sourceNodeId: edge.source,
          sourcePinId: edge.sourceHandle || 'output',
          targetNodeId: edge.target,
          targetPinId: edge.targetHandle || 'input'
        }))
      };

      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '新工作流',
          description: '通过编辑器创建的工作流',
          data: workflowData
        })
      });

      const result = await response.json();
      if (result.success) {
        console.log('工作流保存成功:', result.data);
        setCurrentWorkflow(result.data);
      } else {
        console.error('工作流保存失败:', result.error);
      }
    } catch (error) {
      console.error('保存工作流时出错:', error);
    }
  }, [nodes, edges]);

  return (
    <div className="h-screen w-screen flex flex-col overflow-hidden">
      {/* 顶部工具栏 */}
      <div className="h-12 bg-gray-100 border-b flex items-center px-4 gap-4 flex-shrink-0">
        <h1 className="text-lg font-semibold">工作流编辑器</h1>
        <div className="flex gap-2">
          <button
            onClick={saveWorkflow}
            className="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            保存
          </button>
          <button
            onClick={runWorkflow}
            disabled={isRunning}
            className="px-4 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {isRunning ? '运行中...' : '运行'}
          </button>
          <button
            onClick={stopWorkflow}
            disabled={!isRunning}
            className="px-4 py-1 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            停止
          </button>
          <div className="ml-auto flex items-center gap-4 text-sm text-gray-600">
            <div>节点: {nodes.length} | 连接: {edges.length}</div>
            {workflowStatus && (
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  workflowStatus.status === 'running' ? 'bg-green-500 animate-pulse' :
                  workflowStatus.status === 'completed' ? 'bg-blue-500' :
                  workflowStatus.status === 'error' ? 'bg-red-500' :
                  'bg-gray-500'
                }`}></div>
                <span>状态: {workflowStatus.status}</span>
                {workflowStatus.nodeStatistics && (
                  <span className="text-xs">
                    (运行: {workflowStatus.nodeStatistics.running},
                     完成: {workflowStatus.nodeStatistics.completed})
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex min-h-0">
        {/* 左侧节点面板 */}
        <div className="w-64 bg-gray-50 border-r flex-shrink-0">
          <NodePalette availableNodes={availableNodes} />
        </div>

        {/* 中间区域：画布 + 控制台 */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* 工作流画布 */}
          <div className="flex-1 relative" ref={reactFlowWrapper}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              nodeTypes={nodeTypes}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              onDrop={onDrop}
              onDragOver={onDragOver}
              fitView
            >
              <Controls />
              <Background variant={BackgroundVariant.Dots} />
            </ReactFlow>
          </div>

          {/* 画布下方的控制台 */}
          <div className="h-48 border-t flex-shrink-0">
            <Console />
          </div>
        </div>

        {/* 右侧调试面板 */}
        <div className="w-80 bg-gray-50 border-l flex-shrink-0 flex flex-col">
          <DebugPanel
            selectedNode={selectedNode}
            onNodeUpdate={handleNodeUpdate}
            edges={edges}
            nodes={nodes}
          />
        </div>
      </div>

      {/* 节点交互对话框 */}
      <NodeInteractionDialog
        isOpen={interactionDialog.isOpen}
        nodeType={interactionDialog.nodeType}
        nodeId={interactionDialog.nodeId}
        config={interactionDialog.config}
        status={interactionDialog.status}
        onClose={closeInteractionDialog}
        onAction={handleNodeAction}
      />
    </div>
  );
}

// 主组件包装器
export default function WorkflowEditor() {
  return (
    <ReactFlowProvider>
      <WorkflowEditorInner />
    </ReactFlowProvider>
  );
}
