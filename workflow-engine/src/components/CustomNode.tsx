'use client';

import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Play,
  Square,
  FileText,
  Video,
  Lightbulb,
  Database,
  Calculator,
  Settings,
  GitBranch,
  Timer,
  Clock,
  FunctionSquare,
  BarChart3,
  Shuffle,
  GitCompare
} from 'lucide-react';
import { NodeIconRenderer } from './NodeComponentRenderer';

// 图标映射
const iconMap: { [key: string]: React.ReactNode } = {
  'play': <Play size={16} />,
  'square': <Square size={16} />,
  'file-text': <FileText size={16} />,
  'video': <Video size={16} />,
  'lightbulb': <Lightbulb size={16} />,
  'database': <Database size={16} />,
  'calculator': <Calculator size={16} />,
  'settings': <Settings size={16} />,
  'git-branch': <GitBranch size={16} />,
  'timer': <Timer size={16} />,
  'clock': <Clock size={16} />,
  'function': <FunctionSquare size={16} />,
  'chart': <BarChart3 size={16} />,
  'random': <Shuffle size={16} />,
  'compare': <GitCompare size={16} />,
};

// 颜色映射
const colorMap: { [key: string]: string } = {
  'start': 'bg-green-500',
  'form-input': 'bg-blue-500',
  'video-display': 'bg-indigo-500',
  'led-simulator': 'bg-orange-500',
  'data-processor': 'bg-teal-500',
  'database': 'bg-gray-500',
  'custom': 'bg-pink-500',
};

interface CustomNodeData {
  label: string;
  nodeType: string;
  icon?: string;
  status?: 'idle' | 'running' | 'completed' | 'error' | 'waiting';
  description?: string;
}

export default function CustomNode({ data, selected }: NodeProps<CustomNodeData>) {
  const nodeType = data.nodeType || 'custom';
  const icon = data.icon || 'settings';
  const status = data.status || 'idle';

  // 检查是否是React节点类型
  const isReactNode = nodeType.startsWith('react-');

  // 状态颜色
  const statusColors = {
    idle: 'border-gray-300',
    running: 'border-blue-500 animate-pulse',
    completed: 'border-green-500',
    error: 'border-red-500',
    waiting: 'border-yellow-500 animate-pulse'
  };

  // 状态指示器
  const statusIndicators = {
    idle: '⚪',
    running: '🔵',
    completed: '✅',
    error: '❌',
    waiting: '⏳'
  };

  // 获取节点实际定义的引脚信息
  const getNodePinInfo = () => {
    try {
      // 尝试从NodeFactory获取节点实例来获取引脚信息
      const { NodeFactory } = require('@/lib/core/NodeFactory');
      const factory = NodeFactory.getInstance();

      if (factory.hasNodeType(nodeType)) {
        // 创建临时节点实例来获取引脚信息，使用节点的实际配置
        const nodeConfig = data?.config || {};
        const tempNode = factory.createNode(nodeType, nodeConfig);
        if (tempNode && tempNode.inputs && tempNode.outputs) {
          return {
            inputPins: tempNode.inputs.map(pin => ({
              id: pin.name,
              label: pin.name,
              description: pin.description,
              dataType: pin.dataType,
              required: pin.required
            })),
            outputPins: tempNode.outputs.map(pin => ({
              id: pin.name,
              label: pin.name,
              description: pin.description,
              dataType: pin.dataType
            }))
          };
        }
      }
    } catch (error) {
      console.warn('Failed to get dynamic pin info for node type:', nodeType, error);
    }

    // 回退到硬编码的引脚定义
    return getHardcodedPinInfo();
  };

  // 硬编码的引脚定义（作为回退）
  const getHardcodedPinInfo = () => {
    if (nodeType === 'react-start') {
      return {
        inputPins: [],
        outputPins: [
          { id: 'trigger', label: '触发' },
          { id: 'timestamp', label: '时间戳' }
        ]
      };
    } else if (nodeType === 'react-form-input') {
      return {
        inputPins: [{ id: 'trigger', label: '触发' }],
        outputPins: [
          { id: 'data', label: '数据' },
          { id: 'completed', label: '完成' }
        ]
      };
    } else {
      return {
        inputPins: [{ id: 'default', label: '输入' }],
        outputPins: [{ id: 'default', label: '输出' }]
      };
    }
  };

  // 计算引脚布局
  const calculatePinLayout = () => {
    const titleHeight = 40; // 标题栏高度
    const pinSpacing = 30; // 引脚间距（固定）
    const pinSize = 16; // 引脚大小
    const contentPadding = 24; // 内容区域padding (p-3 = 12px * 2)
    const minContentHeight = 60; // 最小内容高度

    // 获取节点实际的引脚信息
    const { inputPins, outputPins } = getNodePinInfo();

    // 计算所需的内容区域高度
    const maxPins = Math.max(inputPins.length, outputPins.length);
    const requiredHeight = Math.max(
      minContentHeight,
      maxPins * pinSpacing + contentPadding
    );

    // 计算引脚位置
    const calculatePinPositions = (pins) => {
      if (pins.length === 0) {
        return [];
      } else if (pins.length === 1) {
        // 单引脚：居中显示
        return [{ ...pins[0], top: titleHeight + requiredHeight / 2 }];
      } else {
        // 多引脚：均匀分布
        return pins.map((pin, index) => ({
          ...pin,
          top: titleHeight + pinSpacing + (index * pinSpacing)
        }));
      }
    };

    return {
      inputPins: calculatePinPositions(inputPins),
      outputPins: calculatePinPositions(outputPins),
      contentHeight: requiredHeight
    };
  };

  const { inputPins, outputPins, contentHeight } = calculatePinLayout();
  const nodeContentHeight = `${contentHeight}px`;

  return (
    <div className={`
      relative bg-white rounded-lg shadow-md border-2 transition-all duration-200
      ${statusColors[status]}
      ${selected ? 'ring-2 ring-blue-400' : ''}
      min-w-[150px] max-w-[200px]
    `}>
      {/* 节点头部 */}
      <div className={`
        flex items-center gap-2 px-3 py-2 rounded-t-lg text-white
        ${colorMap[nodeType] || 'bg-gray-500'}
      `}>
        <div className="flex-shrink-0">
          {isReactNode ? (
            <NodeIconRenderer
              nodeType={nodeType}
              nodeId={data.nodeId || 'unknown'}
              status={status}
              isRunning={status === 'running'}
              isCompleted={status === 'completed'}
              isError={status === 'error'}
              isWaiting={status === 'waiting'}
            />
          ) : (
            iconMap[icon] || <Settings size={12} />
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">
            {data.customTitle || data.label?.replace(/^react-/, '') || nodeType?.replace(/^react-/, '')}
          </div>
        </div>
        <div className="flex-shrink-0 text-xs">
          {statusIndicators[status]}
        </div>
      </div>
      
      {/* 节点内容 */}
      <div className="px-3 pt-2 pb-3" style={{ minHeight: nodeContentHeight }}>

        {/* 状态信息 - 放在节点底部中央，避免与引脚重叠 */}
        <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2">
          <span className={`
            text-xs px-2 py-1 rounded-full whitespace-nowrap
            ${status === 'idle' ? 'bg-gray-100 text-gray-600' : ''}
            ${status === 'running' ? 'bg-blue-100 text-blue-600' : ''}
            ${status === 'completed' ? 'bg-green-100 text-green-600' : ''}
            ${status === 'error' ? 'bg-red-100 text-red-600' : ''}
            ${status === 'waiting' ? 'bg-yellow-100 text-yellow-600' : ''}
          `}>
            {status === 'idle' && '空闲'}
            {status === 'running' && '运行中'}
            {status === 'completed' && '已完成'}
            {status === 'error' && '错误'}
            {status === 'waiting' && '等待中'}
          </span>
        </div>
      </div>

      {/* 动态渲染输入连接点 */}
      {inputPins.map((pin) => (
        <React.Fragment key={`input-${pin.id}`}>
          <Handle
            type="target"
            position={Position.Left}
            id={pin.id === 'default' ? undefined : pin.id}
            className="w-4 h-4 border-3 border-white rounded-full"
            style={{
              background: '#3b82f6',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              left: '-8px',
              top: `${pin.top}px`
            }}
          />
          <div
            className="absolute left-2 text-xs text-gray-600 bg-white px-1 rounded"
            style={{ top: `${pin.top - 4}px` }}
          >
            {pin.label}
          </div>
        </React.Fragment>
      ))}

      {/* 动态渲染输出连接点 */}
      {outputPins.map((pin) => (
        <React.Fragment key={`output-${pin.id}`}>
          <Handle
            type="source"
            position={Position.Right}
            id={pin.id === 'default' ? undefined : pin.id}
            className="w-4 h-4 border-3 border-white rounded-full"
            style={{
              background: pin.id === 'completed' ? '#f59e0b' : '#10b981',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              right: '-8px',
              top: `${pin.top}px`
            }}
          />
          <div
            className="absolute right-2 text-xs text-gray-600 bg-white px-1 rounded"
            style={{ top: `${pin.top - 4}px` }}
          >
            {pin.label}
          </div>
        </React.Fragment>
      ))}
    </div>
  );
}
