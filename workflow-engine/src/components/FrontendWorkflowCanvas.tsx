/**
 * 前端工作流画布组件
 * 
 * 集成前端工作流引擎的完整示例：
 * 1. 使用前端工作流引擎管理执行
 * 2. 支持前后端协同节点
 * 3. 实时显示执行状态和性能指标
 * 4. 提供丰富的调试和监控功能
 */

'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Play, 
  Pause, 
  Square, 
  RotateCcw, 
  Activity, 
  Cpu, 
  Zap, 
  Clock,
  BarChart3,
  Settings,
  AlertCircle,
  CheckCircle,
  Loader
} from 'lucide-react';
import { FrontendWorkflowEngine, FrontendWorkflowStatus } from '../lib/core/FrontendWorkflowEngine';
import { WorkflowDefinition } from '../lib/core/WorkflowEngine';
import { ReactBaseNode } from '../lib/nodes/ReactBaseNode';

// 执行统计接口
interface ExecutionMetrics {
  totalNodes: number;
  completedNodes: number;
  executingNodes: number;
  queuedNodes: number;
  totalExecutionTime: number;
  averageNodeTime: number;
  backendCallCount: number;
  cacheHitRate: number;
}

// 节点执行信息
interface NodeExecutionInfo {
  nodeId: string;
  nodeName: string;
  status: string;
  executionTime?: number;
  executedOn?: 'frontend' | 'backend';
  cached?: boolean;
  error?: string;
}

/**
 * 前端工作流画布组件
 */
export const FrontendWorkflowCanvas: React.FC = () => {
  // 工作流引擎状态
  const [engine, setEngine] = useState<FrontendWorkflowEngine | null>(null);
  const [workflowStatus, setWorkflowStatus] = useState<FrontendWorkflowStatus>(FrontendWorkflowStatus.IDLE);
  const [executionMetrics, setExecutionMetrics] = useState<ExecutionMetrics | null>(null);
  const [nodeExecutions, setNodeExecutions] = useState<NodeExecutionInfo[]>([]);
  
  // UI状态
  const [showMetrics, setShowMetrics] = useState(true);
  const [showNodeDetails, setShowNodeDetails] = useState(true);
  const [autoScroll, setAutoScroll] = useState(true);
  
  // 引用
  const engineRef = useRef<FrontendWorkflowEngine | null>(null);
  const nodeDetailsRef = useRef<HTMLDivElement>(null);

  /**
   * 初始化工作流引擎
   */
  useEffect(() => {
    const initializeEngine = async () => {
      try {
        const workflowId = 'demo_workflow_' + Date.now();
        const newEngine = new FrontendWorkflowEngine(workflowId);
        
        // 设置事件监听器
        setupEngineEventListeners(newEngine);
        
        // 加载示例工作流
        await loadDemoWorkflow(newEngine);
        
        setEngine(newEngine);
        engineRef.current = newEngine;
        
        console.log(`🚀 Frontend workflow engine initialized: ${workflowId}`);
        
      } catch (error) {
        console.error(`❌ Failed to initialize frontend engine:`, error);
      }
    };

    initializeEngine();

    // 清理函数
    return () => {
      if (engineRef.current) {
        engineRef.current.dispose();
        engineRef.current = null;
      }
    };
  }, []);

  /**
   * 设置引擎事件监听器
   */
  const setupEngineEventListeners = useCallback((engine: FrontendWorkflowEngine) => {
    // 工作流状态变化
    engine.on('workflowStarted', () => {
      setWorkflowStatus(FrontendWorkflowStatus.RUNNING);
      setNodeExecutions([]);
    });

    engine.on('workflowCompleted', (data) => {
      setWorkflowStatus(FrontendWorkflowStatus.COMPLETED);
      console.log(`🎉 Workflow completed in ${data.executionTime}ms`);
    });

    engine.on('workflowPaused', () => {
      setWorkflowStatus(FrontendWorkflowStatus.PAUSED);
    });

    engine.on('workflowStopped', () => {
      setWorkflowStatus(FrontendWorkflowStatus.IDLE);
    });

    engine.on('workflowError', (error) => {
      setWorkflowStatus(FrontendWorkflowStatus.ERROR);
      console.error(`❌ Workflow error:`, error);
    });

    // 节点状态变化
    engine.on('nodeStatusChanged', (data) => {
      updateNodeExecution(data.nodeId, { status: data.status });
    });

    engine.on('nodeCompleted', (data) => {
      updateNodeExecution(data.nodeId, { 
        status: 'completed',
        executionTime: Date.now() // 这里应该从实际数据获取
      });
    });

    engine.on('nodeError', (data) => {
      updateNodeExecution(data.nodeId, { 
        status: 'error',
        error: data.error.message || String(data.error)
      });
    });

    // 定期更新执行指标
    const metricsInterval = setInterval(() => {
      if (engine) {
        const metrics = engine.getExecutionMetrics();
        setExecutionMetrics({
          totalNodes: metrics.totalNodes,
          completedNodes: metrics.completedNodes,
          executingNodes: metrics.executingNodes,
          queuedNodes: metrics.queuedNodes,
          totalExecutionTime: Date.now() - metrics.startTime,
          averageNodeTime: calculateAverageNodeTime(metrics.nodeExecutionTimes),
          backendCallCount: metrics.backendCallTimes.size,
          cacheHitRate: 0 // 这里需要从实际数据计算
        });
      }
    }, 1000);

    // 清理定时器
    engine.on('workflowCompleted', () => clearInterval(metricsInterval));
    engine.on('workflowStopped', () => clearInterval(metricsInterval));
    engine.on('workflowError', () => clearInterval(metricsInterval));

  }, []);

  /**
   * 更新节点执行信息
   */
  const updateNodeExecution = useCallback((nodeId: string, updates: Partial<NodeExecutionInfo>) => {
    setNodeExecutions(prev => {
      const existing = prev.find(item => item.nodeId === nodeId);
      if (existing) {
        return prev.map(item => 
          item.nodeId === nodeId 
            ? { ...item, ...updates }
            : item
        );
      } else {
        const newItem: NodeExecutionInfo = {
          nodeId,
          nodeName: `Node ${nodeId.slice(0, 8)}`,
          status: 'idle',
          ...updates
        };
        return [...prev, newItem];
      }
    });

    // 自动滚动到最新项
    if (autoScroll && nodeDetailsRef.current) {
      setTimeout(() => {
        nodeDetailsRef.current?.scrollTo({
          top: nodeDetailsRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }, 100);
    }
  }, [autoScroll]);

  /**
   * 计算平均节点执行时间
   */
  const calculateAverageNodeTime = (nodeExecutionTimes: Map<string, number>): number => {
    if (nodeExecutionTimes.size === 0) return 0;
    const times = Array.from(nodeExecutionTimes.values());
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  };

  /**
   * 加载示例工作流
   */
  const loadDemoWorkflow = async (engine: FrontendWorkflowEngine) => {
    const demoWorkflow: WorkflowDefinition = {
      id: 'demo_workflow',
      name: '前端引擎演示工作流',
      description: '展示前后端协同工作的示例工作流',
      nodes: [
        {
          id: 'start_1',
          type: 'react-start',
          data: { autoStart: true, delay: 1 },
          position: { x: 100, y: 100 }
        },
        {
          id: 'math_1',
          type: 'react-hybrid-math',
          data: { 
            operation: 'add',
            a: 100,
            b: 200,
            precision: 2
          },
          position: { x: 300, y: 100 }
        },
        {
          id: 'math_2',
          type: 'react-hybrid-math',
          data: { 
            operation: 'factorial',
            a: 10,
            forceBackend: true
          },
          position: { x: 500, y: 100 }
        }
      ],
      connections: [
        {
          id: 'conn_1',
          sourceNodeId: 'start_1',
          sourcePinId: 'trigger',
          targetNodeId: 'math_1',
          targetPinId: 'trigger'
        },
        {
          id: 'conn_2',
          sourceNodeId: 'math_1',
          sourcePinId: 'result',
          targetNodeId: 'math_2',
          targetPinId: 'trigger'
        }
      ]
    };

    await engine.loadWorkflow(demoWorkflow);
  };

  /**
   * 启动工作流
   */
  const handleStartWorkflow = async () => {
    if (engine && workflowStatus === FrontendWorkflowStatus.IDLE) {
      try {
        await engine.startWorkflow();
      } catch (error) {
        console.error(`❌ Failed to start workflow:`, error);
      }
    }
  };

  /**
   * 暂停工作流
   */
  const handlePauseWorkflow = () => {
    if (engine && workflowStatus === FrontendWorkflowStatus.RUNNING) {
      engine.pauseWorkflow();
    }
  };

  /**
   * 恢复工作流
   */
  const handleResumeWorkflow = () => {
    if (engine && workflowStatus === FrontendWorkflowStatus.PAUSED) {
      engine.resumeWorkflow();
    }
  };

  /**
   * 停止工作流
   */
  const handleStopWorkflow = () => {
    if (engine) {
      engine.stopWorkflow();
    }
  };

  /**
   * 重置工作流
   */
  const handleResetWorkflow = async () => {
    if (engine) {
      engine.stopWorkflow();
      setNodeExecutions([]);
      setExecutionMetrics(null);
      
      // 重新加载工作流
      await loadDemoWorkflow(engine);
    }
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-blue-600 bg-blue-50';
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'paused':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  /**
   * 获取状态图标
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Loader className="w-3 h-3 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-3 h-3" />;
      case 'error':
        return <AlertCircle className="w-3 h-3" />;
      default:
        return <Clock className="w-3 h-3" />;
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 工具栏 */}
      <div className="bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-lg font-semibold">前端工作流引擎演示</h1>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(workflowStatus)}`}>
              {workflowStatus}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* 控制按钮 */}
            {workflowStatus === FrontendWorkflowStatus.IDLE && (
              <button
                onClick={handleStartWorkflow}
                className="flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              >
                <Play className="w-4 h-4" />
                启动
              </button>
            )}
            
            {workflowStatus === FrontendWorkflowStatus.RUNNING && (
              <button
                onClick={handlePauseWorkflow}
                className="flex items-center gap-2 px-3 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
              >
                <Pause className="w-4 h-4" />
                暂停
              </button>
            )}
            
            {workflowStatus === FrontendWorkflowStatus.PAUSED && (
              <button
                onClick={handleResumeWorkflow}
                className="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              >
                <Play className="w-4 h-4" />
                恢复
              </button>
            )}
            
            {(workflowStatus === FrontendWorkflowStatus.RUNNING || workflowStatus === FrontendWorkflowStatus.PAUSED) && (
              <button
                onClick={handleStopWorkflow}
                className="flex items-center gap-2 px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
              >
                <Square className="w-4 h-4" />
                停止
              </button>
            )}
            
            <button
              onClick={handleResetWorkflow}
              className="flex items-center gap-2 px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              <RotateCcw className="w-4 h-4" />
              重置
            </button>
            
            {/* 视图切换 */}
            <div className="flex items-center gap-2 ml-4">
              <button
                onClick={() => setShowMetrics(!showMetrics)}
                className={`p-2 rounded-md ${showMetrics ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}
              >
                <BarChart3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setShowNodeDetails(!showNodeDetails)}
                className={`p-2 rounded-md ${showNodeDetails ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}
              >
                <Activity className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 画布区域 */}
        <div className="flex-1 p-4">
          <div className="bg-white rounded-lg shadow-sm h-full p-6">
            <div className="text-center text-gray-500 mt-20">
              <Cpu className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">前端工作流引擎</h3>
              <p className="text-sm">
                这里将显示工作流画布。<br />
                当前演示展示了前端引擎的执行能力和监控功能。
              </p>
              {engine && (
                <div className="mt-4 text-xs text-gray-400">
                  引擎ID: {engine.getStatus()}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="w-80 border-l bg-white">
          {/* 执行指标 */}
          {showMetrics && executionMetrics && (
            <div className="p-4 border-b">
              <div className="flex items-center gap-2 mb-3">
                <BarChart3 className="w-4 h-4 text-blue-500" />
                <h3 className="font-medium">执行指标</h3>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">总节点数:</span>
                  <span className="font-medium">{executionMetrics.totalNodes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">已完成:</span>
                  <span className="font-medium text-green-600">{executionMetrics.completedNodes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">执行中:</span>
                  <span className="font-medium text-blue-600">{executionMetrics.executingNodes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">队列中:</span>
                  <span className="font-medium text-yellow-600">{executionMetrics.queuedNodes}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">总执行时间:</span>
                  <span className="font-medium">{Math.round(executionMetrics.totalExecutionTime)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">平均节点时间:</span>
                  <span className="font-medium">{Math.round(executionMetrics.averageNodeTime)}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">后端调用:</span>
                  <span className="font-medium">{executionMetrics.backendCallCount}</span>
                </div>
              </div>
            </div>
          )}

          {/* 节点执行详情 */}
          {showNodeDetails && (
            <div className="flex-1 flex flex-col">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Activity className="w-4 h-4 text-green-500" />
                    <h3 className="font-medium">节点执行</h3>
                  </div>
                  <label className="flex items-center gap-1 text-xs">
                    <input
                      type="checkbox"
                      checked={autoScroll}
                      onChange={(e) => setAutoScroll(e.target.checked)}
                      className="rounded"
                    />
                    自动滚动
                  </label>
                </div>
              </div>
              
              <div 
                ref={nodeDetailsRef}
                className="flex-1 overflow-y-auto p-4 space-y-2"
              >
                {nodeExecutions.length === 0 ? (
                  <div className="text-center text-gray-500 text-sm mt-8">
                    暂无执行记录
                  </div>
                ) : (
                  nodeExecutions.map((execution, index) => (
                    <div
                      key={`${execution.nodeId}_${index}`}
                      className="p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">{execution.nodeName}</span>
                        <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${getStatusColor(execution.status)}`}>
                          {getStatusIcon(execution.status)}
                          {execution.status}
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-600 space-y-1">
                        <div>ID: {execution.nodeId.slice(0, 8)}...</div>
                        {execution.executionTime && (
                          <div>执行时间: {execution.executionTime}ms</div>
                        )}
                        {execution.executedOn && (
                          <div className="flex items-center gap-1">
                            {execution.executedOn === 'frontend' ? (
                              <Zap className="w-3 h-3 text-blue-500" />
                            ) : (
                              <Cpu className="w-3 h-3 text-green-500" />
                            )}
                            {execution.executedOn === 'frontend' ? '前端执行' : '后端执行'}
                            {execution.cached && ' (缓存)'}
                          </div>
                        )}
                        {execution.error && (
                          <div className="text-red-600 text-xs mt-1">
                            错误: {execution.error}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FrontendWorkflowCanvas;
