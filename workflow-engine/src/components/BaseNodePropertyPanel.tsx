/**
 * 基础节点属性面板组件
 * 
 * 这个组件提供所有节点通用的属性编辑功能：
 * - 节点名称编辑
 * - 节点描述编辑
 * - 节点基本信息显示
 * 
 * 具体节点可以继承这个组件或者将其作为子组件使用
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Settings, Info, Edit3 } from 'lucide-react';

/**
 * 基础节点属性面板的 Props 接口
 */
export interface BaseNodePropertyPanelProps {
  nodeId: string;
  nodeType: string;
  name: string;
  description: string;
  status: string;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
  children?: React.ReactNode; // 允许子组件添加自定义配置项
  title?: string; // 自定义面板标题
  showBasicInfo?: boolean; // 是否显示基本信息
}

/**
 * 基础节点属性面板组件
 */
export const BaseNodePropertyPanel: React.FC<BaseNodePropertyPanelProps> = ({
  nodeId,
  nodeType,
  name,
  description,
  status,
  onNameChange,
  onDescriptionChange,
  children,
  title,
  showBasicInfo = true
}) => {
  const [localName, setLocalName] = useState(name);
  const [localDescription, setLocalDescription] = useState(description);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);

  // 同步外部状态变化
  useEffect(() => {
    setLocalName(name);
  }, [name]);

  useEffect(() => {
    setLocalDescription(description);
  }, [description]);

  // 处理名称变化
  const handleNameChange = useCallback((newName: string) => {
    setLocalName(newName);
  }, []);

  // 处理名称提交
  const handleNameSubmit = useCallback(() => {
    if (localName.trim() !== name) {
      onNameChange(localName.trim());
    }
    setIsEditingName(false);
  }, [localName, name, onNameChange]);

  // 处理名称键盘事件
  const handleNameKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNameSubmit();
    } else if (e.key === 'Escape') {
      setLocalName(name);
      setIsEditingName(false);
    }
  }, [handleNameSubmit, name]);

  // 处理描述变化
  const handleDescriptionChange = useCallback((newDescription: string) => {
    setLocalDescription(newDescription);
  }, []);

  // 处理描述提交
  const handleDescriptionSubmit = useCallback(() => {
    if (localDescription.trim() !== description) {
      onDescriptionChange(localDescription.trim());
    }
    setIsEditingDescription(false);
  }, [localDescription, description, onDescriptionChange]);

  // 处理描述键盘事件
  const handleDescriptionKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleDescriptionSubmit();
    } else if (e.key === 'Escape') {
      setLocalDescription(description);
      setIsEditingDescription(false);
    }
  }, [handleDescriptionSubmit, description]);

  // 获取状态颜色
  const getStatusColor = () => {
    switch (status) {
      case 'running':
        return 'text-blue-600 bg-blue-50';
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      case 'waiting':
        return 'text-yellow-600 bg-yellow-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // 获取状态文本
  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return '空闲';
      case 'running':
        return '运行中';
      case 'completed':
        return '已完成';
      case 'error':
        return '错误';
      case 'waiting':
        return '等待中';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-4">
      {/* 面板标题 */}
      <div className="flex items-center gap-2 mb-4">
        <Settings size={16} />
        <h3 className="font-medium">{title || `${name} 配置`}</h3>
        {showBasicInfo && (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        )}
      </div>

      {/* 基本信息 */}
      {showBasicInfo && (
        <div className="p-3 bg-gray-50 rounded-lg space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Info size={14} />
            <span>基本信息</span>
          </div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500">节点ID:</span>
              <span className="ml-1 font-mono">{nodeId.slice(0, 8)}...</span>
            </div>
            <div>
              <span className="text-gray-500">类型:</span>
              <span className="ml-1">{nodeType}</span>
            </div>
          </div>
        </div>
      )}

      {/* 节点名称 */}
      <div>
        <label className="block text-sm font-medium mb-1">节点名称</label>
        <div className="relative">
          {isEditingName ? (
            <input
              type="text"
              value={localName}
              onChange={(e) => handleNameChange(e.target.value)}
              onBlur={handleNameSubmit}
              onKeyDown={handleNameKeyDown}
              className="w-full px-3 py-2 border border-blue-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入节点名称"
              autoFocus
            />
          ) : (
            <div
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white cursor-pointer hover:border-gray-400 flex items-center justify-between group"
              onClick={() => setIsEditingName(true)}
            >
              <span className={localName ? 'text-gray-900' : 'text-gray-400'}>
                {localName || '点击设置节点名称'}
              </span>
              <Edit3 size={14} className="text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          )}
        </div>
        <p className="text-xs text-gray-500 mt-1">点击编辑节点名称，按 Enter 保存，按 Esc 取消</p>
      </div>

      {/* 节点描述 */}
      <div>
        <label className="block text-sm font-medium mb-1">节点描述</label>
        <div className="relative">
          {isEditingDescription ? (
            <textarea
              value={localDescription}
              onChange={(e) => handleDescriptionChange(e.target.value)}
              onBlur={handleDescriptionSubmit}
              onKeyDown={handleDescriptionKeyDown}
              rows={3}
              className="w-full px-3 py-2 border border-blue-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="输入节点描述"
              autoFocus
            />
          ) : (
            <div
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm bg-white cursor-pointer hover:border-gray-400 min-h-[80px] flex items-start justify-between group"
              onClick={() => setIsEditingDescription(true)}
            >
              <span className={localDescription ? 'text-gray-900' : 'text-gray-400'}>
                {localDescription || '点击设置节点描述'}
              </span>
              <Edit3 size={14} className="text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity mt-1" />
            </div>
          )}
        </div>
        <p className="text-xs text-gray-500 mt-1">点击编辑描述，按 Ctrl+Enter 保存，按 Esc 取消</p>
      </div>

      {/* 自定义配置项 */}
      {children && (
        <div className="border-t pt-4">
          {children}
        </div>
      )}
    </div>
  );
};

/**
 * 简化版基础属性面板 - 只包含名称和描述
 */
export interface SimpleBaseNodePropertyPanelProps {
  name: string;
  description: string;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
}

export const SimpleBaseNodePropertyPanel: React.FC<SimpleBaseNodePropertyPanelProps> = ({
  name,
  description,
  onNameChange,
  onDescriptionChange
}) => {
  return (
    <>
      {/* 节点名称 */}
      <div>
        <label className="block text-sm font-medium mb-1">节点名称</label>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="自定义节点名称"
        />
      </div>

      {/* 节点描述 */}
      <div>
        <label className="block text-sm font-medium mb-1">描述</label>
        <textarea
          value={description}
          onChange={(e) => onDescriptionChange(e.target.value)}
          rows={2}
          className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="节点描述"
        />
      </div>
    </>
  );
};

export default BaseNodePropertyPanel;
