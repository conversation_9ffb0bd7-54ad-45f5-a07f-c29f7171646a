'use client';

import React, { useState, useEffect } from 'react';
import { Node } from 'reactflow';
import { ChevronDown, ChevronRight, Copy, Eye, EyeOff } from 'lucide-react';
import { NodePropertiesRenderer } from './NodeComponentRenderer';

interface DebugPanelProps {
  selectedNode: Node | null;
  onNodeUpdate?: (nodeId: string, updates: Partial<Node>) => void;
  edges?: any[]; // 添加edges参数以获取连接信息
  nodes?: any[]; // 添加nodes参数以获取所有节点信息
}

export default function DebugPanel({ selectedNode, onNodeUpdate, edges = [], nodes = [] }: DebugPanelProps) {
  const [activeTab, setActiveTab] = useState<'input' | 'properties' | 'output'>('properties');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [inputData, setInputData] = useState<any>({});
  const [outputData, setOutputData] = useState<any>({});
  const [isDragOver, setIsDragOver] = useState(false);

  // 获取节点的真实输入输出数据
  useEffect(() => {
    if (selectedNode) {
      console.log('DebugPanel: Updating data for node:', selectedNode.id);

      // 获取连接到当前节点的输入连接
      const inputConnections = edges.filter(edge => edge.target === selectedNode.id);
      console.log('DebugPanel: Input connections:', inputConnections);

      // 转换为调试面板需要的格式
      const formattedInputData: any = {};
      const formattedOutputData: any = {};

      // 格式化输入数据 - 从上级节点的输出获取
      inputConnections.forEach(connection => {
        const sourceNode = nodes.find(node => node.id === connection.source);
        if (sourceNode) {
          const sourceOutputData = sourceNode.data?.outputData || {};
          const sourcePinId = connection.sourceHandle || 'default';
          const targetPinId = connection.targetHandle || 'default';

          // 获取源节点的输出数据
          const outputValue = sourceOutputData[sourcePinId] || sourceNode.data?.config || {};

          formattedInputData[targetPinId] = {
            type: typeof outputValue,
            value: outputValue,
            source: `${sourceNode.data?.label || sourceNode.type}(${sourcePinId})`
          };
        }
      });

      // 如果没有输入连接，显示节点自身的配置作为输入数据
      if (Object.keys(formattedInputData).length === 0) {
        const nodeConfig = selectedNode.data?.config || {};
        Object.entries(nodeConfig).forEach(([key, value]) => {
          formattedInputData[key] = {
            type: typeof value,
            value: value,
            source: 'node config'
          };
        });
      }

      // 格式化输出数据 - 从节点的输出数据获取
      const nodeOutputData = selectedNode.data?.outputData || {};
      Object.entries(nodeOutputData).forEach(([key, value]) => {
        formattedOutputData[key] = {
          type: typeof value,
          value: value
        };
      });

      console.log('DebugPanel: Formatted input data:', formattedInputData);
      console.log('DebugPanel: Formatted output data:', formattedOutputData);

      setInputData(formattedInputData);
      setOutputData(formattedOutputData);
    } else {
      setInputData({});
      setOutputData({});
    }
  }, [selectedNode, edges, nodes]);

  // 处理数据项拖拽开始
  const handleDragStart = (event: React.DragEvent, key: string, data: any) => {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'data-binding',
      sourceKey: key,
      sourceData: data,
      sourceNodeId: selectedNode?.id
    }));
    event.dataTransfer.effectAllowed = 'copy';
  };

  // 处理拖拽进入属性面板区域
  const handleDragEnter = (event: React.DragEvent) => {
    event.preventDefault();
    if (activeTab !== 'properties') {
      setActiveTab('properties');
    }
    setIsDragOver(true);
  };

  // 处理拖拽离开
  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  // 处理拖拽悬停
  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'copy';
  };

  // 处理放置
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    try {
      const dragData = JSON.parse(event.dataTransfer.getData('application/json'));
      if (dragData.type === 'data-binding') {
        // 这里可以处理数据绑定逻辑
        console.log('Data binding:', dragData);
        // TODO: 实现数据绑定到属性的逻辑
      }
    } catch (error) {
      console.error('Failed to parse drag data:', error);
    }
  };

  // 切换展开/折叠状态
  const toggleExpanded = (key: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedItems(newExpanded);
  };

  // 复制数据到剪贴板
  const copyToClipboard = (data: any) => {
    navigator.clipboard.writeText(JSON.stringify(data, null, 2));
  };

  // 渲染数据项
  const renderDataItem = (key: string, item: any, isInput: boolean = false) => {
    const isExpanded = expandedItems.has(key);
    const isObject = typeof item.value === 'object' && item.value !== null;

    return (
      <div key={key} className="border border-gray-200 rounded-lg mb-2 overflow-hidden">
        {/* 数据项头部 */}
        <div
          className={`
            p-3 bg-gray-50 cursor-move hover:bg-gray-100 transition-colors
            ${isInput ? 'border-l-4 border-blue-400' : 'border-l-4 border-green-400'}
          `}
          draggable
          onDragStart={(e) => handleDragStart(e, key, item)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isObject && (
                <button
                  onClick={() => toggleExpanded(key)}
                  className="p-1 hover:bg-gray-200 rounded"
                >
                  {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                </button>
              )}
              <div>
                <div className="font-medium text-sm text-gray-900">{key}</div>
                <div className="text-xs text-gray-500">
                  {item.type}
                  {item.source && ` • 来源: ${item.source}`}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={() => copyToClipboard(item.value)}
                className="p-1 hover:bg-gray-200 rounded text-gray-500 hover:text-gray-700"
                title="复制值"
              >
                <Copy size={14} />
              </button>
            </div>
          </div>
        </div>

        {/* 数据项内容 */}
        <div className="p-3 bg-white">
          {isObject && isExpanded ? (
            <pre className="text-xs text-gray-700 whitespace-pre-wrap overflow-auto max-h-32">
              {JSON.stringify(item.value, null, 2)}
            </pre>
          ) : (
            <div className="text-sm text-gray-700 truncate">
              {typeof item.value === 'string'
                ? `"${item.value}"`
                : JSON.stringify(item.value)
              }
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* 标签页 */}
      <div className="flex border-b">
        <button
          onClick={() => setActiveTab('input')}
          className={`flex-1 py-2 px-4 text-sm ${
            activeTab === 'input'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          输入数据
        </button>
        <button
          onClick={() => setActiveTab('properties')}
          className={`flex-1 py-2 px-4 text-sm ${
            activeTab === 'properties'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          属性
        </button>
        <button
          onClick={() => setActiveTab('output')}
          className={`flex-1 py-2 px-4 text-sm ${
            activeTab === 'output'
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          输出数据
        </button>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 p-4 overflow-auto">
        {activeTab === 'input' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-sm">上级节点输出</h3>
              <span className="text-xs text-gray-500">
                {Object.keys(inputData).length} 项数据
              </span>
            </div>
            {Object.keys(inputData).length > 0 ? (
              Object.entries(inputData).map(([key, item]) => renderDataItem(key, item, true))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">暂无输入数据</div>
                <div className="text-xs mt-1">选择一个有输入连接的节点</div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'properties' && (
          <div
            className={`space-y-4 ${isDragOver ? 'bg-blue-50 border-2 border-dashed border-blue-300' : ''}`}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-sm">
                节点属性 {selectedNode ? `- ${selectedNode.data.label}` : ''}
                {isDragOver && <span className="text-blue-600 ml-2">📎 放置数据绑定</span>}
              </h3>
              {selectedNode && (
                <span className={`
                  text-xs px-2 py-1 rounded-full
                  ${selectedNode.data.status === 'idle' ? 'bg-gray-100 text-gray-600' : ''}
                  ${selectedNode.data.status === 'running' ? 'bg-blue-100 text-blue-600' : ''}
                  ${selectedNode.data.status === 'completed' ? 'bg-green-100 text-green-600' : ''}
                  ${selectedNode.data.status === 'error' ? 'bg-red-100 text-red-600' : ''}
                  ${selectedNode.data.status === 'waiting' ? 'bg-yellow-100 text-yellow-600' : ''}
                `}>
                  {selectedNode.data.status || 'idle'}
                </span>
              )}
            </div>
            {selectedNode ? (
              <NodePropertiesRenderer
                nodeType={selectedNode.data.nodeType || 'unknown'}
                nodeId={selectedNode.id}
                config={selectedNode.data.config || {}}
                name={selectedNode.data.customTitle || selectedNode.data.label || ''}
                description={selectedNode.data.description || ''}
                type={selectedNode.data.nodeType || 'unknown'}
                inputs={[]} // TODO: 从节点获取实际的输入引脚
                outputs={[]} // TODO: 从节点获取实际的输出引脚
                position={selectedNode.position}
                onConfigChange={(key, value) => {
                  console.log('Config change:', key, value);
                  if (selectedNode && onNodeUpdate) {
                    const updatedConfig = { ...selectedNode.data.config, [key]: value };
                    onNodeUpdate(selectedNode.id, {
                      data: {
                        ...selectedNode.data,
                        config: updatedConfig
                      }
                    });
                  }
                }}
                onNameChange={(name) => {
                  console.log('Name change:', name);
                  if (selectedNode && onNodeUpdate) {
                    onNodeUpdate(selectedNode.id, {
                      data: {
                        ...selectedNode.data,
                        customTitle: name
                      }
                    });
                  }
                }}
                onDescriptionChange={(description) => {
                  console.log('Description change:', description);
                  if (selectedNode && onNodeUpdate) {
                    onNodeUpdate(selectedNode.id, {
                      data: {
                        ...selectedNode.data,
                        description: description
                      }
                    });
                  }
                }}
              />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">请选择一个节点</div>
                <div className="text-xs mt-1">点击画布中的节点查看属性</div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'output' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-sm">节点输出</h3>
              <span className="text-xs text-gray-500">
                {Object.keys(outputData).length} 项数据
              </span>
            </div>
            {Object.keys(outputData).length > 0 ? (
              Object.entries(outputData).map(([key, item]) => renderDataItem(key, item, false))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm">暂无输出数据</div>
                <div className="text-xs mt-1">节点执行后将显示输出数据</div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
