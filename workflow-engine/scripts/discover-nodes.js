#!/usr/bin/env node

/**
 * 自动发现服务器端节点脚本
 * 
 * 这个脚本会自动扫描 /src/lib/nodes/server/ 目录，
 * 发现所有符合命名约定的节点文件，并自动更新 index.ts 文件。
 * 
 * 使用方式：
 * 1. 在项目根目录运行：node scripts/discover-nodes.js
 * 2. 或者在 package.json 中添加脚本：npm run discover-nodes
 * 
 * 命名约定：
 * - 文件名必须以 'Server' 开头，以 'Node.ts' 结尾
 * - 类名必须与文件名一致
 * - 必须导出对应的类
 */

const fs = require('fs');
const path = require('path');

// 配置
const NODES_BASE_DIR = path.join(__dirname, '../src/lib/nodes');
const SERVER_INDEX_FILE = path.join(NODES_BASE_DIR, 'server/index.ts');

/**
 * 递归扫描分类目录，发现所有服务器端节点文件
 */
function discoverNodeFiles() {
  console.log('🔍 Scanning for server nodes in categorized directories...');

  if (!fs.existsSync(NODES_BASE_DIR)) {
    console.error(`❌ Nodes base directory not found: ${NODES_BASE_DIR}`);
    return [];
  }

  const nodeFiles = [];

  // 扫描所有分类目录
  function scanDirectory(dirPath, category = '') {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // 跳过 server 目录（这是旧的结构）
        if (item === 'server') {
          continue;
        }

        // 递归扫描子目录
        const newCategory = category ? `${category}/${item}` : item;
        scanDirectory(itemPath, newCategory);
      } else if (stat.isFile()) {
        // 检查文件是否符合服务器端节点命名约定
        if (item.startsWith('Server') && item.endsWith('Node.ts')) {
          const className = item.replace('.ts', '');
          const relativePath = path.relative(NODES_BASE_DIR, dirPath);

          nodeFiles.push({
            fileName: item,
            className: className,
            filePath: itemPath,
            category: category.split('/')[0] || 'unknown',
            relativePath: relativePath,
            fullPath: relativePath
          });

          console.log(`✅ Found server node: ${className} in ${relativePath}`);
        }
      }
    }
  }

  // 开始扫描
  scanDirectory(NODES_BASE_DIR);

  console.log(`🎉 Discovery completed: ${nodeFiles.length} server nodes found`);
  return nodeFiles;
}

/**
 * 验证节点文件的完整性
 */
function validateNodeFile(nodeFile) {
  try {
    const content = fs.readFileSync(nodeFile.filePath, 'utf8');
    
    // 检查是否有类定义
    const classRegex = new RegExp(`export\\s+class\\s+${nodeFile.className}`, 'g');
    if (!classRegex.test(content)) {
      console.warn(`⚠️ ${nodeFile.className}: No exported class found`);
      return false;
    }
    
    // 检查是否有必需的静态方法
    if (!content.includes('getServerNodeInfo()')) {
      console.warn(`⚠️ ${nodeFile.className}: Missing getServerNodeInfo() method`);
      return false;
    }
    
    if (!content.includes('createServerInstance(')) {
      console.warn(`⚠️ ${nodeFile.className}: Missing createServerInstance() method`);
      return false;
    }
    
    console.log(`✅ ${nodeFile.className}: Validation passed`);
    return true;
    
  } catch (error) {
    console.error(`❌ ${nodeFile.className}: Validation failed:`, error.message);
    return false;
  }
}

/**
 * 更新 server/index.ts 文件
 */
function updateIndexFile(nodeFiles) {
  console.log('📝 Updating server/index.ts file...');

  // 读取当前的 index.ts 文件
  let content = '';
  if (fs.existsSync(SERVER_INDEX_FILE)) {
    content = fs.readFileSync(SERVER_INDEX_FILE, 'utf8');
  }

  // 生成节点文件列表（新的分类格式）
  const nodeFileEntries = nodeFiles.map(node => {
    return `    { category: '${node.category}', path: '${node.relativePath}', file: '${node.className}' }`;
  }).join(',\n');

  // 查找并替换节点文件列表
  const nodeFilesRegex = /const nodeFiles = \[\s*([^}]*?)\s*\];/s;

  if (nodeFilesRegex.test(content)) {
    // 替换现有的节点列表
    content = content.replace(
      nodeFilesRegex,
      `const nodeFiles = [
    // 控制类节点
${nodeFileEntries}
    // 新节点会在这里自动添加（通过构建时脚本）
  ];`
    );

    console.log('✅ Updated existing node list in server/index.ts');
  } else {
    console.warn('⚠️ Could not find node list pattern in server/index.ts');
    console.log('📋 Generated node list:');
    console.log(`const nodeFiles = [
${nodeFileEntries}
  ];`);
  }

  // 写回文件
  try {
    fs.writeFileSync(SERVER_INDEX_FILE, content, 'utf8');
    console.log('✅ server/index.ts file updated successfully');
  } catch (error) {
    console.error('❌ Failed to write server/index.ts file:', error.message);
  }
}

/**
 * 生成节点发现报告
 */
function generateReport(nodeFiles) {
  console.log('\n📊 Node Discovery Report:');
  console.log('========================');
  console.log(`Total nodes found: ${nodeFiles.length}`);
  
  if (nodeFiles.length > 0) {
    console.log('\nDiscovered nodes:');
    nodeFiles.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.className} (${node.fileName})`);
    });
  }
  
  console.log('\n🎯 Next steps:');
  console.log('1. Review the updated index.ts file');
  console.log('2. Run your development server to test the changes');
  console.log('3. Add new nodes by creating Server*Node.ts files');
  console.log('4. Run this script again to auto-discover new nodes');
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 Starting automatic node discovery...\n');
  
  try {
    // 发现节点文件
    const nodeFiles = discoverNodeFiles();
    
    if (nodeFiles.length === 0) {
      console.log('ℹ️ No server nodes found. Create some Server*Node.ts files first.');
      return;
    }
    
    // 验证节点文件
    console.log('\n🔍 Validating node files...');
    const validNodes = nodeFiles.filter(validateNodeFile);
    
    if (validNodes.length === 0) {
      console.log('❌ No valid nodes found. Please check your node implementations.');
      return;
    }
    
    if (validNodes.length < nodeFiles.length) {
      console.log(`⚠️ ${nodeFiles.length - validNodes.length} nodes failed validation`);
    }
    
    // 更新 index.ts 文件
    console.log('\n📝 Updating configuration...');
    updateIndexFile(validNodes);
    
    // 生成报告
    generateReport(validNodes);
    
    console.log('\n🎉 Node discovery completed successfully!');
    
  } catch (error) {
    console.error('❌ Node discovery failed:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = {
  discoverNodeFiles,
  validateNodeFile,
  updateIndexFile,
  generateReport,
  main
};
